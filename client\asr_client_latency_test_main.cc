#include <math.h>
#include <sys/time.h>
#include <cctype>
#include <chrono>
#include <condition_variable>
#include <ctime>
#include <fstream>
#include <functional>
#include <iostream>
#include <mutex>
#include <sstream>
#include <string>
#include <thread>
#include <vector>
#include <sys/wait.h>
#include <unistd.h>


#include "grpc/c_grpc_client.h"
#include "mybase/base.h"
#include "signal/wav_reader.h"
#include "utils/timer.h"
#include "utils/utils.h"
#include "utils/threadPool.h"
#include "client/asr_client_pool.h"
#include "client/asr_client_pool.h"
#include "client/tools_util.h"
#include "client/audio_helper.h"

#include <typeinfo>
#include <cstdlib>

std::string host = "127.0.0.1", wav_path, wav_scp, scene_type = "dianxiao",wav_cache = "./tmp", output_log, test_type;

int audio_sample_rate = 8000, port = 10086, num_samples_to_run = 1,
    num_threads = 1, num_aduio_channels = 1, max_sentence_silence = 600,
    test_num_process=1,test_duration_sencond=10;
float audio_interval = 0.02;
bool do_left_channel = true, do_right_channel = true,
     enable_sent_volume = false, enable_sent_speech_rate = false,
     wav_is_url = false;

using namespace std;
using namespace _360sk;
std::mutex Lock1;
std::mutex Lock2;

size_t latency = 0;
int lines = 0;

void decode(std::vector<float>& pcm_data, std::string session_id,ostream& output_fw) {
  // 此处要调整成获取连接池里的数据
  _360sk::CGrpcClient client(host, port, session_id, max_sentence_silence,
                            enable_sent_volume, enable_sent_speech_rate,
                            scene_type);
  {
    bool connect_state = false;
    // auto start_time = std::chrono::high_resolution_clock::now();
    connect_state = client.Connect();
    // auto end_time = std::chrono::high_resolution_clock::now();
    if (!connect_state) {
      SHUKE_WARN << session_id << " failed to connnect grpc server, and re-connect";
      for (int i = 0; i < 3; i++) {
        connect_state = client.Connect();
	      // end_time = std::chrono::high_resolution_clock::now();
	      SHUKE_WARN << "try reconnect ....";
        if (connect_state) break;
      }
    }
    if (!connect_state) {
      SHUKE_WARN << session_id << " cannot to connnect grpc server";
      return;
    }
    // std::chrono::duration<double, std::milli> connect_duration = end_time - start_time;
    // SHUKE_WARN << "grpc-connect-time: [Start: " << formatTime(start_time) << "] [End: " << formatTime(end_time) << "] " << connect_duration.count() << " ms\n";

  }
  // 20ms时间段的样本数
  const int sample_interval = audio_interval * audio_sample_rate;
  // int num_samples = pcm_data.size() * (rand() / RAND_MAX);
  int num_samples = pcm_data.size();
  for (int start = 0; start < num_samples; start += sample_interval) {
    if (client.done()) {
      break;
    }
    int end = std::min(start + sample_interval, num_samples);
    std::vector<int16_t> data;
    data.reserve(end - start);
    for (int j = start; j < end; j++) {
      data.push_back(static_cast<int16_t>(pcm_data[j]));
    }
    client.SendBinaryData(data.data(), data.size() * sizeof(int16_t));
    //计算的是下一次发送数据前应该等待的时间，毫秒级
    client.ResetTimer((start + sample_interval) / 8);
    //延迟20ms模拟FS请求
    std::this_thread::sleep_for(
        std::chrono::milliseconds(static_cast<int>(audio_interval * 1000)));
  }
  Timer timer;
  client.Join();
  // client.Break();
  SHUKE_LOG << session_id << " request over!";
  std::unique_lock<std::mutex> lck2(Lock2);
  latency += timer.Elapsed();
  output_fw << session_id << " " << client.GetText() << "\n";
  lck2.unlock();
}

void process(int test_duration_sencond, int thread_id, const std::string& test_type, const std::string& wav_scp_name, std::ofstream& output_fw) {
  try {
    SHUKE_LOG << "thread start in process, thread_id: "<< std::to_string(thread_id);
    string line, call_id, wav_path, session_id;
    std::ifstream wav_fp(wav_scp_name); // 每个线程使用自己的文件流对象

    if (!wav_fp.is_open()) {
        SHUKE_LOG << "Failed to open wav_scp_name " << wav_scp_name << endl;
        exit(1);
    }
    bool first_sample_per_thread = true;
    int lines = 0;
    using namespace std::chrono;
    // auto start_time = steady_clock::now();
    // auto end_time = start_time + seconds(test_duration_sencond);
    while (lines < num_samples_to_run) {
      // 单个wav_file 处理
      //std::unique_lock<std::mutex> lck1(Lock1);
      if (!getline(wav_fp, line)) {
        SHUKE_LOG << "wav_scp excute finished,deal nums: " << lines;
        break;
      }
      lines++;
      //lck1.unlock();
      split_line(line, call_id, wav_path);
      if (call_id.empty()){
        call_id = generate_unique_id();
      }
      session_id = std::to_string(thread_id) + "_" + call_id;
      SHUKE_LOG << "session-uuid: " << session_id; 

      if (wav_is_url) {
        std::string wav_url = wav_path;
        wav_path = wav_cache + "/" + call_id + ".wav";
        std::string cmd = "wget  -q " + wav_url + " -O " + wav_path;
        system(cmd.c_str());
      }

      bool last_sample_per_thread = false;
      if(lines > num_samples_to_run - num_threads && lines <= num_samples_to_run){
        last_sample_per_thread = true;
      }

      _360sk::WavReader wav_reader;
      if (wav_reader.Open(wav_path)) {
        const int num_sample = wav_reader.num_sample();
        if (wav_reader.num_channel() == 1) {
          std::vector<float> pcm_data(wav_reader.data(),wav_reader.data() + num_sample);
          if(test_type=="latency"){
            //此处处理4014基准音频,延迟压力测试
            if(first_sample_per_thread){
                // srand(time(nullptr));
                int min = 0, max = 24  * 8000;
                int random_number = rand() % (max - min) + min;
                std::vector<float> pcm_data_before(random_number, 0.0);
                pcm_data_before.insert(pcm_data_before.end(), pcm_data.begin(), pcm_data.end());
                pcm_data = pcm_data_before;
                SHUKE_LOG << session_id <<" nSamples_zeros_added: " << random_number;
            }
            if(last_sample_per_thread){
              SHUKE_LOG << session_id <<" last_sample_per_thread";
            }
          }
          decode(pcm_data, session_id, output_fw);
        } else {
          if (test_type=="latency"){
            // latency test require mono channel
            return; 
          }else{
            // stereo channel
            std::vector<float> pcm_left_data(wav_reader.left_data(),
                                            wav_reader.left_data() + num_sample);
            std::vector<float> pcm_right_data(wav_reader.right_data(),
                                              wav_reader.right_data() + num_sample);
            string left_session_id = session_id + "_left";
            string right_session_id = session_id + "_right";
            if (do_left_channel) {
              decode(pcm_left_data, left_session_id, output_fw);
            }
            if (do_right_channel){
              decode(pcm_right_data, right_session_id, output_fw);
            } 
          }
        }
      } else {
        SHUKE_LOG << "failed to open wavfile " << wav_path;
      }
      if (wav_is_url) {
        std::string cmd = "rm -f " + wav_path;
        system(cmd.c_str());
      }
      
      if(first_sample_per_thread){
        first_sample_per_thread = false;
      }
      // // 此处控制进程永远执行，永不结束
      // if(lines == num_samples_to_run){
      //   lines = 0;
      //   wav_fp.clear();
      //   wav_fp.seekg(0, std::ios::beg);  // 重置到开始
      // }
    }//while
  }catch (const std::exception& e) {
    std::cerr << "Exception: " << e.what() << std::endl;
  }

}

int main(int argc, char* argv[]) {

  const char* usage = "asr grpc client conf";
  ParseOptions po(usage);
  po.Register("host", &host, "");
  po.Register("wav_scp", &wav_scp, "");
  po.Register("output_log", &output_log, "");
  po.Register("scene_type", &scene_type, "");
  po.Register("num_samples_to_run", &num_samples_to_run, "");
  po.Register("audio_sample_rate", &audio_sample_rate, "");
  po.Register("port", &port, "");
  po.Register("wav_path", &wav_path, "");
  po.Register("num_threads", &num_threads, "1");
  po.Register("audio_interval", &audio_interval, "");
  po.Register("num_aduio_channels", &num_aduio_channels, "");
  po.Register("do_left_channel", &do_left_channel, "");
  po.Register("do_right_channel", &do_right_channel, "");
  po.Register("max_sentence_silence", &max_sentence_silence, "");
  po.Register("enable_sent_speech_rate", &enable_sent_speech_rate, "");
  po.Register("enable_sent_volume", &enable_sent_volume, "");
  po.Register("wav_is_url", &wav_is_url, "");
  po.Register("wav_cache", &wav_cache, "");
  //test_duration_sencond 压测时间，单位为秒
  po.Register("test_duration_sencond", &test_duration_sencond, "");
  po.Register("test_num_process", &test_num_process, "");
  // test_type 压测类型：latency:基于基准音频压测
  po.Register("test_type", &test_type, ""); 
  po.Read(argc, argv);

  std::ofstream output_fw(output_log.c_str());
  if (!output_fw) {
    SHUKE_ERR << "Failed to open output_log file." << std::endl;
    return 1;
  }
  int conn_pool_size = num_threads + 10; 
  int conn_keepalive_ms = 30000;
  ThreadPool pool(num_threads);
  std::vector<std::future<void>> results;
  std::cout << "Type of wav_scp: " << typeid(wav_scp).name() << std::endl;
  // 检查文件内容是否存在
  if (is_file_non_empty(wav_scp)) {
    SHUKE_LOG << "wav_scp file has content." << std::endl;
  } else {
    SHUKE_ERR << "wav_scp file is empty or cannot be opened." << std::endl;
    return 1;
  }

  std::unique_ptr<AsrClientPool> asrClientPool(new AsrClientPool(host, port, conn_pool_size, conn_keepalive_ms));
  

  for (int i = 0; i < num_threads; ++i) {
      results.emplace_back(pool.enqueue(process,test_duration_sencond, i, test_type,wav_scp, std::ref(output_fw)));   
  }
  // 等待所有线程完成
  for (auto &&result : results) {
      result.get();
  }
  SHUKE_LOG << "mean of latency is " << float(latency) / num_samples_to_run
            << "ms";
  output_fw.close();
  
  return 0;
}

