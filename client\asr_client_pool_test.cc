#include <iostream>
#include <thread>
#include <chrono>
#include <gtest/gtest.h>
#include "client/asr_client_pool.h"

using namespace _360sk;

std::string host = "127.0.0.1"; 
int port = 10086;
int keepalive_ms = 30000;
int pool_size = 5;

// Test case
// ./client/asr_client_pool_test --gtest_filter=AsrClientPool.Pooling
TEST(AsrClientPool, Pooling)
{
    std::unique_ptr<AsrClientPool> asrClientPool(new AsrClientPool(host, port, pool_size, keepalive_ms));
}

// ./client/asr_client_pool_test --gtest_filter=AsrClientPool.Acquire
TEST(AsrClientPool, Acquire)
{
    std::unique_ptr<AsrClientPool> asrClientPool(new AsrClientPool(host, port, pool_size, keepalive_ms));
    std::shared_ptr<AsrClient> asrClient  = asrClientPool->Acquire();
    std::cout << "is_used:" << asrClient->is_used << std::endl;
}

// ./client/asr_client_pool_test --gtest_filter=AsrClientPool.MCycle
void Cycle(AsrClientPool* asrClientPool) {
    std::shared_ptr<AsrClient> asrClient  = asrClientPool->Acquire();
    if (nullptr != asrClient) {
        std::cout << "is_used:" << asrClient->is_used << ", pool_seq:" << asrClient->pool_seq << ", pool size:" << asrClientPool->client_pool.size() << std::endl;
        std::this_thread::sleep_for(std::chrono::seconds(6));
        asrClientPool->Release(asrClient);
    } else {
        std::cout << "Unallocated resources" << std::endl;
    }
}

TEST(AsrClientPool, MCycle)
{
    AsrClientPool* asrClientPool(new AsrClientPool(host, port, pool_size, keepalive_ms));

    const int num_threads = pool_size;
    std::vector<std::thread> threads;

    for (int i = 0; i < num_threads; ++i) {
        threads.push_back(std::thread(Cycle, asrClientPool));
    }
    for (auto& t : threads) {
        t.join();
    }

    std::cout << "The second run========================="  << std::endl;
    std::vector<std::thread> threads2;
    for (int i = 0; i < num_threads; ++i) {
        threads2.push_back(std::thread(Cycle, asrClientPool));
    }
    for (auto& t : threads2) {
        t.join();
    }
}


int main(int argc, char **argv)
{
    ::testing::InitGoogleTest(&argc, argv);
    return RUN_ALL_TESTS();
}