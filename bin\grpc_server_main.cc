#include <grpcpp/ext/proto_server_reflection_plugin.h>
#include <grpcpp/grpcpp.h>
#include <grpcpp/health_check_service_interface.h>
#include "grpc/grpc_server.h"
#include "mybase/base.h"
#include "frontend/feature_pipeline.h"
#include "vad/vad_config.h"
#include "punctuation/punctuation_config.h"

using grpc::Server;
using grpc::ServerBuilder;

int main(int argc, char* argv[]) {
  SHUKE_LOG << "gRPC version: " << grpc_version_string();
  using namespace _360sk;
  std::string vad_model_path, model_path, cuishou_model_path,
      punctuation_model_path, log_dir,asr_version;
  std::string triton_grpc_server_url("localhost:10082");
  int port = 10086, workers = 4, max_num_request = 500,
      threshold_time_out_latency = 2000, max_size_logfile = 1000,
      sequence_id_loop_level=0;
  bool online_close_request = true, triton_grpc_client_verbose = false;
  const char* usage = "asr server conf";
  ParseOptions po(usage);
  po.Register("workers", &workers, "");
  po.Register("port", &port, "");
  po.Register("log-dir", &log_dir, "log_dir");
  po.Register("max-size-logfile", &max_size_logfile, "default 1000; M");
  po.Register("punctuation-model-path", &punctuation_model_path,
              "asr model path");
  po.Register("max_num_request", &max_num_request, "");
  po.Register("sequence-id-loop-level", &sequence_id_loop_level, "Integer range from 0 to 5");
  po.Register("triton_grpc_server_url", &triton_grpc_server_url, "");
  po.Register("vad-model-path", &vad_model_path, "");
  po.Register("asr-version",&asr_version,"");
  po.Read(argc, argv);

  LogInit(log_dir, max_size_logfile);

  auto feature_config = FeatConf(vad_model_path);
  auto vad_config = TorchVadConf(vad_model_path);
  auto punctuation_config = PunctuationConf(punctuation_model_path);
  GrpcServer service(feature_config, vad_config, punctuation_config, max_num_request,sequence_id_loop_level,
                     triton_grpc_server_url, triton_grpc_client_verbose, asr_version);
  grpc::EnableDefaultHealthCheckService(true);
  grpc::reflection::InitProtoReflectionServerBuilderPlugin();
  ServerBuilder builder;
  std::string address("0.0.0.0:" + std::to_string(port));

  builder.AddListeningPort(address, grpc::InsecureServerCredentials());
  builder.RegisterService(&service);
  builder.SetSyncServerOption(ServerBuilder::SyncServerOption::NUM_CQS,
                              workers);
  std::unique_ptr<Server> server(builder.BuildAndStart());
  SHUKE_LOG << "Listening at port " << port;
  server->Wait();
  LogExit();
  return 0;
}
