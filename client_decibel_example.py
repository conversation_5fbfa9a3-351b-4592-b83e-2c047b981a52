#!/usr/bin/env python3
"""
客户端示例：如何处理基于分贝的推送消息
"""

import grpc
import time
from typing import Iterator

# 假设这是生成的protobuf文件
# from asr_pb2 import Request, Response
# from asr_pb2_grpc import AsrServiceStub

class DecibelAwareAsrClient:
    """支持分贝推送的ASR客户端示例"""
    
    def __init__(self, server_address: str):
        self.server_address = server_address
        self.channel = None
        self.stub = None
        
    def connect(self):
        """连接到ASR服务器"""
        self.channel = grpc.insecure_channel(self.server_address)
        # self.stub = AsrServiceStub(self.channel)
        print(f"Connected to ASR server at {self.server_address}")
        
    def disconnect(self):
        """断开连接"""
        if self.channel:
            self.channel.close()
            print("Disconnected from ASR server")
    
    def handle_response(self, response):
        """处理ASR响应，包括分贝推送"""
        if response.event_type == "PARTIAL_RESULT":
            # 检查是否是基于分贝的空结果推送
            if hasattr(response, 'best_text') and response.best_text.text == "":
                self.on_high_volume_detected(response)
            else:
                self.on_partial_result(response)
        elif response.event_type == "SENTENCE_END":
            # 检查是否是基于分贝的空结果推送
            if hasattr(response, 'best_text') and response.best_text.text == "":
                self.on_high_volume_final(response)
            else:
                self.on_final_result(response)
        else:
            self.on_other_event(response)
    
    def on_high_volume_detected(self, response):
        """处理高音量检测事件（部分结果）"""
        print("🔊 HIGH VOLUME DETECTED (Partial)!")
        print(f"   Session: {response.session}")
        print(f"   Time: {time.strftime('%H:%M:%S')}")
        print(f"   Event Type: {response.event_type}")

        # 在这里可以添加自定义逻辑，例如：
        # 1. 实时提醒用户音量过高
        # 2. 记录高音量事件
        # 3. 触发特殊处理流程
        # 4. 发送通知给监控系统

        self.log_high_volume_event(response, "PARTIAL")

    def on_high_volume_final(self, response):
        """处理高音量检测事件（最终结果）"""
        print("🔊 HIGH VOLUME DETECTED (Final)!")
        print(f"   Session: {response.session}")
        print(f"   Time: {time.strftime('%H:%M:%S')}")
        print(f"   Duration: {response.sentence_end_time - response.sentence_begin_time}ms")

        # 处理最终的高音量事件
        self.log_high_volume_event(response, "FINAL")
    
    def on_partial_result(self, response):
        """处理部分识别结果"""
        if hasattr(response, 'best_text'):
            text = response.best_text.text
            if text and text != "[HIGH_VOLUME_DETECTED]":
                print(f"📝 Partial: {text}")
    
    def on_final_result(self, response):
        """处理最终识别结果"""
        if hasattr(response, 'best_text'):
            text = response.best_text.text
            print(f"✅ Final: {text}")
            print(f"   Duration: {response.sentence_end_time - response.sentence_begin_time}ms")
    
    def on_other_event(self, response):
        """处理其他事件"""
        print(f"ℹ️  Other event: {response.event_type}")
    
    def log_high_volume_event(self, response, event_type="UNKNOWN"):
        """记录高音量事件"""
        timestamp = time.strftime('%Y-%m-%d %H:%M:%S')
        log_entry = f"[{timestamp}] High volume detected ({event_type}) in session {response.session}"

        # 写入日志文件
        with open("high_volume_events.log", "a", encoding="utf-8") as f:
            f.write(log_entry + "\n")

        # 可以添加更多日志记录逻辑
        print(f"📋 Logged: {log_entry}")
    
    def create_audio_stream(self, audio_file_path: str) -> Iterator:
        """创建音频流（示例）"""
        # 这里应该实现实际的音频流读取逻辑
        # 返回Request对象的迭代器
        pass
    
    def start_streaming_recognition(self, audio_file_path: str):
        """开始流式识别"""
        print(f"Starting streaming recognition for: {audio_file_path}")
        
        try:
            # 创建音频流
            audio_stream = self.create_audio_stream(audio_file_path)
            
            # 发送流式请求并处理响应
            # responses = self.stub.StreamingRecognize(audio_stream)
            
            # for response in responses:
            #     self.handle_response(response)
            
            print("Streaming recognition completed")
            
        except grpc.RpcError as e:
            print(f"gRPC error: {e}")
        except Exception as e:
            print(f"Error during streaming recognition: {e}")

def main():
    """主函数示例"""
    # 配置
    server_address = "localhost:50051"
    audio_file = "test_audio.wav"
    
    # 创建客户端
    client = DecibelAwareAsrClient(server_address)
    
    try:
        # 连接服务器
        client.connect()
        
        # 开始识别
        client.start_streaming_recognition(audio_file)
        
    except KeyboardInterrupt:
        print("\nInterrupted by user")
    except Exception as e:
        print(f"Error: {e}")
    finally:
        # 断开连接
        client.disconnect()

if __name__ == "__main__":
    print("=== ASR Client with Decibel Push Support ===")
    print("This example shows how to handle high volume detection events")
    print("along with normal ASR results.\n")
    
    # 注意：这是一个示例代码，需要根据实际的protobuf定义进行调整
    print("Note: This is example code. You need to:")
    print("1. Generate actual protobuf files from asr.proto")
    print("2. Import the generated modules")
    print("3. Implement actual audio streaming logic")
    print("4. Adjust response handling based on your proto definitions\n")
    
    main()
