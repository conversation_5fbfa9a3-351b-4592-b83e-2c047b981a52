#pragma once
#include <queue>
#include <memory>
#include <condition_variable>
#include "client/asr_client.h"

namespace _360sk {
class AsrClientPool final{
public:
    AsrClientPool(const std::string host, int port, int pool_size, int keepalive_ms);

    std::shared_ptr<AsrClient> Acquire();

    bool Release(std::shared_ptr<_360sk::AsrClient> client);

    // pool manage
    std::queue<std::shared_ptr<_360sk::AsrClient> > client_pool;
private:
    // pool manage
    std::mutex pool_mutex_;
    std::condition_variable cond_var_;

    // cell info
    std::string host_;
    int port_;
    int pool_size_;
    int keepalive_ms_;
};
};