#pragma once
#include <vector>
#include <cstdint>
#include <string>

namespace _360sk {
class AudioSender {
public:
    AudioSender(float audio_interval, int audio_sample_rate);
    int setAudioData(const std::string& wav_path);
    const std::vector<std::vector<int16_t>>& getAudioData() const;
    const std::vector<std::vector<int16_t>>& getAudioLeftData() const;
    const std::vector<std::vector<int16_t>>& getAudioRightData() const;
    void clearStoredData();  // 清空 stored_data_ 的方法

private:
    float audio_interval_;
    int audio_sample_rate_;
    std::vector<std::vector<int16_t>> stored_data_;
    std::vector<std::vector<int16_t>> stored_left_data_;
    std::vector<std::vector<int16_t>> stored_right_data_;

    int Transform(std::vector<float>& pcm_data, std::vector<std::vector<int16_t>>& stored_data, int num_sample);
};
}; // namespace _360sk
