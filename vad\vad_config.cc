#include "vad/vad_config.h"

namespace _360sk {

std::shared_ptr<VadConfig> TorchVadConf(std::string model_dir) {
    auto vad_config = std::make_shared<VadConfig>();
  float min_silence_len = 0.03, min_speech_len = 0.10, max_pause_len = -1.0,
        vad_score_threshold = 0.0;
  std::string model_file;
  int num_threads = 1, max_speech_pause_length = 600,
      silence_length_ahead_sent = 400, silence_length_tail_sent = 500;
  bool enable_decibel_push = false;
  int decibel_threshold = 50;

  ParseOptions po("vad config");
  po.Register("max-speech-pause-length", &(vad_config->max_speech_pause_length),
              "default 0.03");
  po.Register("silence-length-ahead-sent", &(vad_config->silence_length_ahead_sent),
              "default 0.03");
  po.Register("silence-length-tail-sent", &(vad_config->silence_length_tail_sent),
              "default 0.03");
  po.Register("min-silence-len", &min_silence_len, "default 0.03");
  po.Register("min-speech-len", &min_speech_len, "default 0.10");
  po.Register("max-pause-len", &max_pause_len, "default -1.0");
  po.Register("vad-score-threshold", &vad_score_threshold, "default 0.0;");
  po.Register("num-threads", &num_threads, "num threads for GEMM");
  po.Register("model-file", &model_file, "pt");
  po.Register("enable-decibel-push", &enable_decibel_push, "enable decibel based push");
  po.Register("decibel-threshold", &decibel_threshold, "decibel threshold for push");
  po.ReadConfigFile(model_dir + "/vad.conf");
  //
  auto vad_model = std::make_shared<TorchVadModel>();
  vad_model->Read(model_dir + "/" + model_file, num_threads);
  //

  vad_config->min_silence_len = size_t(min_silence_len * 100);
  vad_config->min_speech_len = size_t(min_speech_len * 100);
  vad_config->max_pause_len = max_pause_len * 100;
  vad_config->vad_score_threshold = vad_score_threshold;
  vad_config->model = vad_model;
  vad_config->enable_decibel_push = enable_decibel_push;
  vad_config->decibel_threshold = decibel_threshold;

  SHUKE_LOG << "sucess to load vad model, enable_decibel_push: " << enable_decibel_push
            << ", decibel_threshold: " << decibel_threshold;
  return vad_config;
}
}  // namespace _360sk