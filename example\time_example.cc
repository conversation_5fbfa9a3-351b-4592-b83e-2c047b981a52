#include <iostream>
#include <chrono>
#include <iomanip>
#include <sstream>
#include <ctime>

std::string getCurrentTimeAsString() {
    // 获取当前时间点
    auto now = std::chrono::high_resolution_clock::now();
    
    // 转换为time_t以获得时间信息
    auto time = std::chrono::system_clock::to_time_t(now);

    // 获取时间的小数部分
    auto duration = now.time_since_epoch();
    auto millis = std::chrono::duration_cast<std::chrono::milliseconds>(duration).count() % 1000;

    // 格式化时间信息
    std::tm tm = *std::localtime(&time);
    std::ostringstream oss;
    oss << std::put_time(&tm, "%Y-%m-%d %H:%M:%S");
    oss << '.' << std::setfill('0') << std::setw(3) << millis;

    return oss.str();
}

int main() {
    std::string currentTime = getCurrentTimeAsString();
    std::cout << "Current time: " << currentTime << std::endl;
    return 0;
}