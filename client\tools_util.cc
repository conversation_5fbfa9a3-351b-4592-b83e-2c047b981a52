// tools_util.cc
#include "tools_util.h"
#include <fstream>
#include <cctype>
namespace _360sk {
bool contains_wav(const std::string& filename) {
    size_t pos = filename.rfind(".wav");
    if (pos == std::string::npos) return false;
    return ((pos == 0) || (filename[pos - 1] != '.')) &&
           (filename.size() - pos == 4);
}

void split_line(const std::string& line, std::string& key, std::string& val) {
    if (line.empty()){
        key="";
        val="";
        return;
    }

    size_t i = 0;
    while (i < line.size() && !std::isspace(line[i])) ++i;
    key = line.substr(0, i);

    if (i < line.size()) {
        val = line.substr(i + 1);
    } else {
        if (contains_wav(key)) {
            val = key;
            key="";
        }else{
            val="";
        }
    }
}

bool is_file_non_empty(const std::string& filename) {
    if (filename.empty()) {
        return false;
    }
    std::ifstream file(filename);
    if (!file.is_open()) {
        return false;
    }
    std::string line;
    if (std::getline(file, line)) {
        return !line.empty();
    }
    return false;
}
};
