#ifndef UTILS_DECIBEL_ANALYZER_H_
#define UTILS_DECIBEL_ANALYZER_H_

#include <vector>
#include <cmath>
#include <algorithm>

namespace _360sk {

/**
 * @brief 分贝计算工具类
 * 用于计算音频数据的分贝值，支持实时流式计算
 */
class DecibelAnalyzer {
 public:
  DecibelAnalyzer() = default;
  ~DecibelAnalyzer() = default;

  /**
   * @brief 从float类型PCM数据计算分贝值
   * @param pcm_data PCM音频数据指针
   * @param num_samples 样本数量
   * @param reference_level 参考电平，默认为1.0
   * @return 分贝值（整数）
   */
  static int CalculateDecibels(const float* pcm_data, int num_samples, 
                              float reference_level = 1.0f);

  /**
   * @brief 从int16类型PCM数据计算分贝值
   * @param pcm_data PCM音频数据指针
   * @param num_samples 样本数量
   * @param reference_level 参考电平，默认为32768.0（16位最大值）
   * @return 分贝值（整数）
   */
  static int CalculateDecibels(const int16_t* pcm_data, int num_samples,
                              float reference_level = 32768.0f);

  /**
   * @brief 从vector<float>计算分贝值
   * @param pcm_data PCM音频数据vector
   * @param reference_level 参考电平，默认为1.0
   * @return 分贝值（整数）
   */
  static int CalculateDecibels(const std::vector<float>& pcm_data,
                              float reference_level = 1.0f);

  /**
   * @brief 计算音频数据的RMS值
   * @param pcm_data PCM音频数据指针
   * @param num_samples 样本数量
   * @return RMS值
   */
  static float CalculateRMS(const float* pcm_data, int num_samples);

  /**
   * @brief 检查分贝值是否超过阈值
   * @param decibel_value 分贝值
   * @param threshold 阈值
   * @return 是否超过阈值
   */
  static bool IsAboveThreshold(int decibel_value, int threshold);

 private:
  // 禁用拷贝构造和赋值
  DecibelAnalyzer(const DecibelAnalyzer&) = delete;
  DecibelAnalyzer& operator=(const DecibelAnalyzer&) = delete;
};

} // namespace _360sk

#endif // UTILS_DECIBEL_ANALYZER_H_
