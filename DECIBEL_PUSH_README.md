# 基于分贝值的语音推送功能

## 功能概述

本功能在ASR转发系统中添加了基于音频分贝值的推送机制。当音频的分贝值超过设定阈值时，即使没有语音识别结果，系统也会推送一个特殊的通知消息。

## 实现原理

### 分贝计算
- 使用RMS（均方根）方法计算音频数据的分贝值
- 公式：`dB = 20 * log10(rms / reference_level)`
- 支持float和int16两种PCM数据格式

### 推送逻辑
1. 在每个音频数据块到达时计算分贝值
2. 如果分贝值超过配置的阈值，立即推送通知
3. 推送消息包含特殊标识`[HIGH_VOLUME_DETECTED]`

## 配置说明

### 配置文件
在`test/model_deploy/vad_model/vad.conf`中添加以下配置：

```
--enable-decibel-push=true    # 启用分贝推送功能
--decibel-threshold=50        # 分贝阈值（建议值：30-70）
```

### 阈值建议
- **安静环境**: 30-40 dB
- **正常环境**: 45-55 dB  
- **嘈杂环境**: 60-70 dB

## 代码结构

### 新增文件
- `utils/decibel_analyzer.h` - 分贝计算工具类头文件
- `utils/decibel_analyzer.cc` - 分贝计算工具类实现
- `wav_decibel_analyzer.py` - Python版本的分贝计算（用于测试）

### 修改文件
- `grpc/grpc_session_map.h` - 添加分贝相关配置到RequestConf
- `grpc/grpc_handler.h` - 添加分贝检测方法声明
- `grpc/grpc_handler.cc` - 实现分贝检测和推送逻辑
- `vad/vad_config.h` - 添加分贝配置到VadConfig
- `vad/vad_config.cc` - 添加分贝配置解析
- `grpc/grpc_server.cc` - 初始化分贝配置
- `CMakeLists.txt` - 添加新的源文件到编译

## 使用方法

### 1. 编译系统
```bash
cd build
cmake ..
make -j4
```

### 2. 配置参数
编辑`test/model_deploy/vad_model/vad.conf`：
```
--enable-decibel-push=true
--decibel-threshold=50
```

### 3. 启动服务
```bash
cd test
./run.server.sh
```

### 4. 客户端接收
客户端会收到包含`[HIGH_VOLUME_DETECTED]`文本的PARTIAL_RESULT事件。

## API说明

### DecibelAnalyzer类
```cpp
class DecibelAnalyzer {
public:
    // 从float数组计算分贝值
    static int CalculateDecibels(const float* pcm_data, int num_samples, 
                                float reference_level = 1.0f);
    
    // 从int16数组计算分贝值
    static int CalculateDecibels(const int16_t* pcm_data, int num_samples,
                                float reference_level = 32768.0f);
    
    // 从vector计算分贝值
    static int CalculateDecibels(const std::vector<float>& pcm_data,
                                float reference_level = 1.0f);
    
    // 检查是否超过阈值
    static bool IsAboveThreshold(int decibel_value, int threshold);
};
```

### 配置结构
```cpp
struct VadConfig {
    bool enable_decibel_push = false;  // 是否启用分贝推送
    int decibel_threshold = 50;        // 分贝阈值
    // ... 其他配置
};

struct RequestConf {
    bool enable_decibel_push = false;  // 是否启用分贝推送
    int decibel_threshold = 50;        // 分贝阈值
    // ... 其他配置
};
```

## 测试

### Python测试脚本
```bash
python test_decibel_integration.py
```

### C++测试程序
```bash
# 编译后运行
./build/decibel_test
```

## 日志输出

启用分贝推送后，系统会输出以下日志：
```
[INFO] current decibel: 65 threshold: 50
[INFO] DECIBEL_PUSH: decibel=65 threshold=50
```

## 注意事项

1. **性能影响**: 分贝计算会增加少量CPU开销，建议在必要时才启用
2. **阈值调整**: 根据实际环境噪音水平调整阈值
3. **推送频率**: 高分贝时可能频繁推送，客户端需要适当处理
4. **兼容性**: 不影响现有的ASR识别和推送逻辑

## 故障排除

### 常见问题
1. **编译错误**: 确保所有新文件都已添加到CMakeLists.txt
2. **配置不生效**: 检查vad.conf文件格式和路径
3. **推送过于频繁**: 适当提高分贝阈值
4. **推送不及时**: 适当降低分贝阈值

### 调试方法
1. 查看日志中的分贝值输出
2. 使用Python脚本测试分贝计算逻辑
3. 检查配置文件是否正确加载
