#!/usr/bin/env python3
"""
测试分贝计算功能的集成测试脚本
"""

import numpy as np
import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from wav_decibel_analyzer import calculate_decibels_from_pcm, calculate_decibels

def test_decibel_calculation():
    """测试分贝计算功能"""
    print("Testing decibel calculation...")
    
    # 测试1: 静音数据
    silence = np.zeros(1000, dtype=np.float32)
    silence_db = calculate_decibels(silence)
    print(f"Silence decibel: {silence_db} dB")
    
    # 测试2: 低音量数据
    low_volume = 0.1 * np.sin(2 * np.pi * np.arange(1000) / 100.0)
    low_db = calculate_decibels(low_volume)
    print(f"Low volume decibel: {low_db} dB")
    
    # 测试3: 中等音量数据
    medium_volume = 0.5 * np.sin(2 * np.pi * np.arange(1000) / 100.0)
    medium_db = calculate_decibels(medium_volume)
    print(f"Medium volume decibel: {medium_db} dB")
    
    # 测试4: 高音量数据
    high_volume = 1.0 * np.sin(2 * np.pi * np.arange(1000) / 100.0)
    high_db = calculate_decibels(high_volume)
    print(f"High volume decibel: {high_db} dB")
    
    # 测试5: 阈值检查
    threshold = 50
    print(f"\nTesting threshold ({threshold} dB):")
    print(f"Silence above threshold: {silence_db > threshold}")
    print(f"Low volume above threshold: {low_db > threshold}")
    print(f"Medium volume above threshold: {medium_db > threshold}")
    print(f"High volume above threshold: {high_db > threshold}")
    
    return {
        'silence': silence_db,
        'low': low_db,
        'medium': medium_db,
        'high': high_db
    }

def test_pcm_data_simulation():
    """模拟PCM数据的分贝计算"""
    print("\nTesting PCM data simulation...")
    
    # 模拟16位PCM数据
    sample_rate = 8000
    duration = 1.0  # 1秒
    frequency = 440  # A4音符
    
    # 生成不同音量的正弦波
    t = np.linspace(0, duration, int(sample_rate * duration))
    
    volumes = [0.1, 0.3, 0.5, 0.7, 1.0]
    for volume in volumes:
        # 生成正弦波并转换为16位PCM
        wave = volume * np.sin(2 * np.pi * frequency * t)
        pcm_16bit = (wave * 32767).astype(np.int16)
        
        # 转换回float进行分贝计算
        pcm_float = pcm_16bit.astype(np.float32)
        
        db_value = calculate_decibels(pcm_float)
        print(f"Volume {volume:.1f}: {db_value} dB")

def generate_test_scenarios():
    """生成各种测试场景"""
    print("\nGenerating test scenarios for ASR system...")
    
    scenarios = {
        "quiet_room": 0.05,      # 安静房间
        "normal_speech": 0.3,    # 正常说话
        "loud_speech": 0.6,      # 大声说话
        "shouting": 0.9,         # 喊叫
        "background_noise": 0.15 # 背景噪音
    }
    
    results = {}
    for scenario, amplitude in scenarios.items():
        # 生成1秒的音频数据
        samples = 8000
        noise = np.random.normal(0, amplitude * 0.1, samples)  # 添加一些噪音
        signal = amplitude * np.sin(2 * np.pi * np.arange(samples) / 100.0) + noise
        
        db_value = calculate_decibels(signal.astype(np.float32))
        results[scenario] = db_value
        
        print(f"{scenario}: {db_value} dB")
    
    return results

def main():
    """主测试函数"""
    print("=== Decibel Calculation Integration Test ===\n")
    
    try:
        # 基础分贝计算测试
        basic_results = test_decibel_calculation()
        
        # PCM数据模拟测试
        test_pcm_data_simulation()
        
        # 测试场景生成
        scenario_results = generate_test_scenarios()
        
        # 总结
        print("\n=== Test Summary ===")
        print("Basic decibel calculation test: PASSED")
        print("PCM data simulation test: PASSED")
        print("Test scenarios generation: PASSED")
        
        # 推荐阈值设置
        print("\n=== Recommended Threshold Settings ===")
        print("For quiet environments: 30-40 dB")
        print("For normal environments: 45-55 dB")
        print("For noisy environments: 60-70 dB")
        
        # 基于测试结果的建议
        if scenario_results.get('normal_speech', 0) > 0:
            recommended_threshold = max(30, scenario_results['normal_speech'] - 10)
            print(f"Recommended threshold based on test: {recommended_threshold} dB")
        
        return True
        
    except Exception as e:
        print(f"Test failed with error: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
