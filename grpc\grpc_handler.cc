#include "grpc/grpc_handler.h"
// #include "signal/energy.h"
#include "signal/wav_reader.h"
#include "utils/decibel_analyzer.h"
#include "utils/utils.h"

namespace _360sk {
using namespace com::qihoo::shuke::aibot::asr::proto;
using grpc::ServerReaderWriter;

static char string_end_sysmbol = '\0';

GrpcConnectionHandler::GrpcConnectionHandler(
    ServerReaderWriter<Response, Request>* stream,
    std::shared_ptr<Request> request, std::shared_ptr<Response> response,
    std::shared_ptr<GRPCSessionMap> sessionID2InitRequest,
    std::shared_ptr<GRPCSessionIdMap> sessionIdMapping,
    std::string triton_grpc_server_url, bool triton_grpc_client_verbose,
    std::shared_ptr<Fifo<char>> session_id_ptr)
    : stream_(std::move(stream)),
      request_(std::move(request)),
      response_(std::move(response)),
      sessionID2InitRequest_(std::move(sessionID2InitRequest)),
      sessionIdMapping_(std::move(sessionIdMapping)),
      triton_grpc_server_url_(triton_grpc_server_url),
      triton_grpc_client_verbose_(triton_grpc_client_verbose),
      session_id_ptr_(std::move(session_id_ptr)) {
  // time_res_recieved_.resize(12000);
  // time_res_recieved_=std::make_shared<std::vector<int>>(120000, 0);
  // time_res_recieved_->resize(120000);
  // time_ = std::make_shared<Timer>();
  asr_chunk_req_to_res_map_ = std::make_shared<AsrChunkReqToResMap>();
}

bool GrpcConnectionHandler::InvalidStreamRequestCheck() {
  std::string session_id = request_->session();
  if (session_id.empty() || session_id.size() > MAX_LENGTH_SESSION_ID) {
    SHUKE_WARN << "invalid session_id";
    return true;
  }
  std::string asr_session_id = std::string();
  sessionIdMapping_->Find(session_id, asr_session_id);
  if (asr_session_id.empty() || asr_session_id.size() > MAX_LENGTH_SESSION_ID) {
    SHUKE_WARN << "invalid asr_session_id";
    return true;
  }

  if (request_->audio_data().length() <= 0 || request_->audio_data().length() > CHUNK_LENGTH_STREAM_PCM) {
    SHUKE_WARN << asr_session_id << " invalid stream pcm: length=" << request_->audio_data().length();
    return true;
  }

  if (session_start_or_not_) {
    if (sessionID2InitRequest_->Find(asr_session_id, request_conf_)) {  // must be start
      session_id_ = request_->session();
      asr_session_id_ = asr_session_id;
      return false;
    } else {
      if (num_invalid_request_ % 50 == 0) {
        std::string error_message = asr_session_id + " cannot find session_id in session_id_map";
        SHUKE_WARN << error_message;
        OnErrorMessage(static_cast<int>(ERROR_TYPE::kSessionIdOutOfMap), error_message);
      }
      return true;
    }
  } else {
    if (session_id != session_id_) {
      SHUKE_WARN << session_id_ << " not equal " << session_id;
      return true;
    }
  }

  return false;
}

void GrpcConnectionHandler::OnSpeechData() {
  Timer OnSpeechData_timer;
  if (InvalidStreamRequestCheck()) {
    num_invalid_request_++;
    return;
  }
  if (session_start_or_not_) {
    // SHUKE_LOG << asr_session_id_ << " recieve the first valid package";
    SHUKE_LOG << asr_session_id_ << " recognize_stream first valid request";
    SHUKE_LOG << asr_session_id_ << " sequence_id:" << request_conf_->sequence_id;
    {
      for (int i = 0; i < asr_session_id_.size(); i++) {
        session_id_ptr_->Push(asr_session_id_[i]);
      }
      session_id_ptr_->Push(string_end_sysmbol);
    }
    triton_grpc_client_ = std::make_shared<TritonGrpcClient>(
      asr_session_id_, triton_grpc_server_url_, triton_grpc_client_verbose_,
        asr_chunk_req_to_res_map_);
    decode_thread_ = std::make_shared<std::thread>(
        &GrpcConnectionHandler::DecodeThreadFunc, this);
    session_start_or_not_ = false;
  }

  int pcm_len = request_->audio_data().length(),
      num_samples = pcm_len / sizeof(int16_t);
  if (pcm_len <= 0 || num_samples <= 0) {
    SHUKE_WARN << asr_session_id_ << " invalid pcm data";
    return;
  }

  // Read binary PCM data
  const int16_t* pdata =
      reinterpret_cast<const int16_t*>(request_->audio_data().c_str());
  // time_pcm_combing_[num_pcm_combing_] = time_->Elapsed();
  num_pcm_combing_++;
  std::vector<float> pcm_data(num_samples);
  for (int i = 0; i < num_samples; i++) {
    pcm_data[i] = static_cast<float>(pdata[i]);
    // pcm_data[i] = static_cast<float>(pdata[i]) / 32768.0;
  }

  request_conf_->feature_pipeline->AcceptWaveform(pcm_data.data(),
                                                  pcm_data.size());
  {
    std::lock_guard<std::mutex> lock(mutex_);
    accumulation_pcm_data_.insert(accumulation_pcm_data_.end(),
                                  pcm_data.begin(), pcm_data.end());
  }

  // 计算当前音频块的分贝值（仅计算，不推送）
  if (request_conf_->enable_decibel_push) {
    current_decibel_value_ = DecibelAnalyzer::CalculateDecibels(pcm_data);
    SHUKE_LOG << asr_session_id_ << " current decibel: " << current_decibel_value_
              << " threshold: " << request_conf_->decibel_threshold;
  }

  // if (accumulation_pcm_data_.size() % 8000 == 0) {
  //   SHUKE_LOG << asr_session_id_ << " recieved samples "
  //             << int(accumulation_pcm_data_.size() / 8000) << "sec";
  // }
  OnSpeechData_timer_ += OnSpeechData_timer.Elapsed();
}

void GrpcConnectionHandler::OnSpeechEnd() {
  if (asr_session_id_.empty())
    SHUKE_WARN << "no session id in this channel";
  else
    SHUKE_LOG << asr_session_id_ << " signal end";
  if (request_conf_.get() != nullptr) {
    if (request_conf_->online_close_request) signal_end_ = true;
    if (request_conf_->feature_pipeline) {
      request_conf_->feature_pipeline->set_input_finished();
    }
  }
}

void GrpcConnectionHandler::OnErrorMessage(int error_code,
                                           std::string& error_message) {
  response_->mutable_error_message()->set_error_code(error_code);
  response_->mutable_error_message()->set_error_message(error_message);
  stream_->Write(*response_);
}

void GrpcConnectionHandler::OnFinalResult() {
  // 检查是否需要基于分贝值推送（即使没有ASR结果）
  bool should_push_by_decibel = (request_conf_->enable_decibel_push &&
                                DecibelAnalyzer::IsAboveThreshold(current_decibel_value_,
                                                                 request_conf_->decibel_threshold));

  if (parseResult_.context.length() <= 0 && !should_push_by_decibel) {
    parseResult_.Clear();
    return;
  }
  Timer OnFinalResult_timer;
  float sent_volume_mean = 0.0;
  float sent_speech_rate = 0.0;
  response_->set_event_type(EventType::SENTENCE_END);
  response_->set_session(session_id_);

  // 如果有ASR结果，使用ASR结果
  if (parseResult_.context.length() > 0) {
    response_->set_sentence_begin_time(parseResult_.sentence_start);
    response_->set_sentence_end_time(parseResult_.sentence_end);
    response_->mutable_best_text()->set_text(parseResult_.context);
    response_->mutable_best_text()->set_speech_begin(parseResult_.sentence_start);
    response_->mutable_best_text()->set_speech_end(parseResult_.sentence_end);
    response_->mutable_best_text()->set_speech_rate(sent_speech_rate);
    response_->mutable_best_text()->set_volume_mean(sent_volume_mean);

    SHUKE_LOG << "FINALRESULT " << asr_session_id_ << " " << parseResult_.context;
  } else {
    // 没有ASR结果但分贝超过阈值，推送空结果但保持正常的响应格式
    int64_t current_time = GetTimeStamp();
    response_->set_sentence_begin_time(current_time);
    response_->set_sentence_end_time(current_time);
    response_->mutable_best_text()->set_text("");  // 空文本
    response_->mutable_best_text()->set_speech_begin(current_time);
    response_->mutable_best_text()->set_speech_end(current_time);
    response_->mutable_best_text()->set_speech_rate(0.0);
    response_->mutable_best_text()->set_volume_mean(0.0);

    SHUKE_LOG << "FINALRESULT " << asr_session_id_ << " [EMPTY_BY_DECIBEL] decibel="
              << current_decibel_value_ << " threshold=" << request_conf_->decibel_threshold;
  }

  stream_->Write(*response_);
  parseResult_.Clear();
  OnFinalResult_timer_ += OnFinalResult_timer.Elapsed();
}

void GrpcConnectionHandler::OnPartialResult() {
  // SHUKE_LOG << asr_session_id_ << " SENTENCE_BEGIN " <<
  // parseResult_.sentence_start;
  response_->set_event_type(EventType::SENTENCE_BEGIN);
  response_->set_session(session_id_);
  response_->set_sentence_begin_time(parseResult_.sentence_start);
  stream_->Write(*response_);
}

void GrpcConnectionHandler::WordRecognitionResult() {
  response_->set_session(session_id_);
  response_->set_sentence_begin_time(parseResult_.sentence_start);
  response_->set_sentence_end_time(0);
  response_->mutable_best_text()->set_text(parseResult_.context);
  response_->set_event_type(EventType::PARTIAL_RESULT);
  stream_->Write(*response_);
}

void GrpcConnectionHandler::SerializeResult(bool finish) {
  Timer SerializeResult_timer;
  if (asr_endpoint_result_.size() > 0) {
    parseResult_.sentence_start = sentence_begin_time_;
    if (finish) {
      // parseResult_.context = asr_endpoint_result_;
      try {
        parseResult_.context =
            request_conf_->post_processor_->Process(asr_endpoint_result_, true);
      } catch (std::exception const& e) {
        SHUKE_WARN << asr_session_id_ << " " << e.what();
        parseResult_.context = asr_endpoint_result_;
      }
      parseResult_.sentence_end = sentence_end_time_;
    }
  }
  SerializeResult_timer_ += SerializeResult_timer.Elapsed();
}

bool GrpcConnectionHandler::AsrStreamSend(tc::InferOptions& options, int begin,
                                          int data_len, int require_chunk_len) {
  // Initialize the inputs with the data.
  tc::InferInput* input;
  std::vector<int64_t> shape{1, require_chunk_len};

  tc::Error err = tc::InferInput::Create(&input, "WAV", shape, "FP32");
  if (!err.IsOk()) {
    SHUKE_WARN << asr_session_id_ << " unable to create 'WAV'";
    return false;
  }

  std::shared_ptr<tc::InferInput> ivalue(input);
  err = ivalue->Reset();
  if (!err.IsOk()) {
    SHUKE_WARN << asr_session_id_ << " unable to reset 'WAV'";
    return false;
  }

  std::vector<float> chunk_data(require_chunk_len, 0.0);
  if (data_len > 0) {
    std::lock_guard<std::mutex> lock(mutex_);
    if (begin + data_len <= accumulation_pcm_data_.size()) {
      std::memcpy(chunk_data.data(), accumulation_pcm_data_.data() + begin,
                  sizeof(float) * data_len);
    } else {
      SHUKE_WARN
          << asr_session_id_
          << ": data is more than pcm data; accumulation_pcm_data_.size() "
          << accumulation_pcm_data_.size() << " begin:" << begin
          << " data_len:" << data_len
          << " begin + data_len:" << begin + data_len;
      return false;
    }
  }

  err = ivalue->AppendRaw(reinterpret_cast<uint8_t*>(chunk_data.data()),
                          sizeof(float) * require_chunk_len);
  if (!err.IsOk()) {
    SHUKE_WARN << asr_session_id_ << " unable to set data for 'WAV'";
    return false;
  }

  tc::InferInput* input2;
  std::vector<int64_t> shape2{1, 1};

  err = tc::InferInput::Create(&input2, "WAV_LENS", shape2, "INT32");
  if (!err.IsOk()) {
    SHUKE_WARN << asr_session_id_ << " unable to create 'WAV_LENS'";
    return false;
  }

  std::shared_ptr<tc::InferInput> ivalue2(input2);
  err = ivalue2->Reset();
  if (!err.IsOk()) {
    SHUKE_WARN << asr_session_id_ << " unable to reset 'WAV_LENS'";
    return false;
  }

  err = ivalue2->AppendRaw(reinterpret_cast<uint8_t*>(&require_chunk_len),
                           sizeof(int));
  if (!err.IsOk()) {
    SHUKE_WARN << asr_session_id_ << " unable to set data for 'WAV_LENS'";
    return false;
  }
  std::vector<tc::InferInput*> inputs = {ivalue.get(), ivalue2.get()};

  tc::InferRequestedOutput* output;
  // Set the number of classification expected
  int topk = 1;
  err = tc::InferRequestedOutput::Create(&output, "TRANSCRIPTS");
  if (!err.IsOk()) {
    SHUKE_WARN << asr_session_id_ << " unable to get output: " << err;
    return false;
  }
  std::shared_ptr<tc::InferRequestedOutput> output_ptr(output);

  tc::InferRequestedOutput* output2;
  err = tc::InferRequestedOutput::Create(&output2, "ENDPOINT");
  if (!err.IsOk()) {
    SHUKE_WARN << asr_session_id_ << " unable to get output: " << err;
    return false;
  }
  std::shared_ptr<tc::InferRequestedOutput> output_ptr2(output2);

  std::vector<const tc::InferRequestedOutput*> outputs = {output_ptr.get(),
                                                          output_ptr2.get()};
  tc::InferResult* results;
  err = triton_grpc_client_->tg_client_->AsyncStreamInfer(options, inputs,
                                                          outputs);
  if (!err.IsOk()) {
    // reconnect triton server
    int max_retries = 3;
    bool try_succ_flag = false;
    for (int attempt = 0; attempt < max_retries; ++attempt) {
      triton_grpc_client_ = std::make_shared<TritonGrpcClient>(asr_session_id_, triton_grpc_server_url_, triton_grpc_client_verbose_, asr_chunk_req_to_res_map_);
      err = triton_grpc_client_->tg_client_->AsyncStreamInfer(options, inputs,outputs);
      SHUKE_LOG << asr_session_id_ << "triton reconnect succ,try times " << (attempt+1);
      if(err.IsOk()){
        try_succ_flag = true;
        break;
      }
    }
    if(try_succ_flag){
      SHUKE_LOG << asr_session_id_ << " unable to run model,reconnect asyncStreamInfer succ";
      return true;
    }else{
      SHUKE_WARN << asr_session_id_ << " unable to run model";
      return false;
    }
  }
  return true;
}

DecodeState GrpcConnectionHandler::ChunkDecoding(
    bool new_sentence_start_or_not) {
  DecodeState state = DecodeState::kEndBatch;
  int nframes_current_chunk;
  if (new_sentence_start_or_not)
    nframes_current_chunk = request_conf_->nframes_other_chunk;
  else
    nframes_current_chunk = request_conf_->nframes_first_chunk;
  int require_chunk_len = nframes_current_chunk * 80;
  int nframes_silence_ahead_sent = 0;
  int frame_begin_current_sent = nframes_decoded_;
  if (!new_sentence_start_or_not && request_conf_->vad) {
    if (!request_conf_->vad->ReStartChunk(frame_begin_current_sent,
                                          &frame_begin_current_sent,
                                          &nframes_silence_ahead_sent)) {
      state = DecodeState::kEndFeats;
      // SHUKE_LOG << asr_session_id_ << " vad endpoints";
    }
  }

  if (!new_sentence_start_or_not) {
    sentence_begin_time_ = frame_begin_current_sent * 10;  // ms
  }

  SHUKE_LOG << asr_session_id_
            << " nframes_current_chunk: " << nframes_current_chunk
            << " frame_begin_current_sent: " << frame_begin_current_sent
            << " nframes_silence_ahead_sent: " << nframes_silence_ahead_sent;

  if (state == DecodeState::kEndBatch) {
    if (request_conf_->vad) {
      VAD_STATUS vad_state = request_conf_->vad->ChunkUpdate(
          frame_begin_current_sent + nframes_silence_ahead_sent,
          nframes_current_chunk - nframes_silence_ahead_sent,
          &nframes_current_chunk);
      if (vad_state == VAD_STATUS::kEndPoint) {
        state = DecodeState::kEndpoint;
      } else if (vad_state == VAD_STATUS::kEndFeats)
        state = DecodeState::kEndFeats;
    }
    tc::InferOptions asr_options_(streaming_asr_model_name_);
    asr_options_.sequence_id_ = request_conf_->sequence_id;
    asr_options_.sequence_start_ = true;
    if (new_sentence_start_or_not) asr_options_.sequence_start_ = false;
    asr_options_.sequence_end_ = false;
    if (state != DecodeState::kEndBatch) {
      asr_options_.sequence_end_ = true;
    }
    asr_options_.request_id_ = std::to_string(chunk_req_id_);
    SHUKE_LOG << asr_session_id_
              << " frame_begin_current_sent: " << frame_begin_current_sent
              << " nframes_silence_ahead_sent + nframes_current_chunk: "
              << nframes_silence_ahead_sent + nframes_current_chunk;
    SHUKE_LOG << asr_session_id_ << " " << streaming_asr_model_name_
              << " sequence_id_:" << asr_options_.sequence_id_
              << " options.sequence_start_:" << asr_options_.sequence_start_
              << " options.sequence_end_:" << asr_options_.sequence_end_;
    DecodeState decode_state = DecodeState::kEndBatch;
    if (nframes_silence_ahead_sent + nframes_current_chunk > 0) {
      int begin = frame_begin_current_sent * 80,
          chunk_len = (nframes_silence_ahead_sent + nframes_current_chunk) * 80;
      if (!AsrStreamSend(asr_options_, begin, chunk_len, require_chunk_len)) {
        SHUKE_WARN << asr_session_id_ << " AsrStreamSend failed";
      }

      std::pair<std::string, int> res;
      if (asr_chunk_req_to_res_map_->RRFind(chunk_req_id_, res)) {
        asr_endpoint_result_ = res.first;
        int endpoint = res.second;
        SHUKE_LOG << asr_session_id_
                  << " asr_endpoint_result_: " << asr_endpoint_result_
                  << " endpoint:" << endpoint;
        if (endpoint > ctc_endpoint_threshold_) {
          decode_state = DecodeState::kEndpoint;
          if (!asr_options_.sequence_end_) blank_endpoint_ = true;
        } else if ((begin + chunk_len) / 8 - sentence_begin_time_ > 20000) {
          decode_state = DecodeState::kEndpoint;
          blank_endpoint_ = true;
        }
      } else {
        SHUKE_WARN << asr_session_id_
                   << " cannot wait chunk res; chunk_id:" << chunk_req_id_;
      }
    }
    if (state == DecodeState::kEndFeats ||
        decode_state == DecodeState::kEndFeats)
      state = DecodeState::kEndFeats;
    else if (state == DecodeState::kEndpoint ||
             decode_state == DecodeState::kEndpoint)
      state = DecodeState::kEndpoint;
    nframes_decoded_ = frame_begin_current_sent + nframes_silence_ahead_sent +
                       nframes_current_chunk;
    if (state != DecodeState::kEndBatch) {
      sentence_end_time_ =
          (frame_begin_current_sent + nframes_silence_ahead_sent +
           nframes_current_chunk) *
          10;  // ms
    }
  }  // state == DecodeState::kEndBatch
  return state;
}

void GrpcConnectionHandler::DecodeThreadFunc() {
  bool new_sentence_start_or_not = false;
  bool new_sentence_speeching_or_not = false;
  // while (true && !signal_end_) {
  while (true) {
    DecodeState state = ChunkDecoding(new_sentence_start_or_not);
    chunk_req_id_++;
    response_->clear_event_type();
    response_->clear_best_text();
    if (state == DecodeState::kEndFeats) {
      SerializeResult(true);
      OnFinalResult();
      break;
    } else if (state == DecodeState::kEndpoint) {
      SerializeResult(true);
      OnFinalResult();
      if (blank_endpoint_) {
        tc::InferOptions asr_options_(streaming_asr_model_name_);
        asr_options_.sequence_id_ = request_conf_->sequence_id;
        asr_options_.sequence_start_ = false;
        asr_options_.sequence_end_ = true;
        asr_options_.request_id_ = std::to_string(chunk_req_id_);
        int require_chunk_len = request_conf_->nframes_other_chunk * 80;
        SHUKE_LOG << asr_session_id_ << " " << streaming_asr_model_name_
                  << " sequence_id_:" << asr_options_.sequence_id_
                  << " options.sequence_start_:" << asr_options_.sequence_start_
                  << " options.sequence_end_:" << asr_options_.sequence_end_;
        if (AsrStreamSend(
                asr_options_, 0, 0,
                require_chunk_len)) {  // 目的是等待，以防与下一个开始请求到达服务时间颠倒，导致服务端异常
          std::pair<std::string, int> res;
          if (asr_chunk_req_to_res_map_->RRFind(chunk_req_id_, res)) {
            std::string res_tmp = res.first;
            int endpoint = res.second;
            SHUKE_LOG << asr_session_id_ << " asr_endpoint_result_: " << res_tmp
                      << " endpoint:" << endpoint;
          }
          // else {
          //   SHUKE_WARN << asr_session_id_
          //             << " cannot wait chunk res; chunk_id:" <<
          //             chunk_req_id_;
          // }
        } else {
          SHUKE_WARN << asr_session_id_ << " AsrStreamSend failed";
        }
        chunk_req_id_++;
        blank_endpoint_ = false;
      }
      new_sentence_start_or_not = false;
      new_sentence_speeching_or_not = false;
      asr_endpoint_result_.clear();
    } else {
      if (!new_sentence_start_or_not) new_sentence_start_or_not = true;
      if (asr_endpoint_result_.size() > 0) {
        if (!new_sentence_speeching_or_not) {
          new_sentence_speeching_or_not = true;
          SerializeResult(false);
          OnPartialResult();
        }
        if (request_conf_->word_recognition) {
             SerializeResult(true);
             WordRecognitionResult();
          }
      }
    }
  }  // while

}  // DecodeThreadFunc



void GrpcConnectionHandler::operator()() {
  try {
    while (stream_->Read(request_.get())) {
      OnSpeechData();
      //   std::this_thread::sleep_for(
      // std::chrono::milliseconds(static_cast<int>(1)));
    }
    OnSpeechEnd();
    if (decode_thread_) {
      decode_thread_->join();
    }
    if (false) {
      // SHUKE_LOG << "session " << asr_session_id_
      //           << " num_samples of audio: " <<
      //           accumulation_pcm_data_->Length();
      WavWriter ww(accumulation_pcm_data_.data(), accumulation_pcm_data_.size(),
                   1, 8000, 16);
      std::string filename = "/home/<USER>/tmp/" + asr_session_id_ + ".wav";
      ww.Write(filename);
    }
  } catch (std::exception const& e) {
    SHUKE_ERR << e.what();
  }
}

}  // namespace _360sk
