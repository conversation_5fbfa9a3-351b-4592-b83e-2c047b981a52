// tools_util_test.cpp
#include <gtest/gtest.h>
#include "tools_util.h"
#include <fstream>
namespace _360sk {
// 测试 contains_wav 函数
TEST(ToolsUtilTest, ContainsWav) {
    EXPECT_TRUE(contains_wav("example.wav"));
    EXPECT_FALSE(contains_wav("example.mp3"));
    EXPECT_FALSE(contains_wav("example.wav.txt"));
    EXPECT_FALSE(contains_wav("examplewav"));
}

// 测试 split_line 函数
TEST(ToolsUtilTest, SplitLine) {
    std::string key, val;

    split_line("key value", key, val);
    EXPECT_EQ(key, "key");
    EXPECT_EQ(val, "value");

    split_line("key", key, val);
    EXPECT_EQ(key, "key");
    EXPECT_EQ(val, "");

    split_line("", key, val);
    EXPECT_EQ(key, "");
    EXPECT_EQ(val, "");

    split_line("example.wav", key, val);
    EXPECT_EQ(key, "");
    EXPECT_EQ(val, "example.wav");
}

// 测试 is_file_non_empty 函数
TEST(ToolsUtilTest, IsFileNonEmpty) {
    // Create a temporary file with content
    std::string filename = "test_file.txt";
    std::ofstream out(filename);
    out << "This is a test.";
    out.close();

    EXPECT_TRUE(is_file_non_empty(filename));

    // Create a temporary empty file
    std::string empty_filename = "empty_file.txt";
    std::ofstream empty_file(empty_filename);
    empty_file.close();

    EXPECT_FALSE(is_file_non_empty(empty_filename));

    // Create a temporary file that does not exist
    std::string non_existent_filename = "non_existent_file.txt";
    EXPECT_FALSE(is_file_non_empty(non_existent_filename));

    // Clean up files
    remove(filename.c_str());
    remove(empty_filename.c_str());
}

// Main function for gtest
int main(int argc, char **argv) {
    ::testing::InitGoogleTest(&argc, argv);
    return RUN_ALL_TESTS();
}
};
