#pragma once

#include <iostream>
#include <fstream>
#include <sstream>
#include <string>
#include <map>

namespace _360sk {
class ConfigParser {
public:
    ConfigParser();
    bool load(const std::string filename) ;
    std::string get(const std::string key, const std::string default_value);
private:
    std::map<std::string, std::string> config_data;
    std::string trim(const std::string str);
};
};