#include "asr_client.h"

#include <iostream>

using grpc::Channel;
using grpc::ClientContext;
using grpc::ClientReaderWriter;
using grpc::Status;
namespace _360sk {

AsrClient::AsrClient(std::string host, int port) : host_(host), port_(port) {}
AsrClient::AsrClient(std::string host, int port, int keepalive_ms, int concurrency)
    : host_(host), port_(port), keepalive_ms_(keepalive_ms), concurrency_(concurrency), session_id_("") {}

std::string ConnectivityStateToString(grpc_connectivity_state state) {
    switch (state) {
        case GRPC_CHANNEL_IDLE:
            return "IDLE";
        case GRPC_CHANNEL_CONNECTING:
            return "CONNECTING";
        case GRPC_CHANNEL_READY:
            return "READY";
        case GRPC_CHANNEL_TRANSIENT_FAILURE:
            return "TRANSIENT FAILURE";
        case GRPC_CHANNEL_SHUTDOWN:
            return "SHUTDOWN";
        default:
            return "UNKNOWN";
    }
}

int AsrClient::Connect() {
    // channel_ = grpc::CreateChannel(host_ + ":" + std::to_string(port_),
    // grpc::InsecureChannelCredentials());

    // 自定义通道参数
    grpc::ChannelArguments channel_args;
    channel_args.SetInt(GRPC_ARG_KEEPALIVE_TIME_MS,
                        keepalive_ms_);  // 设置保活选项
    // channel_args.SetInt(GRPC_ARG_KEEPALIVE_TIMEOUT_MS,
                        // keepalive_ms_ * 2);  // X秒未收到响应则关闭连接
    // channel_args.SetInt(GRPC_ARG_KEEPALIVE_PERMIT_WITHOUT_CALLS, 1);  // 即使没有 RPC 调用也发送 Keepalive
    // SHUKE_LOG << "keepalive_ms_:" << keepalive_ms_;
    // return 1;
    // channel_args.SetInt(GRPC_ARG_INITIAL_RECONNECT_BACKOFF_MS, 1000); //
    // 初始连接超时时间 channel_args.SetMaxReceiveMessageSize(-1);  //
    // 设置为不限制接收消息的大小 channel_args.SetMaxSendMessageSize(-1);     //
    // 设置为不限制发送消息的大小
    // channel_args.SetLoadBalancingPolicyName("round_robin");  //
    // 轮询负载均衡策略

    // 使用 InsecureChannelCredentials 不加密
    channel_ = grpc::CreateCustomChannel(host_ + ":" + std::to_string(port_),
                                         grpc::InsecureChannelCredentials(),
                                         channel_args);

    if (nullptr == channel_) {
        SHUKE_WARN << "channel create failure!";
        return 1;
    }

    grpc_connectivity_state state = channel_->GetState(false);
    SHUKE_LOG << "Initial channel state: " << ConnectivityStateToString(state);
    for (int i = 1; i < 4; i++) {
        if (state != GRPC_CHANNEL_READY) {
            // reconnect
            state = channel_->GetState(true);
            SHUKE_LOG << "Current channel state: "
                      << ConnectivityStateToString(state) << ". retry cnt:" << i;
            if (state == GRPC_CHANNEL_READY) {
                return 0;
            }
            // Wait for the connection status to change
            channel_->WaitForStateChange(state,
                                         std::chrono::system_clock::now() +
                                             std::chrono::milliseconds(100));
        }
    }
    SHUKE_WARN << "Connect failure!host:" << host_ << ", port:" << port_;
    return 2;
}

int AsrClient::Initialize(const std::string &session_id,
                          const std::string &scene_type,
                          int max_sentence_silence, bool enable_sent_volume,
                          bool enable_sent_speech_rate) {
    if (!IsConnected()) {
        return 1;
    }
    if (session_id.empty()) {
        return 2;
    }
    session_id_ = session_id;

    stub_ = SpeechRecognizer::NewStub(channel_);
    ClientContext init_context;
    InitializeRequest init_request;
    init_request.set_session(session_id_);
    if (scene_type == "cuishou") {
        init_request.mutable_session_request()
            ->mutable_secene_param()
            ->set_secene(SeceneType::CUISHOU);
    } else {
        init_request.mutable_session_request()
            ->mutable_secene_param()
            ->set_secene(SeceneType::DIANXIAO);
    }

    init_request.mutable_session_request()
        ->mutable_asr_service_param()
        ->set_max_sentence_silence(max_sentence_silence);
    init_request.mutable_session_request()
        ->mutable_asr_service_param()
        ->set_enable_sent_volume(enable_sent_volume);
    init_request.mutable_session_request()
        ->mutable_asr_service_param()
        ->set_enable_sent_speech_rate(enable_sent_speech_rate);

    InitializeResponse init_response;
    Status status =
        stub_->initialize(&init_context, init_request, &init_response);
    if (status.ok()) {
        SHUKE_LOG << "success initialize, " << init_response.session();
        if (init_response.error_message().error_code() != 0) {
            SHUKE_LOG << "Initialize() fail!session id: " << session_id_
                      << "\terror code: "
                      << init_response.error_message().error_code()
                      << "\terror message: "
                      << init_response.error_message().error_message();
            return 3;
        }
        return 0;
    } else {
        SHUKE_WARN << "Initialize() failure! session id: " << session_id_ << " failed initialize" 
                   << "\terror_code: " << status.error_code()
                  << "\terror message: " << status.error_message();
    }
    return 4;
}

int AsrClient::SendData(const void *data, size_t size) {
    const int16_t *pdata = reinterpret_cast<const int16_t *>(data);
    if (request_) {
        request_->set_session(session_id_);
        request_->set_audio_data(pdata, size);
    }
    if (stream_) stream_->Write(*request_);
    // std::cout << "stream write ..." << std::endl;
    // SHUKE_LOG << "stream_ Write...!session_id:" << session_id_;
    return 0;
}

bool AsrClient::IsConnected() {
    grpc_connectivity_state state = channel_->GetState(false);
    return state == GRPC_CHANNEL_READY;
    // return false;
}

int AsrClient::SuspendedResponse() {
    if (!IsConnected()) {
        std::cout << "connect suspended!" << std::endl;
        return 1;
    }

    context_ = std::make_shared<ClientContext>();
    //   std::cout << "ClientContext Instance Suc" << std::endl;
    stream_ = stub_->recognize_stream(context_.get());
    //   std::cout << "stub_ recognize_stream begin" << std::endl;
    request_ = std::make_shared<Request>();
    response_ = std::make_shared<Response>();

    request_->set_session(session_id_);
    stream_->Write(*request_);
    if (stream_) {
        // std::cout << "stub_ recognize_stream suc" << std::endl;
        t_.reset(new std::thread(&AsrClient::ServerCallback, this));
        SHUKE_LOG << "stub_ recognize_stream reset";
        return 0;
    }
    return 2;
}

int AsrClient::ServerCallback() {
    try {
        // SHUKE_LOG << "listen repsonse stream begin====================";
        /*if (!stream_ || !response_) {
            std::cout << "Stream or response is null!" << std::endl;
            return 1;
        }*/

        // stream is closed()
        /*if (stream_->Finish().ok()) {
            std::cout << "Stream finished successfully." << std::endl;
        } else {
            std::cout << "Stream finished with errors." << std::endl;
            return 2;
        }*/

        // read
        // std::string sentence;
        Timer timer;
        // std::cout << "listen repsonse stream======" << std::endl;
        while (stream_->Read(response_.get())) {
            // std::cout << "stream has read ======" << std::endl;
            if (response_->error_message().error_code() == 0) {
                if (response_->event_type() == EventType::SENTENCE_BEGIN) {
                    SHUKE_LOG << "session_id: " << response_->session()
                              << "\tspeech begin time: "
                              << response_->sentence_begin_time() / 1000.0
                              << "s" << std::endl;
                } else if (response_->event_type() == EventType::SENTENCE_END) {
                    if (!response_->best_text().text().empty()) {
                        // sentence += response_->best_text().text() + " ";
                        SHUKE_LOG
                            << "[sentence_end]"
                            << "session: " << response_->session()
                            << "\tasr result: " << response_->best_text().text()
                            << "\tvolume: "
                            << response_->best_text().volume_mean()
                            << "\tspeech rate: "
                            << response_->best_text().speech_rate()
                            << "\tbegin time: "
                            << response_->sentence_begin_time() / 1000.0 << "s"
                            << "\tend time: "
                            << response_->sentence_end_time() / 1000.0 << "s"
                            << "\tconcurrency: "
                            << std::to_string(concurrency_)
                            << "\tbiz_elapse_time: "
                            <<  std::to_string(bz_timer_.Elapsed() / 1000.0)<< "s"
                            << "\tsentence_elapse_time: "
                            << std::to_string(timer.Elapsed() / 1000.0) << "s";
                    }
                    timer.Reset();
                    // sentence = "";
                }
            } else {
                SHUKE_WARN << "[RES_ERR]" << response_->session() << "\terror_code: "
                          << response_->error_message().error_code()
                          << "\terror_message: "
                          << response_->error_message().error_message();
            }
        }
    } catch (std::exception const &e) {
        SHUKE_WARN << e.what() << std::endl;
    } catch (...) {
        SHUKE_WARN << "Unknown exception during stream read." << std::endl;
    }
}

bool AsrClient::Test() {
    std::cout << "host = " << host_ << ", port = " << port_ << std::endl;
    return true;
}

void AsrClient::Join() {
    if (stream_ && t_) {
        stream_->WritesDone();
        t_->join();
        Status status = stream_->Finish();
        if (!status.ok()) {
            SHUKE_WARN << session_id_ << " recognize rpc failed.";
        }
    }
    // Ensure that the stream is properly reset or destroyed after usage
    stream_.reset();
}

AsrClient::~AsrClient() {}

void AsrClient::Break() {
    if (stream_ && t_) {
        context_->TryCancel();
        t_.release();
    }
}

void AsrClient::ResetStartTime(int time) { bz_timer_.Reset(time); }

};  // namespace _360sk