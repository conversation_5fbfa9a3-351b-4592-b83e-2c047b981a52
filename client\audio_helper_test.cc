#include "gtest/gtest.h"
#include "audio_helper.h"

namespace _360sk {

class AudioSenderTest : public ::testing::Test {
protected:
    AudioSenderTest() : audio_sender_(0.02, 8000) {}  // 初始化 AudioSender 对象

    AudioSender audio_sender_;
};

// 测试 setAudioData 方法
TEST_F(AudioSenderTest, SetAudioData) {
    std::string wav_path = "/data/oceanus_ctr/j-liuchao6-jk/workspace/asr-forwarding/test/data/4014.wav";  // 假设存在一个 test.wav 文件
    audio_sender_.setAudioData(wav_path);
    
    // 检查 stored_data_ 是否存储了音频数据块
    const auto& stored_data = audio_sender_.getAudioData();
    for (const auto& chunk : stored_data) {
        std::cout << "Chunk size: " << chunk.size() << std::endl;
        for (int16_t sample : chunk) {
            std::cout << sample << " ";
        }
        std::cout << std::endl;

        // Example of sending data to an ASR client (assuming you have an asrClient object)
        // asrClient->SendData(chunk.data(), chunk.size());
    }
    EXPECT_FALSE(stored_data.empty());
}

// 测试 clearStoredData 方法
TEST_F(AudioSenderTest, ClearStoredData) {
    std::string wav_path = "/data/oceanus_ctr/j-liuchao6-jk/workspace/asr-forwarding/test/data/4014.wav";  // 假设存在一个 test.wav 文件
    audio_sender_.setAudioData(wav_path);
    audio_sender_.clearStoredData();
    
    // 检查 stored_data_ 是否已经被清空
    const auto& stored_data = audio_sender_.getAudioData();
    EXPECT_TRUE(stored_data.empty());
}

// 测试 getAudioData 方法
TEST_F(AudioSenderTest, GetAudioData) {
    std::string wav_path = "/data/oceanus_ctr/j-liuchao6-jk/workspace/asr-forwarding/test/data/4014.wav";  // 假设存在一个 test.wav 文件
    audio_sender_.setAudioData(wav_path);
    
    // 检查 getAudioData 方法是否返回正确的数据
    const auto& stored_data = audio_sender_.getAudioData();
    EXPECT_GT(stored_data.size(), 0);
    EXPECT_GT(stored_data[0].size(), 0);
}

}  // namespace _360sk
