#include <iostream>
#include "vad/vad_config.h"

int main() {
    try {
        // 测试VAD配置加载
        std::string vad_model_dir = "test/model_deploy/vad_model";
        auto vad_config = _360sk::TorchVadConf(vad_model_dir);
        
        std::cout << "=== VAD Configuration Test ===" << std::endl;
        std::cout << "enable_decibel_push: " << (vad_config->enable_decibel_push ? "true" : "false") << std::endl;
        std::cout << "decibel_threshold: " << vad_config->decibel_threshold << std::endl;
        std::cout << "min_silence_len: " << vad_config->min_silence_len << std::endl;
        std::cout << "min_speech_len: " << vad_config->min_speech_len << std::endl;
        std::cout << "vad_score_threshold: " << vad_config->vad_score_threshold << std::endl;
        std::cout << "max_pause_len: " << vad_config->max_pause_len << std::endl;
        std::cout << "max_speech_pause_length: " << vad_config->max_speech_pause_length << std::endl;
        std::cout << "silence_length_ahead_sent: " << vad_config->silence_length_ahead_sent << std::endl;
        std::cout << "silence_length_tail_sent: " << vad_config->silence_length_tail_sent << std::endl;
        
        std::cout << "\n=== Test Result ===" << std::endl;
        if (vad_config->enable_decibel_push && vad_config->decibel_threshold == 50) {
            std::cout << "✅ Decibel configuration loaded successfully!" << std::endl;
        } else {
            std::cout << "❌ Decibel configuration not loaded correctly!" << std::endl;
        }
        
    } catch (const std::exception& e) {
        std::cerr << "Error: " << e.what() << std::endl;
        return 1;
    }
    
    return 0;
}
