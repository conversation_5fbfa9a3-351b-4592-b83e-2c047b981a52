#include <iostream>
#include <vector>
#include <cmath>
#include "utils/decibel_analyzer.h"

using namespace _360sk;

void test_decibel_calculation() {
    std::cout << "Testing decibel calculation..." << std::endl;
    
    // 测试1: 静音数据
    std::vector<float> silence(1000, 0.0f);
    int silence_db = DecibelAnalyzer::CalculateDecibels(silence);
    std::cout << "Silence decibel: " << silence_db << " dB" << std::endl;
    
    // 测试2: 低音量数据
    std::vector<float> low_volume(1000);
    for (int i = 0; i < 1000; ++i) {
        low_volume[i] = 0.1f * std::sin(2.0f * M_PI * i / 100.0f);
    }
    int low_db = DecibelAnalyzer::CalculateDecibels(low_volume);
    std::cout << "Low volume decibel: " << low_db << " dB" << std::endl;
    
    // 测试3: 中等音量数据
    std::vector<float> medium_volume(1000);
    for (int i = 0; i < 1000; ++i) {
        medium_volume[i] = 0.5f * std::sin(2.0f * M_PI * i / 100.0f);
    }
    int medium_db = DecibelAnalyzer::CalculateDecibels(medium_volume);
    std::cout << "Medium volume decibel: " << medium_db << " dB" << std::endl;
    
    // 测试4: 高音量数据
    std::vector<float> high_volume(1000);
    for (int i = 0; i < 1000; ++i) {
        high_volume[i] = 1.0f * std::sin(2.0f * M_PI * i / 100.0f);
    }
    int high_db = DecibelAnalyzer::CalculateDecibels(high_volume);
    std::cout << "High volume decibel: " << high_db << " dB" << std::endl;
    
    // 测试5: 阈值检查
    int threshold = 50;
    std::cout << "\nTesting threshold (" << threshold << " dB):" << std::endl;
    std::cout << "Silence above threshold: " << DecibelAnalyzer::IsAboveThreshold(silence_db, threshold) << std::endl;
    std::cout << "Low volume above threshold: " << DecibelAnalyzer::IsAboveThreshold(low_db, threshold) << std::endl;
    std::cout << "Medium volume above threshold: " << DecibelAnalyzer::IsAboveThreshold(medium_db, threshold) << std::endl;
    std::cout << "High volume above threshold: " << DecibelAnalyzer::IsAboveThreshold(high_db, threshold) << std::endl;
}

void test_int16_decibel_calculation() {
    std::cout << "\nTesting int16 decibel calculation..." << std::endl;
    
    // 测试int16数据
    std::vector<int16_t> pcm_data(1000);
    for (int i = 0; i < 1000; ++i) {
        pcm_data[i] = static_cast<int16_t>(16384 * std::sin(2.0f * M_PI * i / 100.0f));
    }
    
    int pcm_db = DecibelAnalyzer::CalculateDecibels(pcm_data.data(), pcm_data.size());
    std::cout << "Int16 PCM decibel: " << pcm_db << " dB" << std::endl;
}

int main() {
    try {
        test_decibel_calculation();
        test_int16_decibel_calculation();
        std::cout << "\nAll tests completed successfully!" << std::endl;
    } catch (const std::exception& e) {
        std::cerr << "Test failed with exception: " << e.what() << std::endl;
        return 1;
    }
    
    return 0;
}
