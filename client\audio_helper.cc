#include "signal/wav_reader.h"  // 假设 wav_reader 是一个用于读取 WAV 文件的类
#include "audio_helper.h"

#include <algorithm>
#include <iostream>

namespace _360sk {

AudioSender::AudioSender(float audio_interval, int audio_sample_rate)
    : audio_interval_(audio_interval), audio_sample_rate_(audio_sample_rate) {}

int AudioSender::setAudioData(const std::string& wav_path) {
    WavReader wav_reader;  // 使用默认构造函数
    if (!wav_reader.Open(wav_path)) {
        return 1;
    }

    const int num_sample = wav_reader.num_sample();

    if (wav_reader.num_channel() == 1) {
        if (num_sample <= 0) {
            std::cout << "Invalid number of samples in WAV file: " << wav_path
                      << std::endl;
            return 2;
        }

        const float* data_ptr = wav_reader.data();
        if (data_ptr == nullptr) {
            std::cout << "WAV data pointer is null for file: " << wav_path
                      << std::endl;
            return 3;
        }
        std::vector<float> pcm_data(wav_reader.data(),
                                    wav_reader.data() + num_sample);
        Transform(pcm_data, stored_data_, num_sample);

    } else {
        std::vector<float> pcm_left_data(wav_reader.left_data(),
                                         wav_reader.left_data() + num_sample);
        Transform(pcm_left_data, stored_left_data_, num_sample);

        std::vector<float> pcm_right_data(wav_reader.right_data(),
                                          wav_reader.right_data() + num_sample);
        Transform(pcm_right_data, stored_right_data_, num_sample);
    }
    return 0;
}

int AudioSender::Transform(std::vector<float>& pcm_data,
                           std::vector<std::vector<int16_t>>& stored_data,
                           int num_sample) {
    const int sample_interval =
        static_cast<int>(audio_interval_ * audio_sample_rate_);
    const int num_samples = pcm_data.size();
    for (int start = 0; start < num_samples; start += sample_interval) {
        int end = std::min(start + sample_interval, num_samples);
        std::vector<int16_t> data;
        data.reserve(end - start);

        for (int j = start; j < end; j++) {
            data.push_back(static_cast<int16_t>(pcm_data[j]));
        }

        // 将切割后的数据存储到 stored_data_ 中
        stored_data.push_back(std::move(data));
    }

    return 0;
}

const std::vector<std::vector<int16_t>>& AudioSender::getAudioData() const {
    return stored_data_;
}

const std::vector<std::vector<int16_t>>& AudioSender::getAudioLeftData() const {
    return stored_left_data_;
}

const std::vector<std::vector<int16_t>>& AudioSender::getAudioRightData()
    const {
    return stored_right_data_;
}

void AudioSender::clearStoredData() {
    stored_data_.clear();
    stored_left_data_.clear();
    stored_right_data_.clear();
}

}  // namespace _360sk
