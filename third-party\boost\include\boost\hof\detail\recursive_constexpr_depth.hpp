/*=============================================================================
    Copyright (c) 2016 <PERSON> II
    recursive_constexpr_depth.hpp
    Distributed under the Boost Software License, Version 1.0. (See accompanying
    file LICENSE_1_0.txt or copy at http://www.boost.org/LICENSE_1_0.txt)
==============================================================================*/

#ifndef BOOST_HOF_GUARD_RECURSIVE_CONSTEXPR_DEPTH_HPP
#define BOOST_HOF_GUARD_RECURSIVE_CONSTEXPR_DEPTH_HPP

#ifndef BOOST_HOF_RECURSIVE_CONSTEXPR_DEPTH
#define BOOST_HOF_RECURSIVE_CONSTEXPR_DEPTH 16
#endif

namespace boost { namespace hof {

}} // namespace boost::hof

#endif
