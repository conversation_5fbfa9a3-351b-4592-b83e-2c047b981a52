#include "utils/decibel_analyzer.h"
#include "mybase/log.h"
#include <cmath>
#include <algorithm>

namespace _360sk {

int DecibelAnalyzer::CalculateDecibels(const float* pcm_data, int num_samples, 
                                      float reference_level) {
  if (pcm_data == nullptr || num_samples <= 0) {
    return 0;
  }

  // 计算RMS（均方根）
  float rms = CalculateRMS(pcm_data, num_samples);
  
  // 避免除以零或负数
  if (rms <= 0.0f || reference_level <= 0.0f) {
    return 0;
  }

  // 计算分贝值: dB = 20 * log10(rms / reference_level)
  float decibels = 20.0f * std::log10(rms / reference_level);
  
  // 转换为整数，确保非负值
  return std::max(0, static_cast<int>(std::round(decibels)));
}

int DecibelAnalyzer::CalculateDecibels(const int16_t* pcm_data, int num_samples,
                                      float reference_level) {
  if (pcm_data == nullptr || num_samples <= 0) {
    return 0;
  }

  // 转换为float并计算RMS
  double sum_squares = 0.0;
  for (int i = 0; i < num_samples; ++i) {
    float sample = static_cast<float>(pcm_data[i]);
    sum_squares += sample * sample;
  }
  
  float rms = std::sqrt(sum_squares / num_samples);
  
  // 避免除以零或负数
  if (rms <= 0.0f || reference_level <= 0.0f) {
    return 0;
  }

  // 计算分贝值
  float decibels = 20.0f * std::log10(rms / reference_level);
  
  // 转换为整数，确保非负值
  return std::max(0, static_cast<int>(std::round(decibels)));
}

int DecibelAnalyzer::CalculateDecibels(const std::vector<float>& pcm_data,
                                      float reference_level) {
  if (pcm_data.empty()) {
    return 0;
  }
  
  return CalculateDecibels(pcm_data.data(), static_cast<int>(pcm_data.size()), 
                          reference_level);
}

float DecibelAnalyzer::CalculateRMS(const float* pcm_data, int num_samples) {
  if (pcm_data == nullptr || num_samples <= 0) {
    return 0.0f;
  }

  double sum_squares = 0.0;
  for (int i = 0; i < num_samples; ++i) {
    sum_squares += pcm_data[i] * pcm_data[i];
  }
  
  return std::sqrt(sum_squares / num_samples);
}

bool DecibelAnalyzer::IsAboveThreshold(int decibel_value, int threshold) {
  return decibel_value > threshold;
}

} // namespace _360sk
