#include "client/asr_client.h"

#include <gtest/gtest.h>

#include <chrono>
#include <iostream>
#include <thread>

#include "client/audio_helper.h"
#include "utils/utils.h"

std::string host = "127.0.0.1";
// std::string host = "nlb-7rgn42g802oixwev19.cn-shanghai.nlb.aliyuncs.com";
int port = 10086;

// Test case
// command: ./client/asr_client_test --gtest_filter=AsrClient.Test
TEST(AsrClient, Test) {
    // _360sk::AsrClient* asrClient= new _360sk::AsrClient("127.0.0.1", 10086);
    // asrClient->Test();
    // EXPECT_EQ(add(1, 2), 3);
}

/*Connect*******************************************************/
void CreateAndUseChannel(int keepalive_ms) {
    _360sk::AsrClient* asrClient =
        new _360sk::AsrClient(host, port, keepalive_ms, 1);
    asrClient->Connect();
    std::this_thread::sleep_for(std::chrono::seconds(60));
}

// command: ./client/asr_client_test --gtest_filter=AsrClient.Connect
TEST(AsrClient, Connect) {
    const int num_threads = 3;
    std::vector<std::thread> threads;

    for (int i = 0; i < num_threads; ++i) {
        threads.push_back(std::thread(CreateAndUseChannel, (i + 1) * 1000));
    }
    for (auto& t : threads) {
        t.join();
    }
}

/*Initialize*******************************************************/
// command: ./client/asr_client_test --gtest_filter=AsrClient.Initialize
TEST(AsrClient, Initialize) {
    _360sk::AsrClient* asrClient = new _360sk::AsrClient(host, port, 10000, 1);
    if (0 == asrClient->Connect()) {
        std::string session_id;
        for (int i = 0; i < 35; i++) {
            session_id = "UT_Initialize()_" + _360sk::generate_unique_id() +
                         "_" + std::to_string(i);
            std::cout << "session_id:" << session_id << std::endl;
            int res_code =
                asrClient->Initialize(session_id, "cuishou", 600, false, false);
            std::cout << "Initialize res_code:" << res_code
                      << ", IsConnnected:" << asrClient->IsConnected()
                      << std::endl;
            std::this_thread::sleep_for(std::chrono::seconds(1));
        }
    }
}

/*Initialize*******************************************************/
// command: ./client/asr_client_test --gtest_filter=AsrClient.SendData

void SendData(int keepalive_ms) {
    _360sk::AsrClient* asrClient =
        new _360sk::AsrClient(host, port, keepalive_ms, 1);
    if (0 == asrClient->Connect()) {
        std::string session_id = "utSendData_" + _360sk::generate_unique_id();
        int res_code =
            asrClient->Initialize(session_id, "cuishou", 600, false, false);
        SHUKE_LOG << "Initialize res_code:" << res_code;
        if (0 == res_code) {
            res_code = asrClient->SuspendedResponse();
            if (0 == res_code) {
                _360sk::AudioSender audio_sender_(0.02, 8000);
                std::string wav_path =
                    "/data/oceanus_share/data/audio/wav-file/4014.wav";  
                audio_sender_.setAudioData(wav_path);
                // 检查 getAudioData 方法是否返回正确的数据
                const auto& stored_data = audio_sender_.getAudioData();
                for (int i = 0; i < 10; i++) {
                    int start = 0;
                    for (const auto& chunk : stored_data) {
                        // std::cout << "Chunk size: " << chunk.size() <<
                        // std::endl;
                        /*for (int16_t sample : chunk) {
                            std::cout << sample << " ";
                        }
                        std::cout << std::endl;*/
                        asrClient->SendData(chunk.data(),
                                            chunk.size() * sizeof(int16_t));
                        asrClient->ResetStartTime((start + 160) / 8);
                        std::this_thread::sleep_for(std::chrono::milliseconds(static_cast<int>(20)));
                        start += 160;
                    }
                }

                std::this_thread::sleep_for(std::chrono::seconds(100));
            } else {
                SHUKE_WARN << "SuspenedResponse failure. res_code:" << res_code;
            }
        } else {
            SHUKE_WARN << "Initialize failure. res_code:" << res_code;
        }
    }
    asrClient->Join();
}

TEST(AsrClient, SendData) {
    // SendData(100001);
    // SendData(100002);
    // SendData(100003);
    const int num_threads = 5;
    std::vector<std::thread> threads;

    for (int i = 0; i < num_threads; ++i) {
        threads.push_back(std::thread(SendData, (i + 1) * 1000));
    }
    for (auto& t : threads) {
        t.join();
    }
    // SendData(100004);
}


// ./client/asr_client_test
int main(int argc, char **argv) {
    ::testing::InitGoogleTest(&argc, argv);
    return RUN_ALL_TESTS();
}