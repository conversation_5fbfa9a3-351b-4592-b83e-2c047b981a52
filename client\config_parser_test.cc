#include <gtest/gtest.h>
#include "client/config_parser.h"

using namespace _360sk;

TEST(ConfigParser, Parse) {
    ConfigParser config;
    if (config.load("client/conf/config.ini")) {
        std::string test_name = config.get("test.name", "UnknownApp");
        int test_port = std::stoi(config.get("test.port", "3306"));
        std::cout << "test-Name: " << test_name << std::endl;
        std::cout << "test-Port: " << test_port << std::endl;

        std::cout << "audio-scene_type:" << config.get("audio.scene_type", "") << std::endl;
        std::cout << "audio-enable_sent_volume:" << std::stoi(config.get("audio.enable_sent_volume", "3")) << std::endl;
        std::cout << "audio-max_sentence_silence:" << std::stoi(config.get("audio.max_sentence_silence", "3")) << std::endl;

    } else {
        std::cerr << "Failed to load configuration file." << std::endl;
    }
}

// ./client/config_parser_test
int main(int argc, char **argv) {
    ::testing::InitGoogleTest(&argc, argv);
    return RUN_ALL_TESTS();
}