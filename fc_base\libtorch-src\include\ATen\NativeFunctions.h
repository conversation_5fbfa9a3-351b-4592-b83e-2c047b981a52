#pragma once

// @generated by tools/codegen/gen.py from NativeFunctions.h

#include <ATen/Context.h>
#include <ATen/NativeMetaFunctions.h>
#include <ATen/core/Reduction.h>
#include <c10/core/ScalarType.h>
#include <c10/core/TensorOptions.h>

#include <array>
#include <functional>
#include <string>
#include <tuple>
#include <vector>

namespace c10 {
class Scalar;
}
namespace at {
struct Generator;
class Tensor;
struct Type;
} // namespace at

namespace at {
namespace native {

TORCH_API at::Tensor _cast_Byte(const at::Tensor & self, bool non_blocking=false);
TORCH_API at::Tensor _cast_Char(const at::Tensor & self, bool non_blocking=false);
TORCH_API at::Tensor _cast_Double(const at::Tensor & self, bool non_blocking=false);
TORCH_API at::Tensor _cast_Float(const at::Tensor & self, bool non_blocking=false);
TORCH_API at::Tensor _cast_Int(const at::Tensor & self, bool non_blocking=false);
TORCH_API at::Tensor _cast_Long(const at::Tensor & self, bool non_blocking=false);
TORCH_API at::Tensor _cast_Short(const at::Tensor & self, bool non_blocking=false);
TORCH_API at::Tensor _cast_Half(const at::Tensor & self, bool non_blocking=false);
TORCH_API void _backward(const at::Tensor & self, at::TensorList inputs, const c10::optional<at::Tensor> & gradient={}, c10::optional<bool> retain_graph=c10::nullopt, bool create_graph=false);
TORCH_API void set_data(at::Tensor & self, const at::Tensor & new_data);
TORCH_API at::Tensor data(const at::Tensor & self);
TORCH_API bool is_leaf(const at::Tensor & self);
TORCH_API int64_t output_nr(const at::Tensor & self);
TORCH_API int64_t _version(const at::Tensor & self);
TORCH_API at::Tensor & requires_grad_(at::Tensor & self, bool requires_grad=true);
TORCH_API void retain_grad(at::Tensor & self);
TORCH_API bool retains_grad(const at::Tensor & self);
TORCH_API at::Tensor _fw_primal(const at::Tensor & self, int64_t level);
TORCH_API at::Tensor _make_dual(const at::Tensor & primal, const at::Tensor & tangent, int64_t level);
TORCH_API ::std::tuple<at::Tensor,at::Tensor> _unpack_dual(const at::Tensor & dual, int64_t level);
TORCH_API at::Tensor & rename_(at::Tensor & self, c10::optional<at::DimnameList> names);
TORCH_API at::Tensor rename(const at::Tensor & self, c10::optional<at::DimnameList> names);
TORCH_API at::Tensor align_to(const at::Tensor & self, at::DimnameList names);
TORCH_API at::Tensor align_to(const at::Tensor & self, at::DimnameList order, int64_t ellipsis_idx);
TORCH_API at::Tensor align_as(const at::Tensor & self, const at::Tensor & other);
TORCH_API ::std::vector<at::Tensor> align_tensors(at::TensorList tensors);
TORCH_API void _assert_async_cpu(const at::Tensor & self);
TORCH_API void _assert_async_cuda(const at::Tensor & self);
TORCH_API at::Tensor refine_names(const at::Tensor & self, at::DimnameList names);
TORCH_API bool _use_cudnn_ctc_loss(const at::Tensor & log_probs, const at::Tensor & targets, at::IntArrayRef input_lengths, at::IntArrayRef target_lengths, int64_t blank);
TORCH_API ::std::tuple<at::Tensor,at::Tensor> _cudnn_ctc_loss(const at::Tensor & log_probs, const at::Tensor & targets, at::IntArrayRef input_lengths, at::IntArrayRef target_lengths, int64_t blank, bool deterministic, bool zero_infinity);
TORCH_API bool _use_cudnn_rnn_flatten_weight();
TORCH_API at::Tensor _cudnn_rnn_flatten_weight(at::TensorList weight_arr, int64_t weight_stride0, int64_t input_size, int64_t mode, int64_t hidden_size, int64_t proj_size, int64_t num_layers, bool batch_first, bool bidirectional);
TORCH_API ::std::tuple<at::Tensor,at::Tensor,at::Tensor,at::Tensor,at::Tensor> _cudnn_rnn(const at::Tensor & input, at::TensorList weight, int64_t weight_stride0, const c10::optional<at::Tensor> & weight_buf, const at::Tensor & hx, const c10::optional<at::Tensor> & cx, int64_t mode, int64_t hidden_size, int64_t proj_size, int64_t num_layers, bool batch_first, double dropout, bool train, bool bidirectional, at::IntArrayRef batch_sizes, const c10::optional<at::Tensor> & dropout_state);
TORCH_API ::std::tuple<at::Tensor,at::Tensor,at::Tensor,::std::vector<at::Tensor>> _cudnn_rnn_backward(const at::Tensor & input, at::TensorList weight, int64_t weight_stride0, const at::Tensor & weight_buf, const at::Tensor & hx, const c10::optional<at::Tensor> & cx, const at::Tensor & output, const c10::optional<at::Tensor> & grad_output, const c10::optional<at::Tensor> & grad_hy, const c10::optional<at::Tensor> & grad_cy, int64_t mode, int64_t hidden_size, int64_t proj_size, int64_t num_layers, bool batch_first, double dropout, bool train, bool bidirectional, at::IntArrayRef batch_sizes, const c10::optional<at::Tensor> & dropout_state, const at::Tensor & reserve, ::std::array<bool,4> output_mask);
TORCH_API at::Tensor _cudnn_init_dropout_state(double dropout, bool train, int64_t dropout_seed, c10::optional<at::ScalarType> dtype={}, c10::optional<at::Layout> layout={}, c10::optional<at::Device> device={}, c10::optional<bool> pin_memory={});
TORCH_API int64_t _debug_has_internal_overlap(const at::Tensor & self);
TORCH_API ::std::tuple<at::Tensor,at::Tensor> fused_dropout_cuda(const at::Tensor & self, double p, c10::optional<at::Generator> generator=c10::nullopt);
TORCH_API at::Tensor masked_scale_cuda(const at::Tensor & self, const at::Tensor & mask, double scale);
TORCH_API ::std::tuple<at::Tensor,at::Tensor> _sobol_engine_draw(const at::Tensor & quasi, int64_t n, const at::Tensor & sobolstate, int64_t dimension, int64_t num_generated, c10::optional<at::ScalarType> dtype);
TORCH_API at::Tensor & _sobol_engine_ff_(at::Tensor & self, int64_t n, const at::Tensor & sobolstate, int64_t dimension, int64_t num_generated);
TORCH_API at::Tensor & _sobol_engine_scramble_(at::Tensor & self, const at::Tensor & ltm, int64_t dimension);
TORCH_API at::Tensor & _sobol_engine_initialize_state_(at::Tensor & self, int64_t dimension);
TORCH_API at::Tensor _reshape_from_tensor(const at::Tensor & self, const at::Tensor & shape);
TORCH_API at::Tensor _shape_as_tensor(const at::Tensor & self);
TORCH_API at::Tensor dropout(const at::Tensor & input, double p, bool train);
TORCH_API at::Tensor & dropout_(at::Tensor & self, double p, bool train);
TORCH_API at::Tensor feature_dropout(const at::Tensor & input, double p, bool train);
TORCH_API at::Tensor & feature_dropout_(at::Tensor & self, double p, bool train);
TORCH_API at::Tensor alpha_dropout(const at::Tensor & input, double p, bool train);
TORCH_API at::Tensor & alpha_dropout_(at::Tensor & self, double p, bool train);
TORCH_API at::Tensor feature_alpha_dropout(const at::Tensor & input, double p, bool train);
TORCH_API at::Tensor & feature_alpha_dropout_(at::Tensor & self, double p, bool train);
TORCH_API at::Tensor abs(const at::Tensor & self);
TORCH_API at::Tensor & abs_(at::Tensor & self);
TORCH_API at::Tensor & abs_out(const at::Tensor & self, at::Tensor & out);
TORCH_API at::Tensor absolute(const at::Tensor & self);
TORCH_API at::Tensor & absolute_out(const at::Tensor & self, at::Tensor & out);
TORCH_API at::Tensor & absolute_(at::Tensor & self);
TORCH_API at::Tensor angle(const at::Tensor & self);
TORCH_API at::Tensor & angle_out(const at::Tensor & self, at::Tensor & out);
TORCH_API at::Tensor view_as_real(const at::Tensor & self);
TORCH_API at::Tensor view_as_complex(const at::Tensor & self);
struct TORCH_API structured_sgn_out : public at::meta::structured_sgn {
void impl(const at::Tensor & self, const at::Tensor & out);
};
TORCH_API at::Tensor real(const at::Tensor & self);
TORCH_API at::Tensor imag(const at::Tensor & self);
TORCH_API at::Tensor _conj(const at::Tensor & self);
TORCH_API at::Tensor conj(const at::Tensor & self);
TORCH_API at::Tensor _conj_physical(const at::Tensor & self);
TORCH_API at::Tensor conj_physical(const at::Tensor & self);
TORCH_API at::Tensor & conj_physical_(at::Tensor & self);
TORCH_API at::Tensor & conj_physical_out(const at::Tensor & self, at::Tensor & out);
TORCH_API at::Tensor & conj_physical_out_sparse(const at::Tensor & self, at::Tensor & out);
TORCH_API at::Tensor resolve_conj(const at::Tensor & self);
TORCH_API at::Tensor resolve_neg(const at::Tensor & self);
TORCH_API at::Tensor _neg_view(const at::Tensor & self);
struct TORCH_API structured_acos_out : public at::meta::structured_acos {
void impl(const at::Tensor & self, const at::Tensor & out);
};
TORCH_API at::Tensor arccos(const at::Tensor & self);
TORCH_API at::Tensor & arccos_out(const at::Tensor & self, at::Tensor & out);
TORCH_API at::Tensor & arccos_(at::Tensor & self);
TORCH_API at::Tensor avg_pool1d(const at::Tensor & self, at::IntArrayRef kernel_size, at::IntArrayRef stride={}, at::IntArrayRef padding=0, bool ceil_mode=false, bool count_include_pad=true);
TORCH_API at::Tensor adaptive_avg_pool1d(const at::Tensor & self, at::IntArrayRef output_size);
TORCH_API ::std::tuple<at::Tensor,at::Tensor> adaptive_max_pool1d(const at::Tensor & self, at::IntArrayRef output_size);
struct TORCH_API structured_add_out : public at::meta::structured_add_Tensor {
void impl(const at::Tensor & self, const at::Tensor & other, const at::Scalar & alpha, const at::Tensor & out);
};
TORCH_API at::Tensor add_sparse(const at::Tensor & self, const at::Tensor & other, const at::Scalar & alpha=1);
TORCH_API at::Tensor & add_out_sparse_cpu(const at::Tensor & self, const at::Tensor & other, const at::Scalar & alpha, at::Tensor & out);
TORCH_API at::Tensor & add_sparse_(at::Tensor & self, const at::Tensor & other, const at::Scalar & alpha=1);
TORCH_API at::Tensor & add_out_sparse_cuda(const at::Tensor & self, const at::Tensor & other, const at::Scalar & alpha, at::Tensor & out);
TORCH_API at::Tensor add_sparse_csr(const at::Tensor & self, const at::Tensor & other, const at::Scalar & alpha=1);
TORCH_API at::Tensor & add_out_sparse_csr_cpu(const at::Tensor & self, const at::Tensor & other, const at::Scalar & alpha, at::Tensor & out);
TORCH_API at::Tensor & add_sparse_csr_(at::Tensor & self, const at::Tensor & other, const at::Scalar & alpha=1);
TORCH_API at::Tensor & add_out_sparse_csr_cuda(const at::Tensor & self, const at::Tensor & other, const at::Scalar & alpha, at::Tensor & out);
TORCH_API at::Tensor mkldnn_add(const at::Tensor & self, const at::Tensor & other, const at::Scalar & alpha=1);
TORCH_API at::Tensor & mkldnn_add_out(const at::Tensor & self, const at::Tensor & other, const at::Scalar & alpha, at::Tensor & out);
TORCH_API at::Tensor & mkldnn_add_(at::Tensor & self, const at::Tensor & other, const at::Scalar & alpha=1);
TORCH_API at::Tensor add_relu(const at::Tensor & self, const at::Tensor & other, const at::Scalar & alpha=1);
TORCH_API at::Tensor & add_relu_out(const at::Tensor & self, const at::Tensor & other, const at::Scalar & alpha, at::Tensor & out);
TORCH_API at::Tensor & add_relu_(at::Tensor & self, const at::Tensor & other, const at::Scalar & alpha=1);
TORCH_API at::Tensor add_relu(const at::Tensor & self, const at::Scalar & other, const at::Scalar & alpha=1);
TORCH_API at::Tensor & add_relu_(at::Tensor & self, const at::Scalar & other, const at::Scalar & alpha=1);
TORCH_API at::Tensor add(const at::Tensor & self, const at::Scalar & other, const at::Scalar & alpha=1);
TORCH_API at::Tensor & add_(at::Tensor & self, const at::Scalar & other, const at::Scalar & alpha=1);
struct TORCH_API structured_addmv_out_cpu : public at::meta::structured_addmv {
void impl(const at::Tensor & self, const at::Tensor & mat, const at::Tensor & vec, const at::Scalar & beta, const at::Scalar & alpha, const at::Tensor & out);
};
struct TORCH_API structured_addmv_out_cuda : public at::meta::structured_addmv {
void impl(const at::Tensor & self, const at::Tensor & mat, const at::Tensor & vec, const at::Scalar & beta, const at::Scalar & alpha, const at::Tensor & out);
};
TORCH_API at::Tensor math_addr(const at::Tensor & self, const at::Tensor & vec1, const at::Tensor & vec2, const at::Scalar & beta=1, const at::Scalar & alpha=1);
TORCH_API at::Tensor & math_addr_out(const at::Tensor & self, const at::Tensor & vec1, const at::Tensor & vec2, const at::Scalar & beta, const at::Scalar & alpha, at::Tensor & out);
TORCH_API at::Tensor & addr_(at::Tensor & self, const at::Tensor & vec1, const at::Tensor & vec2, const at::Scalar & beta=1, const at::Scalar & alpha=1);
TORCH_API at::Tensor addr(const at::Tensor & self, const at::Tensor & vec1, const at::Tensor & vec2, const at::Scalar & beta=1, const at::Scalar & alpha=1);
TORCH_API at::Tensor & addr_out(const at::Tensor & self, const at::Tensor & vec1, const at::Tensor & vec2, const at::Scalar & beta, const at::Scalar & alpha, at::Tensor & out);
TORCH_API at::Tensor affine_grid_generator(const at::Tensor & theta, at::IntArrayRef size, bool align_corners);
TORCH_API at::Tensor affine_grid_generator_backward(const at::Tensor & grad, at::IntArrayRef size, bool align_corners);
struct TORCH_API structured_all_out : public at::meta::structured_all_dim {
void impl(const at::Tensor & self, int64_t dim, bool keepdim, const at::Tensor & out);
};
TORCH_API at::Tensor all(const at::Tensor & self, at::Dimname dim, bool keepdim=false);
TORCH_API at::Tensor & all_out(const at::Tensor & self, at::Dimname dim, bool keepdim, at::Tensor & out);
TORCH_API bool allclose(const at::Tensor & self, const at::Tensor & other, double rtol=1e-05, double atol=1e-08, bool equal_nan=false);
struct TORCH_API structured_any_out : public at::meta::structured_any_dim {
void impl(const at::Tensor & self, int64_t dim, bool keepdim, const at::Tensor & out);
};
TORCH_API at::Tensor any(const at::Tensor & self, at::Dimname dim, bool keepdim=false);
TORCH_API at::Tensor & any_out(const at::Tensor & self, at::Dimname dim, bool keepdim, at::Tensor & out);
TORCH_API at::Tensor arange(const at::Scalar & end, c10::optional<at::ScalarType> dtype={}, c10::optional<at::Layout> layout={}, c10::optional<at::Device> device={}, c10::optional<bool> pin_memory={});
TORCH_API at::Tensor arange(const at::Scalar & start, const at::Scalar & end, c10::optional<at::ScalarType> dtype={}, c10::optional<at::Layout> layout={}, c10::optional<at::Device> device={}, c10::optional<bool> pin_memory={});
TORCH_API at::Tensor arange(const at::Scalar & start, const at::Scalar & end, const at::Scalar & step, c10::optional<at::ScalarType> dtype={}, c10::optional<at::Layout> layout={}, c10::optional<at::Device> device={}, c10::optional<bool> pin_memory={});
TORCH_API at::Tensor & arange_out(const at::Scalar & end, at::Tensor & out);
TORCH_API at::Tensor & arange_cpu_out(const at::Scalar & start, const at::Scalar & end, const at::Scalar & step, at::Tensor & out);
TORCH_API at::Tensor & arange_cuda_out(const at::Scalar & start, const at::Scalar & end, const at::Scalar & step, at::Tensor & out);
TORCH_API at::Tensor _dim_arange(const at::Tensor & like, int64_t dim);
struct TORCH_API structured_argmax_out : public at::meta::structured_argmax {
void impl(const at::Tensor & self, c10::optional<int64_t> dim, bool keepdim, const at::Tensor & out);
};
struct TORCH_API structured_argmin_out : public at::meta::structured_argmin {
void impl(const at::Tensor & self, c10::optional<int64_t> dim, bool keepdim, const at::Tensor & out);
};
struct TORCH_API structured_acosh_out : public at::meta::structured_acosh {
void impl(const at::Tensor & self, const at::Tensor & out);
};
TORCH_API at::Tensor arccosh(const at::Tensor & self);
TORCH_API at::Tensor & arccosh_out(const at::Tensor & self, at::Tensor & out);
TORCH_API at::Tensor & arccosh_(at::Tensor & self);
struct TORCH_API structured_asinh_out : public at::meta::structured_asinh {
void impl(const at::Tensor & self, const at::Tensor & out);
};
TORCH_API at::Tensor arcsinh(const at::Tensor & self);
TORCH_API at::Tensor & arcsinh_out(const at::Tensor & self, at::Tensor & out);
TORCH_API at::Tensor & arcsinh_(at::Tensor & self);
struct TORCH_API structured_atanh_out : public at::meta::structured_atanh {
void impl(const at::Tensor & self, const at::Tensor & out);
};
TORCH_API at::Tensor arctanh(const at::Tensor & self);
TORCH_API at::Tensor & arctanh_out(const at::Tensor & self, at::Tensor & out);
TORCH_API at::Tensor & arctanh_(at::Tensor & self);
TORCH_API at::Tensor as_strided_tensorimpl(const at::Tensor & self, at::IntArrayRef size, at::IntArrayRef stride, c10::optional<int64_t> storage_offset=c10::nullopt);
TORCH_API at::Tensor as_strided_qtensorimpl(const at::Tensor & self, at::IntArrayRef size, at::IntArrayRef stride, c10::optional<int64_t> storage_offset=c10::nullopt);
TORCH_API const at::Tensor & as_strided_(const at::Tensor & self, at::IntArrayRef size, at::IntArrayRef stride, c10::optional<int64_t> storage_offset=c10::nullopt);
struct TORCH_API structured_asin_out : public at::meta::structured_asin {
void impl(const at::Tensor & self, const at::Tensor & out);
};
TORCH_API at::Tensor asin_sparse(const at::Tensor & self);
TORCH_API at::Tensor & asin_out_sparse(const at::Tensor & self, at::Tensor & out);
TORCH_API at::Tensor & asin_sparse_(at::Tensor & self);
TORCH_API at::Tensor arcsin(const at::Tensor & self);
TORCH_API at::Tensor & arcsin_out(const at::Tensor & self, at::Tensor & out);
TORCH_API at::Tensor & arcsin_(at::Tensor & self);
struct TORCH_API structured_atan_out : public at::meta::structured_atan {
void impl(const at::Tensor & self, const at::Tensor & out);
};
TORCH_API at::Tensor arctan(const at::Tensor & self);
TORCH_API at::Tensor & arctan_out(const at::Tensor & self, at::Tensor & out);
TORCH_API at::Tensor & arctan_(at::Tensor & self);
TORCH_API at::Tensor atleast_1d(const at::Tensor & self);
TORCH_API ::std::vector<at::Tensor> atleast_1d(at::TensorList tensors);
TORCH_API at::Tensor atleast_2d(const at::Tensor & self);
TORCH_API ::std::vector<at::Tensor> atleast_2d(at::TensorList tensors);
TORCH_API at::Tensor atleast_3d(const at::Tensor & self);
TORCH_API ::std::vector<at::Tensor> atleast_3d(at::TensorList tensors);
TORCH_API at::Tensor baddbmm_cpu(const at::Tensor & self, const at::Tensor & batch1, const at::Tensor & batch2, const at::Scalar & beta=1, const at::Scalar & alpha=1);
TORCH_API at::Tensor & baddbmm_out_cpu(const at::Tensor & self, const at::Tensor & batch1, const at::Tensor & batch2, const at::Scalar & beta, const at::Scalar & alpha, at::Tensor & out);
TORCH_API at::Tensor & baddbmm__cpu(at::Tensor & self, const at::Tensor & batch1, const at::Tensor & batch2, const at::Scalar & beta=1, const at::Scalar & alpha=1);
TORCH_API at::Tensor baddbmm_cuda(const at::Tensor & self, const at::Tensor & batch1, const at::Tensor & batch2, const at::Scalar & beta=1, const at::Scalar & alpha=1);
TORCH_API at::Tensor & baddbmm_out_cuda(const at::Tensor & self, const at::Tensor & batch1, const at::Tensor & batch2, const at::Scalar & beta, const at::Scalar & alpha, at::Tensor & out);
TORCH_API at::Tensor & baddbmm__cuda(at::Tensor & self, const at::Tensor & batch1, const at::Tensor & batch2, const at::Scalar & beta=1, const at::Scalar & alpha=1);
TORCH_API at::Tensor & _baddbmm_mkl_(at::Tensor & self, const at::Tensor & batch1, const at::Tensor & batch2, const at::Scalar & beta=1, const at::Scalar & alpha=1);
TORCH_API at::Tensor bartlett_window(int64_t window_length, c10::optional<at::ScalarType> dtype={}, c10::optional<at::Layout> layout={}, c10::optional<at::Device> device={}, c10::optional<bool> pin_memory={});
TORCH_API at::Tensor bartlett_window(int64_t window_length, bool periodic, c10::optional<at::ScalarType> dtype={}, c10::optional<at::Layout> layout={}, c10::optional<at::Device> device={}, c10::optional<bool> pin_memory={});
TORCH_API at::Tensor batch_norm(const at::Tensor & input, const c10::optional<at::Tensor> & weight, const c10::optional<at::Tensor> & bias, const c10::optional<at::Tensor> & running_mean, const c10::optional<at::Tensor> & running_var, bool training, double momentum, double eps, bool cudnn_enabled);
TORCH_API at::Tensor quantized_batch_norm(const at::Tensor & input, const c10::optional<at::Tensor> & weight, const c10::optional<at::Tensor> & bias, const at::Tensor & mean, const at::Tensor & var, double eps, double output_scale, int64_t output_zero_point);
TORCH_API ::std::tuple<at::Tensor,at::Tensor,at::Tensor,at::Tensor,int64_t> _batch_norm_impl_index(const at::Tensor & input, const c10::optional<at::Tensor> & weight, const c10::optional<at::Tensor> & bias, const c10::optional<at::Tensor> & running_mean, const c10::optional<at::Tensor> & running_var, bool training, double momentum, double eps, bool cudnn_enabled);
TORCH_API ::std::tuple<at::Tensor,at::Tensor,at::Tensor> _batch_norm_impl_index_backward(int64_t impl_index, const at::Tensor & input, const at::Tensor & grad_output, const c10::optional<at::Tensor> & weight, const c10::optional<at::Tensor> & running_mean, const c10::optional<at::Tensor> & running_var, const c10::optional<at::Tensor> & save_mean, const c10::optional<at::Tensor> & save_var_transform, bool train, double eps, ::std::array<bool,3> output_mask, const at::Tensor & reservedSpace);
TORCH_API at::Tensor bernoulli(const at::Tensor & self, c10::optional<at::Generator> generator=c10::nullopt);
TORCH_API at::Tensor & bernoulli_out(const at::Tensor & self, c10::optional<at::Generator> generator, at::Tensor & out);
TORCH_API at::Tensor & bernoulli_(at::Tensor & self, const at::Tensor & p, c10::optional<at::Generator> generator=c10::nullopt);
TORCH_API at::Tensor & bernoulli_(at::Tensor & self, double p=0.5, c10::optional<at::Generator> generator=c10::nullopt);
TORCH_API at::Tensor bernoulli(const at::Tensor & self, double p, c10::optional<at::Generator> generator=c10::nullopt);
TORCH_API at::Tensor bilinear(const at::Tensor & input1, const at::Tensor & input2, const at::Tensor & weight, const c10::optional<at::Tensor> & bias);
TORCH_API at::Tensor binary_cross_entropy_cpu(const at::Tensor & self, const at::Tensor & target, const c10::optional<at::Tensor> & weight={}, int64_t reduction=at::Reduction::Mean);
TORCH_API at::Tensor & binary_cross_entropy_out_cpu(const at::Tensor & self, const at::Tensor & target, const c10::optional<at::Tensor> & weight, int64_t reduction, at::Tensor & out);
TORCH_API at::Tensor binary_cross_entropy_cuda(const at::Tensor & self, const at::Tensor & target, const c10::optional<at::Tensor> & weight={}, int64_t reduction=at::Reduction::Mean);
TORCH_API at::Tensor & binary_cross_entropy_out_cuda(const at::Tensor & self, const at::Tensor & target, const c10::optional<at::Tensor> & weight, int64_t reduction, at::Tensor & out);
TORCH_API at::Tensor binary_cross_entropy_backward_cpu(const at::Tensor & grad_output, const at::Tensor & self, const at::Tensor & target, const c10::optional<at::Tensor> & weight={}, int64_t reduction=at::Reduction::Mean);
TORCH_API at::Tensor & binary_cross_entropy_backward_out_cpu(const at::Tensor & grad_output, const at::Tensor & self, const at::Tensor & target, const c10::optional<at::Tensor> & weight, int64_t reduction, at::Tensor & grad_input);
TORCH_API at::Tensor binary_cross_entropy_backward_cuda(const at::Tensor & grad_output, const at::Tensor & self, const at::Tensor & target, const c10::optional<at::Tensor> & weight={}, int64_t reduction=at::Reduction::Mean);
TORCH_API at::Tensor & binary_cross_entropy_backward_out_cuda(const at::Tensor & grad_output, const at::Tensor & self, const at::Tensor & target, const c10::optional<at::Tensor> & weight, int64_t reduction, at::Tensor & grad_input);
TORCH_API at::Tensor binary_cross_entropy_with_logits(const at::Tensor & self, const at::Tensor & target, const c10::optional<at::Tensor> & weight={}, const c10::optional<at::Tensor> & pos_weight={}, int64_t reduction=at::Reduction::Mean);
TORCH_API at::Tensor binary_cross_entropy_with_logits_backward(const at::Tensor & grad_output, const at::Tensor & self, const at::Tensor & target, const c10::optional<at::Tensor> & weight={}, const c10::optional<at::Tensor> & pos_weight={}, int64_t reduction=at::Reduction::Mean);
TORCH_API at::Tensor _bincount_cpu(const at::Tensor & self, const c10::optional<at::Tensor> & weights={}, int64_t minlength=0);
TORCH_API at::Tensor _bincount_cuda(const at::Tensor & self, const c10::optional<at::Tensor> & weights={}, int64_t minlength=0);
struct TORCH_API structured_bitwise_not_out : public at::meta::structured_bitwise_not {
void impl(const at::Tensor & self, const at::Tensor & out);
};
struct TORCH_API structured_copysign_out : public at::meta::structured_copysign_Tensor {
void impl(const at::Tensor & self, const at::Tensor & other, const at::Tensor & out);
};
TORCH_API at::Tensor copysign(const at::Tensor & self, const at::Scalar & other);
TORCH_API at::Tensor & copysign_out(const at::Tensor & self, const at::Scalar & other, at::Tensor & out);
TORCH_API at::Tensor & copysign_(at::Tensor & self, const at::Scalar & other);
TORCH_API at::Tensor logical_not(const at::Tensor & self);
TORCH_API at::Tensor & logical_not_(at::Tensor & self);
TORCH_API at::Tensor & logical_not_out(const at::Tensor & self, at::Tensor & out);
TORCH_API at::Tensor logical_xor(const at::Tensor & self, const at::Tensor & other);
TORCH_API at::Tensor & logical_xor_(at::Tensor & self, const at::Tensor & other);
TORCH_API at::Tensor & logical_xor_out(const at::Tensor & self, const at::Tensor & other, at::Tensor & out);
TORCH_API at::Tensor logical_and(const at::Tensor & self, const at::Tensor & other);
TORCH_API at::Tensor & logical_and_(at::Tensor & self, const at::Tensor & other);
TORCH_API at::Tensor & logical_and_out(const at::Tensor & self, const at::Tensor & other, at::Tensor & out);
TORCH_API at::Tensor logical_or(const at::Tensor & self, const at::Tensor & other);
TORCH_API at::Tensor & logical_or_(at::Tensor & self, const at::Tensor & other);
TORCH_API at::Tensor & logical_or_out(const at::Tensor & self, const at::Tensor & other, at::Tensor & out);
TORCH_API at::Tensor blackman_window(int64_t window_length, c10::optional<at::ScalarType> dtype={}, c10::optional<at::Layout> layout={}, c10::optional<at::Device> device={}, c10::optional<bool> pin_memory={});
TORCH_API at::Tensor blackman_window(int64_t window_length, bool periodic, c10::optional<at::ScalarType> dtype={}, c10::optional<at::Layout> layout={}, c10::optional<at::Device> device={}, c10::optional<bool> pin_memory={});
TORCH_API at::Tensor bmm_cpu(const at::Tensor & self, const at::Tensor & mat2);
TORCH_API at::Tensor & bmm_out_cpu(const at::Tensor & self, const at::Tensor & mat2, at::Tensor & out);
TORCH_API at::Tensor bmm_cuda(const at::Tensor & self, const at::Tensor & mat2);
TORCH_API at::Tensor & bmm_out_cuda(const at::Tensor & self, const at::Tensor & mat2, at::Tensor & out);
TORCH_API at::Tensor bmm_sparse_cpu(const at::Tensor & self, const at::Tensor & mat2);
TORCH_API at::Tensor & bmm_out_sparse_cpu(const at::Tensor & self, const at::Tensor & mat2, at::Tensor & out);
TORCH_API at::Tensor bmm_sparse_cuda(const at::Tensor & self, const at::Tensor & mat2);
TORCH_API at::Tensor & bmm_out_sparse_cuda(const at::Tensor & self, const at::Tensor & mat2, at::Tensor & out);
TORCH_API ::std::vector<at::Tensor> broadcast_tensors(at::TensorList tensors);
TORCH_API at::Tensor broadcast_to(const at::Tensor & self, at::IntArrayRef size);
TORCH_API at::Tensor cat(at::TensorList tensors, int64_t dim=0);
TORCH_API at::Tensor & cat_out(at::TensorList tensors, int64_t dim, at::Tensor & out);
TORCH_API at::Tensor cat(at::TensorList tensors, at::Dimname dim);
TORCH_API at::Tensor & cat_out(at::TensorList tensors, at::Dimname dim, at::Tensor & out);
TORCH_API at::Tensor concat(at::TensorList tensors, int64_t dim=0);
TORCH_API at::Tensor & concat_out(at::TensorList tensors, int64_t dim, at::Tensor & out);
TORCH_API at::Tensor concat(at::TensorList tensors, at::Dimname dim);
TORCH_API at::Tensor & concat_out(at::TensorList tensors, at::Dimname dim, at::Tensor & out);
TORCH_API at::Tensor block_diag(at::TensorList tensors);
TORCH_API at::Tensor ceil(const at::Tensor & self);
TORCH_API at::Tensor & ceil_(at::Tensor & self);
struct TORCH_API structured_ceil_out : public at::meta::structured_ceil {
void impl(const at::Tensor & self, const at::Tensor & out);
};
TORCH_API at::Tensor chain_matmul(at::TensorList matrices);
TORCH_API at::Tensor & chain_matmul_out(at::TensorList matrices, at::Tensor & out);
TORCH_API ::std::vector<at::Tensor> unsafe_chunk(const at::Tensor & self, int64_t chunks, int64_t dim=0);
TORCH_API ::std::vector<at::Tensor> chunk(const at::Tensor & self, int64_t chunks, int64_t dim=0);
TORCH_API ::std::vector<at::Tensor> tensor_split(const at::Tensor & self, int64_t sections, int64_t dim=0);
TORCH_API ::std::vector<at::Tensor> tensor_split(const at::Tensor & self, at::IntArrayRef indices, int64_t dim=0);
TORCH_API ::std::vector<at::Tensor> tensor_split(const at::Tensor & self, const at::Tensor & tensor_indices_or_sections, int64_t dim=0);
TORCH_API at::Tensor & clamp_(at::Tensor & self, const c10::optional<at::Scalar> & min=c10::nullopt, const c10::optional<at::Scalar> & max=c10::nullopt);
struct TORCH_API structured_clamp_out : public at::meta::structured_clamp {
void impl(const at::Tensor & self, at::OptionalScalarRef min, at::OptionalScalarRef max, const at::Tensor & out);
};
TORCH_API at::Tensor clamp_quantized_cpu(const at::Tensor & self, const c10::optional<at::Scalar> & min=c10::nullopt, const c10::optional<at::Scalar> & max=c10::nullopt);
TORCH_API at::Tensor & clamp_(at::Tensor & self, const c10::optional<at::Tensor> & min={}, const c10::optional<at::Tensor> & max={});
TORCH_API at::Tensor clamp(const at::Tensor & self, const c10::optional<at::Tensor> & min={}, const c10::optional<at::Tensor> & max={});
TORCH_API at::Tensor & clamp_out(const at::Tensor & self, const c10::optional<at::Tensor> & min, const c10::optional<at::Tensor> & max, at::Tensor & out);
TORCH_API at::Tensor clamp_max(const at::Tensor & self, const at::Scalar & max);
TORCH_API at::Tensor & clamp_max_(at::Tensor & self, const at::Scalar & max);
TORCH_API at::Tensor & clamp_max_out(const at::Tensor & self, const at::Scalar & max, at::Tensor & out);
TORCH_API at::Tensor clamp_max(const at::Tensor & self, const at::Tensor & max);
TORCH_API at::Tensor & clamp_max_(at::Tensor & self, const at::Tensor & max);
TORCH_API at::Tensor & clamp_max_out(const at::Tensor & self, const at::Tensor & max, at::Tensor & out);
TORCH_API at::Tensor clamp_min(const at::Tensor & self, const at::Scalar & min);
TORCH_API at::Tensor & clamp_min_(at::Tensor & self, const at::Scalar & min);
TORCH_API at::Tensor & clamp_min_out(const at::Tensor & self, const at::Scalar & min, at::Tensor & out);
TORCH_API at::Tensor clamp_min(const at::Tensor & self, const at::Tensor & min);
TORCH_API at::Tensor & clamp_min_(at::Tensor & self, const at::Tensor & min);
TORCH_API at::Tensor & clamp_min_out(const at::Tensor & self, const at::Tensor & min, at::Tensor & out);
TORCH_API at::Tensor clip(const at::Tensor & self, const c10::optional<at::Scalar> & min=c10::nullopt, const c10::optional<at::Scalar> & max=c10::nullopt);
TORCH_API at::Tensor & clip_out(const at::Tensor & self, const c10::optional<at::Scalar> & min, const c10::optional<at::Scalar> & max, at::Tensor & out);
TORCH_API at::Tensor & clip_(at::Tensor & self, const c10::optional<at::Scalar> & min=c10::nullopt, const c10::optional<at::Scalar> & max=c10::nullopt);
TORCH_API at::Tensor clip(const at::Tensor & self, const c10::optional<at::Tensor> & min={}, const c10::optional<at::Tensor> & max={});
TORCH_API at::Tensor & clip_out(const at::Tensor & self, const c10::optional<at::Tensor> & min, const c10::optional<at::Tensor> & max, at::Tensor & out);
TORCH_API at::Tensor & clip_(at::Tensor & self, const c10::optional<at::Tensor> & min={}, const c10::optional<at::Tensor> & max={});
TORCH_API bool cudnn_is_acceptable(const at::Tensor & self);
TORCH_API at::Tensor complex(const at::Tensor & real, const at::Tensor & imag);
TORCH_API at::Tensor & complex_out(const at::Tensor & real, const at::Tensor & imag, at::Tensor & out);
TORCH_API at::Tensor polar(const at::Tensor & abs, const at::Tensor & angle);
TORCH_API at::Tensor & polar_out(const at::Tensor & abs, const at::Tensor & angle, at::Tensor & out);
TORCH_API at::Tensor constant_pad_nd(const at::Tensor & self, at::IntArrayRef pad, const at::Scalar & value=0);
TORCH_API at::Tensor contiguous(const at::Tensor & self, at::MemoryFormat memory_format=MemoryFormat::Contiguous);
TORCH_API at::Tensor convolution(const at::Tensor & input, const at::Tensor & weight, const c10::optional<at::Tensor> & bias, at::IntArrayRef stride, at::IntArrayRef padding, at::IntArrayRef dilation, bool transposed, at::IntArrayRef output_padding, int64_t groups);
TORCH_API at::Tensor convolution_overrideable(const at::Tensor & input, const at::Tensor & weight, const c10::optional<at::Tensor> & bias, at::IntArrayRef stride, at::IntArrayRef padding, at::IntArrayRef dilation, bool transposed, at::IntArrayRef output_padding, int64_t groups);
TORCH_API ::std::tuple<at::Tensor,at::Tensor,at::Tensor> convolution_backward_overrideable(const at::Tensor & grad_output, const at::Tensor & input, const at::Tensor & weight, at::IntArrayRef stride, at::IntArrayRef padding, at::IntArrayRef dilation, bool transposed, at::IntArrayRef output_padding, int64_t groups, ::std::array<bool,3> output_mask);
TORCH_API at::Tensor _convolution(const at::Tensor & input, const at::Tensor & weight, const c10::optional<at::Tensor> & bias, at::IntArrayRef stride, at::IntArrayRef padding, at::IntArrayRef dilation, bool transposed, at::IntArrayRef output_padding, int64_t groups, bool benchmark, bool deterministic, bool cudnn_enabled, bool allow_tf32);
TORCH_API at::Tensor _convolution(const at::Tensor & input, const at::Tensor & weight, const c10::optional<at::Tensor> & bias, at::IntArrayRef stride, at::IntArrayRef padding, at::IntArrayRef dilation, bool transposed, at::IntArrayRef output_padding, int64_t groups, bool benchmark, bool deterministic, bool cudnn_enabled);
TORCH_API at::Tensor _convolution_mode(const at::Tensor & input, const at::Tensor & weight, const c10::optional<at::Tensor> & bias, at::IntArrayRef stride, c10::string_view padding, at::IntArrayRef dilation, int64_t groups);
TORCH_API at::Tensor _convolution_nogroup(const at::Tensor & input, const at::Tensor & weight, const c10::optional<at::Tensor> & bias, at::IntArrayRef stride, at::IntArrayRef padding, at::IntArrayRef dilation, bool transposed, at::IntArrayRef output_padding);
TORCH_API ::std::tuple<at::Tensor,at::Tensor,at::Tensor> _convolution_double_backward(const c10::optional<at::Tensor> & ggI, const c10::optional<at::Tensor> & ggW, const c10::optional<at::Tensor> & ggb, const at::Tensor & gO, const at::Tensor & weight, const at::Tensor & self, at::IntArrayRef stride, at::IntArrayRef padding, at::IntArrayRef dilation, bool transposed, at::IntArrayRef output_padding, int64_t groups, bool benchmark, bool deterministic, bool cudnn_enabled, bool allow_tf32, ::std::array<bool,3> output_mask);
TORCH_API at::Tensor conv1d(const at::Tensor & input, const at::Tensor & weight, const c10::optional<at::Tensor> & bias={}, at::IntArrayRef stride=1, at::IntArrayRef padding=0, at::IntArrayRef dilation=1, int64_t groups=1);
TORCH_API at::Tensor conv2d(const at::Tensor & input, const at::Tensor & weight, const c10::optional<at::Tensor> & bias={}, at::IntArrayRef stride=1, at::IntArrayRef padding=0, at::IntArrayRef dilation=1, int64_t groups=1);
TORCH_API at::Tensor conv3d(const at::Tensor & input, const at::Tensor & weight, const c10::optional<at::Tensor> & bias={}, at::IntArrayRef stride=1, at::IntArrayRef padding=0, at::IntArrayRef dilation=1, int64_t groups=1);
TORCH_API at::Tensor conv1d(const at::Tensor & input, const at::Tensor & weight, const c10::optional<at::Tensor> & bias={}, at::IntArrayRef stride=1, c10::string_view padding="valid", at::IntArrayRef dilation=1, int64_t groups=1);
TORCH_API at::Tensor conv2d(const at::Tensor & input, const at::Tensor & weight, const c10::optional<at::Tensor> & bias={}, at::IntArrayRef stride=1, c10::string_view padding="valid", at::IntArrayRef dilation=1, int64_t groups=1);
TORCH_API at::Tensor conv3d(const at::Tensor & input, const at::Tensor & weight, const c10::optional<at::Tensor> & bias={}, at::IntArrayRef stride=1, c10::string_view padding="valid", at::IntArrayRef dilation=1, int64_t groups=1);
TORCH_API at::Tensor conv_tbc(const at::Tensor & self, const at::Tensor & weight, const at::Tensor & bias, int64_t pad=0);
TORCH_API ::std::tuple<at::Tensor,at::Tensor,at::Tensor> conv_tbc_backward(const at::Tensor & self, const at::Tensor & input, const at::Tensor & weight, const at::Tensor & bias, int64_t pad);
TORCH_API at::Tensor conv_transpose1d(const at::Tensor & input, const at::Tensor & weight, const c10::optional<at::Tensor> & bias={}, at::IntArrayRef stride=1, at::IntArrayRef padding=0, at::IntArrayRef output_padding=0, int64_t groups=1, at::IntArrayRef dilation=1);
TORCH_API at::Tensor conv_transpose2d(const at::Tensor & input, const at::Tensor & weight, const c10::optional<at::Tensor> & bias={}, at::IntArrayRef stride=1, at::IntArrayRef padding=0, at::IntArrayRef output_padding=0, int64_t groups=1, at::IntArrayRef dilation=1);
TORCH_API at::Tensor conv_transpose3d(const at::Tensor & input, const at::Tensor & weight, const c10::optional<at::Tensor> & bias={}, at::IntArrayRef stride=1, at::IntArrayRef padding=0, at::IntArrayRef output_padding=0, int64_t groups=1, at::IntArrayRef dilation=1);
TORCH_API at::Tensor & copy_(at::Tensor & self, const at::Tensor & src, bool non_blocking=false);
TORCH_API at::Tensor & copy_mkldnn_(at::Tensor & self, const at::Tensor & src, bool non_blocking=false);
struct TORCH_API structured_cos_out : public at::meta::structured_cos {
void impl(const at::Tensor & self, const at::Tensor & out);
};
struct TORCH_API structured_cosh_out : public at::meta::structured_cosh {
void impl(const at::Tensor & self, const at::Tensor & out);
};
TORCH_API at::Tensor cosine_embedding_loss(const at::Tensor & input1, const at::Tensor & input2, const at::Tensor & target, double margin=0.0, int64_t reduction=at::Reduction::Mean);
TORCH_API at::Tensor count_nonzero_cpu(const at::Tensor & self, at::IntArrayRef dim);
TORCH_API at::Tensor count_nonzero_cuda(const at::Tensor & self, at::IntArrayRef dim);
TORCH_API at::Tensor count_nonzero(const at::Tensor & self, c10::optional<int64_t> dim=c10::nullopt);
TORCH_API at::Tensor cov(const at::Tensor & self, int64_t correction=1, const c10::optional<at::Tensor> & fweights={}, const c10::optional<at::Tensor> & aweights={});
TORCH_API at::Tensor corrcoef(const at::Tensor & self);
TORCH_API at::Tensor cudnn_affine_grid_generator_forward(const at::Tensor & theta, int64_t N, int64_t C, int64_t H, int64_t W);
TORCH_API at::Tensor cudnn_affine_grid_generator_backward(const at::Tensor & grad, int64_t N, int64_t C, int64_t H, int64_t W);
TORCH_API ::std::tuple<at::Tensor,at::Tensor,at::Tensor,at::Tensor> cudnn_batch_norm(const at::Tensor & input, const at::Tensor & weight, const c10::optional<at::Tensor> & bias, const c10::optional<at::Tensor> & running_mean, const c10::optional<at::Tensor> & running_var, bool training, double exponential_average_factor, double epsilon);
TORCH_API ::std::tuple<at::Tensor,at::Tensor,at::Tensor> cudnn_batch_norm_backward(const at::Tensor & input, const at::Tensor & grad_output, const at::Tensor & weight, const c10::optional<at::Tensor> & running_mean, const c10::optional<at::Tensor> & running_var, const c10::optional<at::Tensor> & save_mean, const c10::optional<at::Tensor> & save_var, double epsilon, const at::Tensor & reserveSpace);
TORCH_API at::Tensor cudnn_convolution_deprecated(const at::Tensor & self, const at::Tensor & weight, const c10::optional<at::Tensor> & bias, at::IntArrayRef padding, at::IntArrayRef stride, at::IntArrayRef dilation, int64_t groups, bool benchmark, bool deterministic);
TORCH_API at::Tensor cudnn_convolution_deprecated2(const at::Tensor & self, const at::Tensor & weight, at::IntArrayRef padding, at::IntArrayRef stride, at::IntArrayRef dilation, int64_t groups, bool benchmark, bool deterministic);
TORCH_API at::Tensor cudnn_convolution(const at::Tensor & self, const at::Tensor & weight, at::IntArrayRef padding, at::IntArrayRef stride, at::IntArrayRef dilation, int64_t groups, bool benchmark, bool deterministic, bool allow_tf32);
TORCH_API at::Tensor cudnn_convolution_backward_input(at::IntArrayRef self_size, const at::Tensor & grad_output, const at::Tensor & weight, at::IntArrayRef padding, at::IntArrayRef stride, at::IntArrayRef dilation, int64_t groups, bool benchmark, bool deterministic, bool allow_tf32);
TORCH_API ::std::tuple<at::Tensor,at::Tensor> cudnn_convolution_backward(const at::Tensor & self, const at::Tensor & grad_output, const at::Tensor & weight, at::IntArrayRef padding, at::IntArrayRef stride, at::IntArrayRef dilation, int64_t groups, bool benchmark, bool deterministic, bool allow_tf32, ::std::array<bool,2> output_mask);
TORCH_API at::Tensor cudnn_convolution_backward_weight(at::IntArrayRef weight_size, const at::Tensor & grad_output, const at::Tensor & self, at::IntArrayRef padding, at::IntArrayRef stride, at::IntArrayRef dilation, int64_t groups, bool benchmark, bool deterministic, bool allow_tf32);
TORCH_API at::Tensor cudnn_convolution_transpose_deprecated(const at::Tensor & self, const at::Tensor & weight, const c10::optional<at::Tensor> & bias, at::IntArrayRef padding, at::IntArrayRef output_padding, at::IntArrayRef stride, at::IntArrayRef dilation, int64_t groups, bool benchmark, bool deterministic);
TORCH_API at::Tensor cudnn_convolution_transpose_deprecated2(const at::Tensor & self, const at::Tensor & weight, at::IntArrayRef padding, at::IntArrayRef output_padding, at::IntArrayRef stride, at::IntArrayRef dilation, int64_t groups, bool benchmark, bool deterministic);
TORCH_API at::Tensor cudnn_convolution_transpose(const at::Tensor & self, const at::Tensor & weight, at::IntArrayRef padding, at::IntArrayRef output_padding, at::IntArrayRef stride, at::IntArrayRef dilation, int64_t groups, bool benchmark, bool deterministic, bool allow_tf32);
TORCH_API ::std::tuple<at::Tensor,at::Tensor> cudnn_convolution_transpose_backward(const at::Tensor & self, const at::Tensor & grad_output, const at::Tensor & weight, at::IntArrayRef padding, at::IntArrayRef output_padding, at::IntArrayRef stride, at::IntArrayRef dilation, int64_t groups, bool benchmark, bool deterministic, bool allow_tf32, ::std::array<bool,2> output_mask);
TORCH_API at::Tensor cudnn_convolution_transpose_backward_input(const at::Tensor & grad_output, const at::Tensor & weight, at::IntArrayRef padding, at::IntArrayRef stride, at::IntArrayRef dilation, int64_t groups, bool benchmark, bool deterministic, bool allow_tf32);
TORCH_API at::Tensor cudnn_convolution_transpose_backward_weight(at::IntArrayRef weight_size, const at::Tensor & grad_output, const at::Tensor & self, at::IntArrayRef padding, at::IntArrayRef stride, at::IntArrayRef dilation, int64_t groups, bool benchmark, bool deterministic, bool allow_tf32);
TORCH_API at::Tensor cudnn_convolution_relu(const at::Tensor & self, const at::Tensor & weight, const c10::optional<at::Tensor> & bias, at::IntArrayRef stride, at::IntArrayRef padding, at::IntArrayRef dilation, int64_t groups);
TORCH_API at::Tensor cudnn_convolution_add_relu(const at::Tensor & self, const at::Tensor & weight, const at::Tensor & z, const c10::optional<at::Scalar> & alpha, const c10::optional<at::Tensor> & bias, at::IntArrayRef stride, at::IntArrayRef padding, at::IntArrayRef dilation, int64_t groups);
TORCH_API at::Tensor cudnn_grid_sampler_forward(const at::Tensor & self, const at::Tensor & grid);
TORCH_API ::std::tuple<at::Tensor,at::Tensor> cudnn_grid_sampler_backward(const at::Tensor & self, const at::Tensor & grid, const at::Tensor & grad_output);
TORCH_API ::std::tuple<at::Tensor,at::Tensor> cummax(const at::Tensor & self, int64_t dim);
TORCH_API ::std::tuple<at::Tensor &,at::Tensor &> cummax_out(const at::Tensor & self, int64_t dim, at::Tensor & values, at::Tensor & indices);
TORCH_API ::std::tuple<at::Tensor,at::Tensor> cummax(const at::Tensor & self, at::Dimname dim);
TORCH_API ::std::tuple<at::Tensor &,at::Tensor &> cummax_out(const at::Tensor & self, at::Dimname dim, at::Tensor & values, at::Tensor & indices);
TORCH_API void cummax_helper_cpu(const at::Tensor & self, at::Tensor & values, at::Tensor & indices, int64_t dim);
TORCH_API void cummax_helper_cuda(const at::Tensor & self, at::Tensor & values, at::Tensor & indices, int64_t dim);
TORCH_API ::std::tuple<at::Tensor,at::Tensor> cummin(const at::Tensor & self, int64_t dim);
TORCH_API ::std::tuple<at::Tensor &,at::Tensor &> cummin_out(const at::Tensor & self, int64_t dim, at::Tensor & values, at::Tensor & indices);
TORCH_API ::std::tuple<at::Tensor,at::Tensor> cummin(const at::Tensor & self, at::Dimname dim);
TORCH_API ::std::tuple<at::Tensor &,at::Tensor &> cummin_out(const at::Tensor & self, at::Dimname dim, at::Tensor & values, at::Tensor & indices);
TORCH_API void cummin_helper_cpu(const at::Tensor & self, at::Tensor & values, at::Tensor & indices, int64_t dim);
TORCH_API void cummin_helper_cuda(const at::Tensor & self, at::Tensor & values, at::Tensor & indices, int64_t dim);
TORCH_API at::Tensor cummaxmin_backward(const at::Tensor & grad, const at::Tensor & input, const at::Tensor & indices, int64_t dim);
struct TORCH_API structured_cumprod_out : public at::meta::structured_cumprod {
void impl(const at::Tensor & self, int64_t dim, c10::optional<at::ScalarType> dtype, const at::Tensor & out);
};
TORCH_API at::Tensor cumprod(const at::Tensor & self, at::Dimname dim, c10::optional<at::ScalarType> dtype=c10::nullopt);
TORCH_API at::Tensor & cumprod_out(const at::Tensor & self, at::Dimname dim, c10::optional<at::ScalarType> dtype, at::Tensor & out);
TORCH_API at::Tensor & cumprod_(at::Tensor & self, at::Dimname dim, c10::optional<at::ScalarType> dtype=c10::nullopt);
TORCH_API at::Tensor cumprod_backward(const at::Tensor & grad, const at::Tensor & input, int64_t dim, const at::Tensor & output);
struct TORCH_API structured_cumsum_out : public at::meta::structured_cumsum {
void impl(const at::Tensor & self, int64_t dim, c10::optional<at::ScalarType> dtype, const at::Tensor & out);
};
TORCH_API at::Tensor cumsum(const at::Tensor & self, at::Dimname dim, c10::optional<at::ScalarType> dtype=c10::nullopt);
TORCH_API at::Tensor & cumsum_out(const at::Tensor & self, at::Dimname dim, c10::optional<at::ScalarType> dtype, at::Tensor & out);
TORCH_API at::Tensor & cumsum_(at::Tensor & self, at::Dimname dim, c10::optional<at::ScalarType> dtype=c10::nullopt);
TORCH_API at::Tensor cumulative_trapezoid(const at::Tensor & y, const at::Tensor & x, int64_t dim=-1);
TORCH_API at::Tensor cumulative_trapezoid(const at::Tensor & y, const at::Scalar & dx=1, int64_t dim=-1);
TORCH_API at::Tensor ctc_loss(const at::Tensor & log_probs, const at::Tensor & targets, at::IntArrayRef input_lengths, at::IntArrayRef target_lengths, int64_t blank=0, int64_t reduction=at::Reduction::Mean, bool zero_infinity=false);
TORCH_API at::Tensor ctc_loss(const at::Tensor & log_probs, const at::Tensor & targets, const at::Tensor & input_lengths, const at::Tensor & target_lengths, int64_t blank=0, int64_t reduction=at::Reduction::Mean, bool zero_infinity=false);
TORCH_API ::std::tuple<at::Tensor,at::Tensor> ctc_loss_cpu(const at::Tensor & log_probs, const at::Tensor & targets, at::IntArrayRef input_lengths, at::IntArrayRef target_lengths, int64_t blank=0, bool zero_infinity=false);
TORCH_API ::std::tuple<at::Tensor,at::Tensor> ctc_loss_gpu(const at::Tensor & log_probs, const at::Tensor & targets, at::IntArrayRef input_lengths, at::IntArrayRef target_lengths, int64_t blank=0, bool zero_infinity=false);
TORCH_API at::Tensor ctc_loss_backward_cpu(const at::Tensor & grad, const at::Tensor & log_probs, const at::Tensor & targets, at::IntArrayRef input_lengths, at::IntArrayRef target_lengths, const at::Tensor & neg_log_likelihood, const at::Tensor & log_alpha, int64_t blank, bool zero_infinity=false);
TORCH_API at::Tensor ctc_loss_backward_gpu(const at::Tensor & grad, const at::Tensor & log_probs, const at::Tensor & targets, at::IntArrayRef input_lengths, at::IntArrayRef target_lengths, const at::Tensor & neg_log_likelihood, const at::Tensor & log_alpha, int64_t blank, bool zero_infinity=false);
TORCH_API at::Tensor diag_embed(const at::Tensor & self, int64_t offset=0, int64_t dim1=-2, int64_t dim2=-1);
TORCH_API at::Tensor diagflat(const at::Tensor & self, int64_t offset=0);
TORCH_API at::Tensor diagonal(const at::Tensor & self, int64_t offset=0, int64_t dim1=0, int64_t dim2=1);
TORCH_API at::Tensor diagonal(const at::Tensor & self, at::Dimname outdim, at::Dimname dim1, at::Dimname dim2, int64_t offset=0);
TORCH_API at::Tensor diagonal_backward(const at::Tensor & grad_output, at::IntArrayRef input_sizes, int64_t offset, int64_t dim1, int64_t dim2);
TORCH_API at::Tensor & fill_diagonal_(at::Tensor & self, const at::Scalar & fill_value, bool wrap=false);
TORCH_API at::Tensor diff(const at::Tensor & self, int64_t n=1, int64_t dim=-1, const c10::optional<at::Tensor> & prepend={}, const c10::optional<at::Tensor> & append={});
TORCH_API at::Tensor & diff_out(const at::Tensor & self, int64_t n, int64_t dim, const c10::optional<at::Tensor> & prepend, const c10::optional<at::Tensor> & append, at::Tensor & out);
TORCH_API ::std::vector<at::Tensor> gradient(const at::Tensor & self, const c10::optional<at::Scalar> & spacing=c10::nullopt, c10::optional<int64_t> dim=c10::nullopt, int64_t edge_order=1);
TORCH_API ::std::vector<at::Tensor> gradient(const at::Tensor & self, const at::Scalar & spacing, at::IntArrayRef dim, int64_t edge_order=1);
TORCH_API ::std::vector<at::Tensor> gradient(const at::Tensor & self, at::IntArrayRef dim, int64_t edge_order=1);
TORCH_API ::std::vector<at::Tensor> gradient(const at::Tensor & self, at::ArrayRef<at::Scalar> spacing, c10::optional<int64_t> dim=c10::nullopt, int64_t edge_order=1);
TORCH_API ::std::vector<at::Tensor> gradient(const at::Tensor & self, at::ArrayRef<at::Scalar> spacing, at::IntArrayRef dim, int64_t edge_order=1);
TORCH_API ::std::vector<at::Tensor> gradient(const at::Tensor & self, at::TensorList spacing, c10::optional<int64_t> dim=c10::nullopt, int64_t edge_order=1);
TORCH_API ::std::vector<at::Tensor> gradient(const at::Tensor & self, at::TensorList spacing, at::IntArrayRef dim, int64_t edge_order=1);
struct TORCH_API structured_div_out : public at::meta::structured_div_Tensor {
void impl(const at::Tensor & self, const at::Tensor & other, const at::Tensor & out);
};
TORCH_API at::Tensor div_sparse(const at::Tensor & self, const at::Tensor & other);
TORCH_API at::Tensor & div_out_sparse_zerodim(const at::Tensor & self, const at::Tensor & other, at::Tensor & out);
TORCH_API at::Tensor & div_sparse_(at::Tensor & self, const at::Tensor & other);
struct TORCH_API structured_div_out_mode : public at::meta::structured_div_Tensor_mode {
void impl(const at::Tensor & self, const at::Tensor & other, c10::optional<c10::string_view> rounding_mode, const at::Tensor & out);
};
TORCH_API at::Tensor div_sparse(const at::Tensor & self, const at::Tensor & other, c10::optional<c10::string_view> rounding_mode);
TORCH_API at::Tensor & div_out_sparse_zerodim(const at::Tensor & self, const at::Tensor & other, c10::optional<c10::string_view> rounding_mode, at::Tensor & out);
TORCH_API at::Tensor & div_sparse_(at::Tensor & self, const at::Tensor & other, c10::optional<c10::string_view> rounding_mode);
TORCH_API at::Tensor div(const at::Tensor & self, const at::Scalar & other);
TORCH_API at::Tensor & div_(at::Tensor & self, const at::Scalar & other);
TORCH_API at::Tensor div(const at::Tensor & self, const at::Scalar & other, c10::optional<c10::string_view> rounding_mode);
TORCH_API at::Tensor & div_(at::Tensor & self, const at::Scalar & other, c10::optional<c10::string_view> rounding_mode);
TORCH_API at::Tensor divide(const at::Tensor & self, const at::Tensor & other);
TORCH_API at::Tensor & divide_out(const at::Tensor & self, const at::Tensor & other, at::Tensor & out);
TORCH_API at::Tensor & divide_(at::Tensor & self, const at::Tensor & other);
TORCH_API at::Tensor divide(const at::Tensor & self, const at::Scalar & other);
TORCH_API at::Tensor & divide_(at::Tensor & self, const at::Scalar & other);
TORCH_API at::Tensor divide(const at::Tensor & self, const at::Tensor & other, c10::optional<c10::string_view> rounding_mode);
TORCH_API at::Tensor & divide_out(const at::Tensor & self, const at::Tensor & other, c10::optional<c10::string_view> rounding_mode, at::Tensor & out);
TORCH_API at::Tensor & divide_(at::Tensor & self, const at::Tensor & other, c10::optional<c10::string_view> rounding_mode);
TORCH_API at::Tensor divide(const at::Tensor & self, const at::Scalar & other, c10::optional<c10::string_view> rounding_mode);
TORCH_API at::Tensor & divide_(at::Tensor & self, const at::Scalar & other, c10::optional<c10::string_view> rounding_mode);
TORCH_API at::Tensor true_divide(const at::Tensor & self, const at::Tensor & other);
TORCH_API at::Tensor & true_divide_out(const at::Tensor & self, const at::Tensor & other, at::Tensor & out);
TORCH_API at::Tensor & true_divide_(at::Tensor & self, const at::Tensor & other);
TORCH_API at::Tensor true_divide(const at::Tensor & self, const at::Scalar & other);
TORCH_API at::Tensor & true_divide_(at::Tensor & self, const at::Scalar & other);
TORCH_API at::Tensor & dot_out(const at::Tensor & self, const at::Tensor & tensor, at::Tensor & out);
TORCH_API at::Tensor dot(const at::Tensor & self, const at::Tensor & tensor);
TORCH_API at::Tensor dot_cuda(const at::Tensor & self, const at::Tensor & tensor);
TORCH_API at::Tensor & vdot_out(const at::Tensor & self, const at::Tensor & other, at::Tensor & out);
TORCH_API at::Tensor vdot(const at::Tensor & self, const at::Tensor & other);
TORCH_API at::Tensor vdot_cuda(const at::Tensor & self, const at::Tensor & other);
TORCH_API at::Tensor einsum(c10::string_view equation, at::TensorList tensors);
TORCH_API at::Tensor embedding(const at::Tensor & weight, const at::Tensor & indices, int64_t padding_idx=-1, bool scale_grad_by_freq=false, bool sparse=false);
TORCH_API at::Tensor embedding_backward(const at::Tensor & grad, const at::Tensor & indices, int64_t num_weights, int64_t padding_idx, bool scale_grad_by_freq, bool sparse);
TORCH_API at::Tensor embedding_dense_backward_cpu(const at::Tensor & grad_output, const at::Tensor & indices, int64_t num_weights, int64_t padding_idx, bool scale_grad_by_freq);
TORCH_API at::Tensor embedding_dense_backward_cuda(const at::Tensor & grad_output, const at::Tensor & indices, int64_t num_weights, int64_t padding_idx, bool scale_grad_by_freq);
TORCH_API at::Tensor & embedding_renorm_cpu_(at::Tensor & self, const at::Tensor & indices, double max_norm, double norm_type);
TORCH_API at::Tensor & embedding_renorm_cuda_(at::Tensor & self, const at::Tensor & indices, double max_norm, double norm_type);
TORCH_API at::Tensor embedding_sparse_backward(const at::Tensor & grad, const at::Tensor & indices, int64_t num_weights, int64_t padding_idx, bool scale_grad_by_freq);
TORCH_API ::std::tuple<at::Tensor,at::Tensor,at::Tensor,at::Tensor> _embedding_bag_forward_only_cpu(const at::Tensor & weight, const at::Tensor & indices, const at::Tensor & offsets, bool scale_grad_by_freq=false, int64_t mode=0, bool sparse=false, const c10::optional<at::Tensor> & per_sample_weights={}, bool include_last_offset=false, int64_t padding_idx=-1);
TORCH_API ::std::tuple<at::Tensor,at::Tensor,at::Tensor,at::Tensor> _embedding_bag_forward_only_cuda(const at::Tensor & weight, const at::Tensor & indices, const at::Tensor & offsets, bool scale_grad_by_freq=false, int64_t mode=0, bool sparse=false, const c10::optional<at::Tensor> & per_sample_weights={}, bool include_last_offset=false, int64_t padding_idx=-1);
TORCH_API ::std::tuple<at::Tensor,at::Tensor> _rowwise_prune(const at::Tensor & weight, const at::Tensor & mask, at::ScalarType compressed_indices_dtype);
TORCH_API at::Tensor row_stack(at::TensorList tensors);
TORCH_API at::Tensor & row_stack_out(at::TensorList tensors, at::Tensor & out);
TORCH_API ::std::tuple<at::Tensor,at::Tensor,at::Tensor,at::Tensor> embedding_bag(const at::Tensor & weight, const at::Tensor & indices, const at::Tensor & offsets, bool scale_grad_by_freq=false, int64_t mode=0, bool sparse=false, const c10::optional<at::Tensor> & per_sample_weights={}, bool include_last_offset=false);
TORCH_API ::std::tuple<at::Tensor,at::Tensor,at::Tensor,at::Tensor> embedding_bag(const at::Tensor & weight, const at::Tensor & indices, const at::Tensor & offsets, bool scale_grad_by_freq, int64_t mode, bool sparse, const c10::optional<at::Tensor> & per_sample_weights, bool include_last_offset, c10::optional<int64_t> padding_idx);
TORCH_API ::std::tuple<at::Tensor,at::Tensor,at::Tensor,at::Tensor> _embedding_bag_cpu(const at::Tensor & weight, const at::Tensor & indices, const at::Tensor & offsets, bool scale_grad_by_freq=false, int64_t mode=0, bool sparse=false, const c10::optional<at::Tensor> & per_sample_weights={}, bool include_last_offset=false, int64_t padding_idx=-1);
TORCH_API ::std::tuple<at::Tensor,at::Tensor,at::Tensor,at::Tensor> _embedding_bag_cuda(const at::Tensor & weight, const at::Tensor & indices, const at::Tensor & offsets, bool scale_grad_by_freq=false, int64_t mode=0, bool sparse=false, const c10::optional<at::Tensor> & per_sample_weights={}, bool include_last_offset=false, int64_t padding_idx=-1);
TORCH_API at::Tensor _embedding_bag_backward(const at::Tensor & grad, const at::Tensor & indices, const at::Tensor & offsets, const at::Tensor & offset2bag, const at::Tensor & bag_size, const at::Tensor & maximum_indices, int64_t num_weights, bool scale_grad_by_freq, int64_t mode, bool sparse, const c10::optional<at::Tensor> & per_sample_weights, int64_t padding_idx=-1);
TORCH_API at::Tensor _embedding_bag_sparse_backward(const at::Tensor & grad, const at::Tensor & indices, const at::Tensor & offsets, const at::Tensor & offset2bag, const at::Tensor & bag_size, int64_t num_weights, bool scale_grad_by_freq, int64_t mode, const c10::optional<at::Tensor> & per_sample_weights, int64_t padding_idx=-1);
TORCH_API at::Tensor _embedding_bag_dense_backward_cpu(const at::Tensor & grad, const at::Tensor & indices, const at::Tensor & offset2bag, const at::Tensor & bag_size, const at::Tensor & maximum_indices, int64_t num_weights, bool scale_grad_by_freq, int64_t mode, const c10::optional<at::Tensor> & per_sample_weights, int64_t padding_idx=-1);
TORCH_API at::Tensor _embedding_bag_dense_backward_cuda(const at::Tensor & grad, const at::Tensor & indices, const at::Tensor & offset2bag, const at::Tensor & bag_size, const at::Tensor & maximum_indices, int64_t num_weights, bool scale_grad_by_freq, int64_t mode, const c10::optional<at::Tensor> & per_sample_weights, int64_t padding_idx=-1);
TORCH_API at::Tensor _embedding_bag_per_sample_weights_backward_cpu(const at::Tensor & grad, const at::Tensor & weight, const at::Tensor & indices, const at::Tensor & offsets, const at::Tensor & offset2bag, int64_t mode, int64_t padding_idx=-1);
TORCH_API at::Tensor _embedding_bag_per_sample_weights_backward_cuda(const at::Tensor & grad, const at::Tensor & weight, const at::Tensor & indices, const at::Tensor & offsets, const at::Tensor & offset2bag, int64_t mode, int64_t padding_idx=-1);
TORCH_API at::Tensor empty(at::IntArrayRef size, c10::optional<at::DimnameList> names, c10::optional<at::ScalarType> dtype={}, c10::optional<at::Layout> layout={}, c10::optional<at::Device> device={}, c10::optional<bool> pin_memory={}, c10::optional<at::MemoryFormat> memory_format=c10::nullopt);
TORCH_API at::Tensor empty_cpu(at::IntArrayRef size, c10::optional<at::ScalarType> dtype={}, c10::optional<at::Layout> layout={}, c10::optional<at::Device> device={}, c10::optional<bool> pin_memory={}, c10::optional<at::MemoryFormat> memory_format=c10::nullopt);
TORCH_API at::Tensor empty_cuda(at::IntArrayRef size, c10::optional<at::ScalarType> dtype={}, c10::optional<at::Layout> layout={}, c10::optional<at::Device> device={}, c10::optional<bool> pin_memory={}, c10::optional<at::MemoryFormat> memory_format=c10::nullopt);
TORCH_API at::Tensor empty_sparse(at::IntArrayRef size, c10::optional<at::ScalarType> dtype={}, c10::optional<at::Layout> layout={}, c10::optional<at::Device> device={}, c10::optional<bool> pin_memory={}, c10::optional<at::MemoryFormat> memory_format=c10::nullopt);
TORCH_API at::Tensor empty_mkldnn(at::IntArrayRef size, c10::optional<at::ScalarType> dtype={}, c10::optional<at::Layout> layout={}, c10::optional<at::Device> device={}, c10::optional<bool> pin_memory={}, c10::optional<at::MemoryFormat> memory_format=c10::nullopt);
TORCH_API at::Tensor empty_meta(at::IntArrayRef size, c10::optional<at::ScalarType> dtype={}, c10::optional<at::Layout> layout={}, c10::optional<at::Device> device={}, c10::optional<bool> pin_memory={}, c10::optional<at::MemoryFormat> memory_format=c10::nullopt);
TORCH_API at::Tensor new_empty(const at::Tensor & self, at::IntArrayRef size, c10::optional<at::ScalarType> dtype={}, c10::optional<at::Layout> layout={}, c10::optional<at::Device> device={}, c10::optional<bool> pin_memory={});
TORCH_API at::Tensor new_empty_strided(const at::Tensor & self, at::IntArrayRef size, at::IntArrayRef stride, c10::optional<at::ScalarType> dtype={}, c10::optional<at::Layout> layout={}, c10::optional<at::Device> device={}, c10::optional<bool> pin_memory={});
TORCH_API at::Tensor new_full(const at::Tensor & self, at::IntArrayRef size, const at::Scalar & fill_value, c10::optional<at::ScalarType> dtype={}, c10::optional<at::Layout> layout={}, c10::optional<at::Device> device={}, c10::optional<bool> pin_memory={});
TORCH_API at::Tensor new_zeros(const at::Tensor & self, at::IntArrayRef size, c10::optional<at::ScalarType> dtype={}, c10::optional<at::Layout> layout={}, c10::optional<at::Device> device={}, c10::optional<bool> pin_memory={});
TORCH_API at::Tensor new_ones(const at::Tensor & self, at::IntArrayRef size, c10::optional<at::ScalarType> dtype={}, c10::optional<at::Layout> layout={}, c10::optional<at::Device> device={}, c10::optional<bool> pin_memory={});
TORCH_API at::Tensor empty_affine_quantized_other_backends_stub(at::IntArrayRef size, c10::optional<at::ScalarType> dtype={}, c10::optional<at::Layout> layout={}, c10::optional<at::Device> device={}, c10::optional<bool> pin_memory={}, double scale=1, int64_t zero_point=0, c10::optional<at::MemoryFormat> memory_format=MemoryFormat::Contiguous);
TORCH_API at::Tensor empty_affine_quantized(at::IntArrayRef size, c10::optional<at::ScalarType> dtype={}, c10::optional<at::Layout> layout={}, c10::optional<at::Device> device={}, c10::optional<bool> pin_memory={}, double scale=1, int64_t zero_point=0, c10::optional<at::MemoryFormat> memory_format=MemoryFormat::Contiguous);
TORCH_API at::Tensor empty_per_channel_affine_quantized_other_backends_stub(at::IntArrayRef size, const at::Tensor & scales, const at::Tensor & zero_points, int64_t axis, c10::optional<at::ScalarType> dtype={}, c10::optional<at::Layout> layout={}, c10::optional<at::Device> device={}, c10::optional<bool> pin_memory={}, c10::optional<at::MemoryFormat> memory_format=MemoryFormat::Contiguous);
TORCH_API at::Tensor empty_per_channel_affine_quantized(at::IntArrayRef size, const at::Tensor & scales, const at::Tensor & zero_points, int64_t axis, c10::optional<at::ScalarType> dtype={}, c10::optional<at::Layout> layout={}, c10::optional<at::Device> device={}, c10::optional<bool> pin_memory={}, c10::optional<at::MemoryFormat> memory_format=MemoryFormat::Contiguous);
TORCH_API const at::Tensor & resize_(const at::Tensor & self, at::IntArrayRef size, c10::optional<at::MemoryFormat> memory_format=c10::nullopt);
TORCH_API const at::Tensor & resize_cuda_(const at::Tensor & self, at::IntArrayRef size, c10::optional<at::MemoryFormat> memory_format=c10::nullopt);
TORCH_API const at::Tensor & quantized_resize_cpu_(const at::Tensor & self, at::IntArrayRef size, c10::optional<at::MemoryFormat> memory_format=c10::nullopt);
TORCH_API at::Tensor empty_quantized(at::IntArrayRef size, const at::Tensor & qtensor, c10::optional<at::ScalarType> dtype={}, c10::optional<at::Layout> layout={}, c10::optional<at::Device> device={}, c10::optional<bool> pin_memory={}, c10::optional<at::MemoryFormat> memory_format=c10::nullopt);
TORCH_API at::Tensor & empty_out(at::IntArrayRef size, c10::optional<at::MemoryFormat> memory_format, at::Tensor & out);
TORCH_API at::Tensor empty_like(const at::Tensor & self, c10::optional<at::ScalarType> dtype={}, c10::optional<at::Layout> layout={}, c10::optional<at::Device> device={}, c10::optional<bool> pin_memory={}, c10::optional<at::MemoryFormat> memory_format=c10::nullopt);
TORCH_API at::Tensor empty_strided_cpu(at::IntArrayRef size, at::IntArrayRef stride, c10::optional<at::ScalarType> dtype={}, c10::optional<at::Layout> layout={}, c10::optional<at::Device> device={}, c10::optional<bool> pin_memory={});
TORCH_API at::Tensor empty_strided_cuda(at::IntArrayRef size, at::IntArrayRef stride, c10::optional<at::ScalarType> dtype={}, c10::optional<at::Layout> layout={}, c10::optional<at::Device> device={}, c10::optional<bool> pin_memory={});
TORCH_API at::Tensor empty_strided_meta(at::IntArrayRef size, at::IntArrayRef stride, c10::optional<at::ScalarType> dtype={}, c10::optional<at::Layout> layout={}, c10::optional<at::Device> device={}, c10::optional<bool> pin_memory={});
struct TORCH_API structured_erf_out : public at::meta::structured_erf {
void impl(const at::Tensor & self, const at::Tensor & out);
};
struct TORCH_API structured_erfc_out : public at::meta::structured_erfc {
void impl(const at::Tensor & self, const at::Tensor & out);
};
struct TORCH_API structured_exp_out : public at::meta::structured_exp {
void impl(const at::Tensor & self, const at::Tensor & out);
};
struct TORCH_API structured_exp2_out : public at::meta::structured_exp2 {
void impl(const at::Tensor & self, const at::Tensor & out);
};
struct TORCH_API structured_expm1_out : public at::meta::structured_expm1 {
void impl(const at::Tensor & self, const at::Tensor & out);
};
TORCH_API at::Tensor expand(const at::Tensor & self, at::IntArrayRef size, bool implicit=false);
TORCH_API at::Tensor expand_as(const at::Tensor & self, const at::Tensor & other);
TORCH_API at::Tensor eye(int64_t n, c10::optional<at::ScalarType> dtype={}, c10::optional<at::Layout> layout={}, c10::optional<at::Device> device={}, c10::optional<bool> pin_memory={});
TORCH_API at::Tensor eye(int64_t n, int64_t m, c10::optional<at::ScalarType> dtype={}, c10::optional<at::Layout> layout={}, c10::optional<at::Device> device={}, c10::optional<bool> pin_memory={});
TORCH_API at::Tensor & eye_out_cpu(int64_t n, at::Tensor & out);
TORCH_API at::Tensor & eye_out_cuda(int64_t n, at::Tensor & out);
TORCH_API at::Tensor & eye_out_cpu(int64_t n, int64_t m, at::Tensor & out);
TORCH_API at::Tensor & eye_out_cuda(int64_t n, int64_t m, at::Tensor & out);
TORCH_API at::Tensor flatten(const at::Tensor & self, int64_t start_dim=0, int64_t end_dim=-1);
TORCH_API at::Tensor flatten(const at::Tensor & self, int64_t start_dim, int64_t end_dim, at::Dimname out_dim);
TORCH_API at::Tensor flatten(const at::Tensor & self, at::Dimname start_dim, at::Dimname end_dim, at::Dimname out_dim);
TORCH_API at::Tensor flatten(const at::Tensor & self, at::DimnameList dims, at::Dimname out_dim);
TORCH_API at::Tensor unflatten(const at::Tensor & self, int64_t dim, at::IntArrayRef sizes, c10::optional<at::DimnameList> names=c10::nullopt);
TORCH_API at::Tensor unflatten(const at::Tensor & self, at::Dimname dim, at::IntArrayRef sizes, at::DimnameList names);
TORCH_API at::Tensor & fill_(at::Tensor & self, const at::Scalar & value);
TORCH_API at::Tensor & fill_meta_(at::Tensor & self, const at::Scalar & value);
TORCH_API at::Tensor & fill_(at::Tensor & self, const at::Tensor & value);
TORCH_API at::Tensor & fill_meta_(at::Tensor & self, const at::Tensor & value);
TORCH_API at::Tensor floor(const at::Tensor & self);
TORCH_API at::Tensor & floor_(at::Tensor & self);
struct TORCH_API structured_floor_out : public at::meta::structured_floor {
void impl(const at::Tensor & self, const at::Tensor & out);
};
TORCH_API at::Tensor floor_divide(const at::Tensor & self, const at::Tensor & other);
TORCH_API at::Tensor & floor_divide_out(const at::Tensor & self, const at::Tensor & other, at::Tensor & out);
TORCH_API at::Tensor & floor_divide_(at::Tensor & self, const at::Tensor & other);
TORCH_API at::Tensor floor_divide_sparse(const at::Tensor & self, const at::Tensor & other);
TORCH_API at::Tensor & floor_divide_out_sparse_zerodim(const at::Tensor & self, const at::Tensor & other, at::Tensor & out);
TORCH_API at::Tensor & floor_divide_sparse_(at::Tensor & self, const at::Tensor & other);
TORCH_API at::Tensor floor_divide(const at::Tensor & self, const at::Scalar & other);
TORCH_API at::Tensor & floor_divide_(at::Tensor & self, const at::Scalar & other);
struct TORCH_API structured_frac_out : public at::meta::structured_frac {
void impl(const at::Tensor & self, const at::Tensor & out);
};
TORCH_API at::Tensor full(at::IntArrayRef size, const at::Scalar & fill_value, c10::optional<at::DimnameList> names, c10::optional<at::ScalarType> dtype={}, c10::optional<at::Layout> layout={}, c10::optional<at::Device> device={}, c10::optional<bool> pin_memory={});
TORCH_API at::Tensor full(at::IntArrayRef size, const at::Scalar & fill_value, c10::optional<at::ScalarType> dtype={}, c10::optional<at::Layout> layout={}, c10::optional<at::Device> device={}, c10::optional<bool> pin_memory={});
TORCH_API at::Tensor & full_out(at::IntArrayRef size, const at::Scalar & fill_value, at::Tensor & out);
TORCH_API at::Tensor full_like(const at::Tensor & self, const at::Scalar & fill_value, c10::optional<at::ScalarType> dtype={}, c10::optional<at::Layout> layout={}, c10::optional<at::Device> device={}, c10::optional<bool> pin_memory={}, c10::optional<at::MemoryFormat> memory_format=c10::nullopt);
TORCH_API at::Tensor from_file(c10::string_view filename, c10::optional<bool> shared=c10::nullopt, c10::optional<int64_t> size=0, c10::optional<at::ScalarType> dtype={}, c10::optional<at::Layout> layout={}, c10::optional<at::Device> device={}, c10::optional<bool> pin_memory={});
struct TORCH_API structured_gcd_out : public at::meta::structured_gcd {
void impl(const at::Tensor & self, const at::Tensor & other, const at::Tensor & out);
};
struct TORCH_API structured_lcm_out : public at::meta::structured_lcm {
void impl(const at::Tensor & self, const at::Tensor & other, const at::Tensor & out);
};
TORCH_API at::Tensor grid_sampler(const at::Tensor & input, const at::Tensor & grid, int64_t interpolation_mode, int64_t padding_mode, bool align_corners);
TORCH_API at::Tensor grid_sampler_2d_cpu(const at::Tensor & input, const at::Tensor & grid, int64_t interpolation_mode, int64_t padding_mode, bool align_corners);
TORCH_API at::Tensor grid_sampler_2d_cuda(const at::Tensor & input, const at::Tensor & grid, int64_t interpolation_mode, int64_t padding_mode, bool align_corners);
TORCH_API ::std::tuple<at::Tensor,at::Tensor> grid_sampler_2d_backward_cpu(const at::Tensor & grad_output, const at::Tensor & input, const at::Tensor & grid, int64_t interpolation_mode, int64_t padding_mode, bool align_corners);
TORCH_API ::std::tuple<at::Tensor,at::Tensor> grid_sampler_2d_backward_cuda(const at::Tensor & grad_output, const at::Tensor & input, const at::Tensor & grid, int64_t interpolation_mode, int64_t padding_mode, bool align_corners);
TORCH_API at::Tensor _grid_sampler_2d_cpu_fallback(const at::Tensor & input, const at::Tensor & grid, int64_t interpolation_mode, int64_t padding_mode, bool align_corners);
TORCH_API ::std::tuple<at::Tensor,at::Tensor> _grid_sampler_2d_cpu_fallback_backward(const at::Tensor & grad_output, const at::Tensor & input, const at::Tensor & grid, int64_t interpolation_mode, int64_t padding_mode, bool align_corners);
TORCH_API at::Tensor grid_sampler_3d_cpu(const at::Tensor & input, const at::Tensor & grid, int64_t interpolation_mode, int64_t padding_mode, bool align_corners);
TORCH_API at::Tensor grid_sampler_3d_cuda(const at::Tensor & input, const at::Tensor & grid, int64_t interpolation_mode, int64_t padding_mode, bool align_corners);
TORCH_API ::std::tuple<at::Tensor,at::Tensor> grid_sampler_3d_backward_cpu(const at::Tensor & grad_output, const at::Tensor & input, const at::Tensor & grid, int64_t interpolation_mode, int64_t padding_mode, bool align_corners);
TORCH_API ::std::tuple<at::Tensor,at::Tensor> grid_sampler_3d_backward_cuda(const at::Tensor & grad_output, const at::Tensor & input, const at::Tensor & grid, int64_t interpolation_mode, int64_t padding_mode, bool align_corners);
TORCH_API at::Tensor hann_window(int64_t window_length, c10::optional<at::ScalarType> dtype={}, c10::optional<at::Layout> layout={}, c10::optional<at::Device> device={}, c10::optional<bool> pin_memory={});
TORCH_API at::Tensor hann_window(int64_t window_length, bool periodic, c10::optional<at::ScalarType> dtype={}, c10::optional<at::Layout> layout={}, c10::optional<at::Device> device={}, c10::optional<bool> pin_memory={});
TORCH_API at::Tensor hamming_window(int64_t window_length, c10::optional<at::ScalarType> dtype={}, c10::optional<at::Layout> layout={}, c10::optional<at::Device> device={}, c10::optional<bool> pin_memory={});
TORCH_API at::Tensor hamming_window(int64_t window_length, bool periodic, c10::optional<at::ScalarType> dtype={}, c10::optional<at::Layout> layout={}, c10::optional<at::Device> device={}, c10::optional<bool> pin_memory={});
TORCH_API at::Tensor hamming_window(int64_t window_length, bool periodic, double alpha, c10::optional<at::ScalarType> dtype={}, c10::optional<at::Layout> layout={}, c10::optional<at::Device> device={}, c10::optional<bool> pin_memory={});
TORCH_API at::Tensor hamming_window(int64_t window_length, bool periodic, double alpha, double beta, c10::optional<at::ScalarType> dtype={}, c10::optional<at::Layout> layout={}, c10::optional<at::Device> device={}, c10::optional<bool> pin_memory={});
TORCH_API at::Tensor kaiser_window(int64_t window_length, c10::optional<at::ScalarType> dtype={}, c10::optional<at::Layout> layout={}, c10::optional<at::Device> device={}, c10::optional<bool> pin_memory={});
TORCH_API at::Tensor kaiser_window(int64_t window_length, bool periodic, c10::optional<at::ScalarType> dtype={}, c10::optional<at::Layout> layout={}, c10::optional<at::Device> device={}, c10::optional<bool> pin_memory={});
TORCH_API at::Tensor kaiser_window(int64_t window_length, bool periodic, double beta, c10::optional<at::ScalarType> dtype={}, c10::optional<at::Layout> layout={}, c10::optional<at::Device> device={}, c10::optional<bool> pin_memory={});
TORCH_API at::Tensor hinge_embedding_loss(const at::Tensor & self, const at::Tensor & target, double margin=1.0, int64_t reduction=at::Reduction::Mean);
TORCH_API at::Tensor group_norm(const at::Tensor & input, int64_t num_groups, const c10::optional<at::Tensor> & weight={}, const c10::optional<at::Tensor> & bias={}, double eps=1e-05, bool cudnn_enabled=true);
TORCH_API ::std::tuple<at::Tensor,at::Tensor,at::Tensor> math_group_norm(const at::Tensor & input, const c10::optional<at::Tensor> & weight, const c10::optional<at::Tensor> & bias, int64_t N, int64_t C, int64_t HxW, int64_t group, double eps);
TORCH_API ::std::tuple<at::Tensor,at::Tensor,at::Tensor> native_group_norm(const at::Tensor & input, const c10::optional<at::Tensor> & weight, const c10::optional<at::Tensor> & bias, int64_t N, int64_t C, int64_t HxW, int64_t group, double eps);
TORCH_API ::std::tuple<at::Tensor,at::Tensor,at::Tensor> native_group_norm_backward(const at::Tensor & grad_out, const at::Tensor & input, const at::Tensor & mean, const at::Tensor & rstd, const c10::optional<at::Tensor> & weight, int64_t N, int64_t C, int64_t HxW, int64_t group, ::std::array<bool,3> output_mask);
TORCH_API at::Tensor _fft_r2c_mkl(const at::Tensor & self, at::IntArrayRef dim, int64_t normalization, bool onesided);
TORCH_API at::Tensor & _fft_r2c_mkl_out(const at::Tensor & self, at::IntArrayRef dim, int64_t normalization, bool onesided, at::Tensor & out);
TORCH_API at::Tensor _fft_r2c_cufft(const at::Tensor & self, at::IntArrayRef dim, int64_t normalization, bool onesided);
TORCH_API at::Tensor & _fft_r2c_cufft_out(const at::Tensor & self, at::IntArrayRef dim, int64_t normalization, bool onesided, at::Tensor & out);
TORCH_API at::Tensor _fft_c2r_mkl(const at::Tensor & self, at::IntArrayRef dim, int64_t normalization, int64_t last_dim_size);
TORCH_API at::Tensor & _fft_c2r_mkl_out(const at::Tensor & self, at::IntArrayRef dim, int64_t normalization, int64_t last_dim_size, at::Tensor & out);
TORCH_API at::Tensor _fft_c2r_cufft(const at::Tensor & self, at::IntArrayRef dim, int64_t normalization, int64_t last_dim_size);
TORCH_API at::Tensor & _fft_c2r_cufft_out(const at::Tensor & self, at::IntArrayRef dim, int64_t normalization, int64_t last_dim_size, at::Tensor & out);
TORCH_API at::Tensor _fft_c2c_mkl(const at::Tensor & self, at::IntArrayRef dim, int64_t normalization, bool forward);
TORCH_API at::Tensor & _fft_c2c_mkl_out(const at::Tensor & self, at::IntArrayRef dim, int64_t normalization, bool forward, at::Tensor & out);
TORCH_API at::Tensor _fft_c2c_cufft(const at::Tensor & self, at::IntArrayRef dim, int64_t normalization, bool forward);
TORCH_API at::Tensor & _fft_c2c_cufft_out(const at::Tensor & self, at::IntArrayRef dim, int64_t normalization, bool forward, at::Tensor & out);
TORCH_API int64_t _cufft_get_plan_cache_size(int64_t device_index);
TORCH_API int64_t _cufft_get_plan_cache_max_size(int64_t device_index);
TORCH_API void _cufft_set_plan_cache_max_size(int64_t device_index, int64_t max_size);
TORCH_API void _cufft_clear_plan_cache(int64_t device_index);
TORCH_API at::Tensor index(const at::Tensor & self, const c10::List<c10::optional<at::Tensor>> & indices);
TORCH_API at::Tensor quantized_index(const at::Tensor & self, const c10::List<c10::optional<at::Tensor>> & indices);
TORCH_API at::Tensor & index_copy_(at::Tensor & self, int64_t dim, const at::Tensor & index, const at::Tensor & source);
TORCH_API at::Tensor index_copy(const at::Tensor & self, int64_t dim, const at::Tensor & index, const at::Tensor & source);
TORCH_API at::Tensor & index_copy_(at::Tensor & self, at::Dimname dim, const at::Tensor & index, const at::Tensor & source);
TORCH_API at::Tensor index_copy(const at::Tensor & self, at::Dimname dim, const at::Tensor & index, const at::Tensor & source);
TORCH_API at::Tensor & index_put_(at::Tensor & self, const c10::List<c10::optional<at::Tensor>> & indices, const at::Tensor & values, bool accumulate=false);
TORCH_API at::Tensor index_put(const at::Tensor & self, const c10::List<c10::optional<at::Tensor>> & indices, const at::Tensor & values, bool accumulate=false);
TORCH_API at::Tensor & _index_put_impl_(at::Tensor & self, const c10::List<c10::optional<at::Tensor>> & indices, const at::Tensor & values, bool accumulate=false, bool unsafe=false);
TORCH_API at::Tensor instance_norm(const at::Tensor & input, const c10::optional<at::Tensor> & weight, const c10::optional<at::Tensor> & bias, const c10::optional<at::Tensor> & running_mean, const c10::optional<at::Tensor> & running_var, bool use_input_stats, double momentum, double eps, bool cudnn_enabled);
TORCH_API at::Tensor inverse(const at::Tensor & self);
TORCH_API at::Tensor & inverse_out(const at::Tensor & self, at::Tensor & out);
TORCH_API at::Tensor _inverse_helper_cpu(const at::Tensor & self);
TORCH_API at::Tensor _inverse_helper_cuda(const at::Tensor & self);
TORCH_API at::Tensor isclose(const at::Tensor & self, const at::Tensor & other, double rtol=1e-05, double atol=1e-08, bool equal_nan=false);
struct TORCH_API structured_isin_Tensor_Tensor_out : public at::meta::structured_isin_Tensor_Tensor {
void impl(const at::Tensor & elements, const at::Tensor & test_elements, bool assume_unique, bool invert, const at::Tensor & out);
};
struct TORCH_API structured_isin_Tensor_Scalar_out : public at::meta::structured_isin_Tensor_Scalar {
void impl(const at::Tensor & elements, const at::Scalar & test_element, bool assume_unique, bool invert, const at::Tensor & out);
};
struct TORCH_API structured_isin_Scalar_Tensor_out : public at::meta::structured_isin_Scalar_Tensor {
void impl(const at::Scalar & element, const at::Tensor & test_elements, bool assume_unique, bool invert, const at::Tensor & out);
};
TORCH_API at::Tensor isnan(const at::Tensor & self);
TORCH_API at::Tensor isnan_sparse(const at::Tensor & self);
TORCH_API bool is_distributed(const at::Tensor & self);
TORCH_API bool is_floating_point(const at::Tensor & self);
TORCH_API bool is_complex(const at::Tensor & self);
TORCH_API bool is_conj(const at::Tensor & self);
TORCH_API bool is_neg(const at::Tensor & self);
TORCH_API at::Tensor isreal(const at::Tensor & self);
TORCH_API bool is_nonzero(const at::Tensor & self);
TORCH_API bool is_same_size(const at::Tensor & self, const at::Tensor & other);
TORCH_API bool is_signed(const at::Tensor & self);
TORCH_API bool is_inference(const at::Tensor & self);
TORCH_API at::Tensor kl_div(const at::Tensor & self, const at::Tensor & target, int64_t reduction=at::Reduction::Mean, bool log_target=false);
TORCH_API at::Tensor kl_div_backward_cpu(const at::Tensor & grad_output, const at::Tensor & self, const at::Tensor & target, int64_t reduction=at::Reduction::Mean, bool log_target=false);
TORCH_API at::Tensor kl_div_backward_cuda(const at::Tensor & grad_output, const at::Tensor & self, const at::Tensor & target, int64_t reduction=at::Reduction::Mean, bool log_target=false);
TORCH_API at::Tensor kron(const at::Tensor & self, const at::Tensor & other);
TORCH_API at::Tensor & kron_out(const at::Tensor & self, const at::Tensor & other, at::Tensor & out);
TORCH_API ::std::tuple<at::Tensor,at::Tensor> kthvalue(const at::Tensor & self, int64_t k, int64_t dim=-1, bool keepdim=false);
TORCH_API ::std::tuple<at::Tensor &,at::Tensor &> kthvalue_out_cpu(const at::Tensor & self, int64_t k, int64_t dim, bool keepdim, at::Tensor & values, at::Tensor & indices);
TORCH_API ::std::tuple<at::Tensor &,at::Tensor &> kthvalue_out_cuda(const at::Tensor & self, int64_t k, int64_t dim, bool keepdim, at::Tensor & values, at::Tensor & indices);
TORCH_API ::std::tuple<at::Tensor,at::Tensor> kthvalue(const at::Tensor & self, int64_t k, at::Dimname dim, bool keepdim=false);
TORCH_API ::std::tuple<at::Tensor &,at::Tensor &> kthvalue_out(const at::Tensor & self, int64_t k, at::Dimname dim, bool keepdim, at::Tensor & values, at::Tensor & indices);
TORCH_API at::Tensor layer_norm(const at::Tensor & input, at::IntArrayRef normalized_shape, const c10::optional<at::Tensor> & weight={}, const c10::optional<at::Tensor> & bias={}, double eps=1e-05, bool cudnn_enable=true);
TORCH_API ::std::tuple<at::Tensor,at::Tensor,at::Tensor> math_native_layer_norm(const at::Tensor & input, at::IntArrayRef normalized_shape, const c10::optional<at::Tensor> & weight, const c10::optional<at::Tensor> & bias, double eps);
TORCH_API ::std::tuple<at::Tensor,at::Tensor,at::Tensor> layer_norm_cpu(const at::Tensor & input, at::IntArrayRef normalized_shape, const c10::optional<at::Tensor> & weight, const c10::optional<at::Tensor> & bias, double eps);
TORCH_API ::std::tuple<at::Tensor,at::Tensor,at::Tensor> layer_norm_cuda(const at::Tensor & input, at::IntArrayRef normalized_shape, const c10::optional<at::Tensor> & weight, const c10::optional<at::Tensor> & bias, double eps);
TORCH_API ::std::tuple<at::Tensor,at::Tensor,at::Tensor> layer_norm_backward_cpu(const at::Tensor & grad_out, const at::Tensor & input, at::IntArrayRef normalized_shape, const at::Tensor & mean, const at::Tensor & rstd, const c10::optional<at::Tensor> & weight, const c10::optional<at::Tensor> & bias, ::std::array<bool,3> output_mask);
TORCH_API ::std::tuple<at::Tensor,at::Tensor,at::Tensor> layer_norm_backward_cuda(const at::Tensor & grad_out, const at::Tensor & input, at::IntArrayRef normalized_shape, const at::Tensor & mean, const at::Tensor & rstd, const c10::optional<at::Tensor> & weight, const c10::optional<at::Tensor> & bias, ::std::array<bool,3> output_mask);
TORCH_API at::Tensor nan_to_num(const at::Tensor & self, c10::optional<double> nan=c10::nullopt, c10::optional<double> posinf=c10::nullopt, c10::optional<double> neginf=c10::nullopt);
TORCH_API at::Tensor & nan_to_num_(at::Tensor & self, c10::optional<double> nan=c10::nullopt, c10::optional<double> posinf=c10::nullopt, c10::optional<double> neginf=c10::nullopt);
TORCH_API at::Tensor & nan_to_num_out(const at::Tensor & self, c10::optional<double> nan, c10::optional<double> posinf, c10::optional<double> neginf, at::Tensor & out);
TORCH_API at::Tensor linear(const at::Tensor & input, const at::Tensor & weight, const c10::optional<at::Tensor> & bias={});
TORCH_API at::Tensor & linear_out(const at::Tensor & input, const at::Tensor & weight, const c10::optional<at::Tensor> & bias, at::Tensor & out);
TORCH_API at::Tensor mkldnn_linear(const at::Tensor & self, const at::Tensor & weight, const c10::optional<at::Tensor> & bias={});
TORCH_API at::Tensor mkldnn_linear_backward_input(at::IntArrayRef input_size, const at::Tensor & grad_output, const at::Tensor & weight);
TORCH_API ::std::tuple<at::Tensor,at::Tensor> mkldnn_linear_backward_weights(const at::Tensor & grad_output, const at::Tensor & input, const at::Tensor & weight, bool bias_defined);
TORCH_API ::std::tuple<at::Tensor,at::Tensor,at::Tensor> mkldnn_linear_backward(const at::Tensor & self, const at::Tensor & grad_output, const at::Tensor & weight, ::std::array<bool,3> output_mask);
TORCH_API at::Tensor fbgemm_linear_int8_weight_fp32_activation(const at::Tensor & input, const at::Tensor & weight, const at::Tensor & packed, const at::Tensor & col_offsets, const at::Scalar & weight_scale, const at::Scalar & weight_zero_point, const at::Tensor & bias);
TORCH_API at::Tensor fbgemm_linear_int8_weight(const at::Tensor & input, const at::Tensor & weight, const at::Tensor & packed, const at::Tensor & col_offsets, const at::Scalar & weight_scale, const at::Scalar & weight_zero_point, const at::Tensor & bias);
TORCH_API ::std::tuple<at::Tensor,at::Tensor,double,int64_t> fbgemm_linear_quantize_weight(const at::Tensor & input);
TORCH_API at::Tensor fbgemm_pack_gemm_matrix_fp16(const at::Tensor & input);
TORCH_API at::Tensor fbgemm_linear_fp16_weight_fp32_activation(const at::Tensor & input, const at::Tensor & packed_weight, const at::Tensor & bias);
TORCH_API at::Tensor fbgemm_linear_fp16_weight(const at::Tensor & input, const at::Tensor & packed_weight, const at::Tensor & bias);
TORCH_API at::Tensor fbgemm_pack_quantized_matrix(const at::Tensor & input);
TORCH_API at::Tensor fbgemm_pack_quantized_matrix(const at::Tensor & input, int64_t K, int64_t N);
TORCH_API at::Tensor ldexp(const at::Tensor & self, const at::Tensor & other);
TORCH_API at::Tensor & ldexp_out(const at::Tensor & self, const at::Tensor & other, at::Tensor & out);
TORCH_API at::Tensor & ldexp_(at::Tensor & self, const at::Tensor & other);
TORCH_API at::Tensor linspace(const at::Scalar & start, const at::Scalar & end, c10::optional<int64_t> steps=c10::nullopt, c10::optional<at::ScalarType> dtype={}, c10::optional<at::Layout> layout={}, c10::optional<at::Device> device={}, c10::optional<bool> pin_memory={});
TORCH_API at::Tensor & linspace_cpu_out(const at::Scalar & start, const at::Scalar & end, c10::optional<int64_t> steps, at::Tensor & out);
TORCH_API at::Tensor & linspace_cuda_out(const at::Scalar & start, const at::Scalar & end, c10::optional<int64_t> steps, at::Tensor & out);
struct TORCH_API structured_log_out : public at::meta::structured_log {
void impl(const at::Tensor & self, const at::Tensor & out);
};
struct TORCH_API structured_log10_out : public at::meta::structured_log10 {
void impl(const at::Tensor & self, const at::Tensor & out);
};
struct TORCH_API structured_log1p_out : public at::meta::structured_log1p {
void impl(const at::Tensor & self, const at::Tensor & out);
};
TORCH_API at::Tensor log1p_sparse(const at::Tensor & self);
TORCH_API at::Tensor & log1p_out_sparse(const at::Tensor & self, at::Tensor & out);
TORCH_API at::Tensor & log1p_sparse_(at::Tensor & self);
struct TORCH_API structured_log2_out : public at::meta::structured_log2 {
void impl(const at::Tensor & self, const at::Tensor & out);
};
TORCH_API at::Tensor logaddexp(const at::Tensor & self, const at::Tensor & other);
struct TORCH_API structured_logaddexp_out : public at::meta::structured_logaddexp {
void impl(const at::Tensor & self, const at::Tensor & other, const at::Tensor & out);
};
TORCH_API at::Tensor logaddexp2(const at::Tensor & self, const at::Tensor & other);
struct TORCH_API structured_logaddexp2_out : public at::meta::structured_logaddexp2 {
void impl(const at::Tensor & self, const at::Tensor & other, const at::Tensor & out);
};
struct TORCH_API structured_xlogy_out : public at::meta::structured_xlogy_Tensor {
void impl(const at::Tensor & self, const at::Tensor & other, const at::Tensor & out);
};
TORCH_API at::Tensor xlogy(const at::Scalar & self, const at::Tensor & other);
TORCH_API at::Tensor & xlogy_out(const at::Scalar & self, const at::Tensor & other, at::Tensor & out);
TORCH_API at::Tensor xlogy(const at::Tensor & self, const at::Scalar & other);
TORCH_API at::Tensor & xlogy_out(const at::Tensor & self, const at::Scalar & other, at::Tensor & out);
TORCH_API at::Tensor & xlogy_(at::Tensor & self, const at::Scalar & other);
TORCH_API at::Tensor logdet(const at::Tensor & self);
TORCH_API at::Tensor logspace(const at::Scalar & start, const at::Scalar & end, c10::optional<int64_t> steps=c10::nullopt, double base=10.0, c10::optional<at::ScalarType> dtype={}, c10::optional<at::Layout> layout={}, c10::optional<at::Device> device={}, c10::optional<bool> pin_memory={});
TORCH_API at::Tensor & logspace_cpu_out(const at::Scalar & start, const at::Scalar & end, c10::optional<int64_t> steps, double base, at::Tensor & out);
TORCH_API at::Tensor & logspace_cuda_out(const at::Scalar & start, const at::Scalar & end, c10::optional<int64_t> steps, double base, at::Tensor & out);
TORCH_API at::Tensor log_softmax(const at::Tensor & self, int64_t dim, c10::optional<at::ScalarType> dtype=c10::nullopt);
TORCH_API at::Tensor log_softmax(const at::Tensor & self, at::Dimname dim, c10::optional<at::ScalarType> dtype=c10::nullopt);
struct TORCH_API structured_log_softmax_cpu_out : public at::meta::structured__log_softmax {
void impl(const at::Tensor & self, int64_t dim, bool half_to_float, const at::Tensor & out);
};
struct TORCH_API structured_log_softmax_cuda_out : public at::meta::structured__log_softmax {
void impl(const at::Tensor & self, int64_t dim, bool half_to_float, const at::Tensor & out);
};
struct TORCH_API structured_log_softmax_backward_cpu_out : public at::meta::structured__log_softmax_backward_data {
void impl(const at::Tensor & grad_output, const at::Tensor & output, int64_t dim, const at::Tensor & self, const at::Tensor & out);
};
struct TORCH_API structured_log_softmax_backward_cuda_out : public at::meta::structured__log_softmax_backward_data {
void impl(const at::Tensor & grad_output, const at::Tensor & output, int64_t dim, const at::Tensor & self, const at::Tensor & out);
};
TORCH_API at::Tensor _logcumsumexp_cpu(const at::Tensor & self, int64_t dim);
TORCH_API at::Tensor & _logcumsumexp_out_cpu(const at::Tensor & self, int64_t dim, at::Tensor & out);
TORCH_API at::Tensor _logcumsumexp_cuda(const at::Tensor & self, int64_t dim);
TORCH_API at::Tensor & _logcumsumexp_out_cuda(const at::Tensor & self, int64_t dim, at::Tensor & out);
TORCH_API at::Tensor logcumsumexp(const at::Tensor & self, int64_t dim);
TORCH_API at::Tensor & logcumsumexp_out(const at::Tensor & self, int64_t dim, at::Tensor & out);
TORCH_API at::Tensor logcumsumexp(const at::Tensor & self, at::Dimname dim);
TORCH_API at::Tensor & logcumsumexp_out(const at::Tensor & self, at::Dimname dim, at::Tensor & out);
TORCH_API at::Tensor logsumexp(const at::Tensor & self, at::IntArrayRef dim, bool keepdim=false);
TORCH_API at::Tensor & logsumexp_out(const at::Tensor & self, at::IntArrayRef dim, bool keepdim, at::Tensor & out);
TORCH_API at::Tensor logsumexp(const at::Tensor & self, at::DimnameList dim, bool keepdim=false);
TORCH_API at::Tensor & logsumexp_out(const at::Tensor & self, at::DimnameList dim, bool keepdim, at::Tensor & out);
TORCH_API at::Tensor margin_ranking_loss(const at::Tensor & input1, const at::Tensor & input2, const at::Tensor & target, double margin=0.0, int64_t reduction=at::Reduction::Mean);
TORCH_API at::Tensor matmul(const at::Tensor & self, const at::Tensor & other);
TORCH_API at::Tensor & matmul_out(const at::Tensor & self, const at::Tensor & other, at::Tensor & out);
TORCH_API at::Tensor matrix_rank(const at::Tensor & self, double tol, bool symmetric=false);
TORCH_API at::Tensor matrix_rank(const at::Tensor & self, bool symmetric=false);
TORCH_API at::Tensor matrix_power(const at::Tensor & self, int64_t n);
TORCH_API at::Tensor & matrix_power_out(const at::Tensor & self, int64_t n, at::Tensor & out);
TORCH_API at::Tensor matrix_exp(const at::Tensor & self);
TORCH_API at::Tensor matrix_exp_backward(const at::Tensor & self, const at::Tensor & grad);
TORCH_API ::std::tuple<at::Tensor,at::Tensor> _aminmax_all(const at::Tensor & self);
TORCH_API ::std::tuple<at::Tensor,at::Tensor> _aminmax(const at::Tensor & self, int64_t dim, bool keepdim=false);
struct TORCH_API structured_aminmax_out : public at::meta::structured_aminmax {
void impl(const at::Tensor & self, c10::optional<int64_t> dim, bool keepdim, const at::Tensor & min, const at::Tensor & max);
};
TORCH_API at::Tensor _compute_linear_combination(const at::Tensor & input, const at::Tensor & coefficients);
TORCH_API at::Tensor & _compute_linear_combination_out(const at::Tensor & input, const at::Tensor & coefficients, at::Tensor & out);
TORCH_API ::std::tuple<at::Tensor,at::Tensor> max(const at::Tensor & self, int64_t dim, bool keepdim=false);
TORCH_API ::std::tuple<at::Tensor &,at::Tensor &> max_out(const at::Tensor & self, int64_t dim, bool keepdim, at::Tensor & max, at::Tensor & max_values);
TORCH_API ::std::tuple<at::Tensor,at::Tensor> max(const at::Tensor & self, at::Dimname dim, bool keepdim=false);
TORCH_API ::std::tuple<at::Tensor &,at::Tensor &> max_out(const at::Tensor & self, at::Dimname dim, bool keepdim, at::Tensor & max, at::Tensor & max_values);
TORCH_API at::Tensor value_selecting_reduction_backward(const at::Tensor & grad, int64_t dim, const at::Tensor & indices, at::IntArrayRef sizes, bool keepdim);
TORCH_API at::Tensor amax(const at::Tensor & self, at::IntArrayRef dim={}, bool keepdim=false);
TORCH_API at::Tensor & amax_out(const at::Tensor & self, at::IntArrayRef dim, bool keepdim, at::Tensor & out);
TORCH_API ::std::tuple<at::Tensor,at::Tensor> max_pool1d_with_indices(const at::Tensor & self, at::IntArrayRef kernel_size, at::IntArrayRef stride={}, at::IntArrayRef padding=0, at::IntArrayRef dilation=1, bool ceil_mode=false);
TORCH_API at::Tensor max_pool1d(const at::Tensor & self, at::IntArrayRef kernel_size, at::IntArrayRef stride={}, at::IntArrayRef padding=0, at::IntArrayRef dilation=1, bool ceil_mode=false);
TORCH_API at::Tensor max_pool2d(const at::Tensor & self, at::IntArrayRef kernel_size, at::IntArrayRef stride={}, at::IntArrayRef padding=0, at::IntArrayRef dilation=1, bool ceil_mode=false);
TORCH_API at::Tensor mkldnn_max_pool2d(const at::Tensor & self, at::IntArrayRef kernel_size, at::IntArrayRef stride={}, at::IntArrayRef padding=0, at::IntArrayRef dilation=1, bool ceil_mode=false);
TORCH_API at::Tensor mkldnn_max_pool2d_backward(const at::Tensor & grad_output, const at::Tensor & output, const at::Tensor & input, at::IntArrayRef kernel_size, at::IntArrayRef stride={}, at::IntArrayRef padding=0, at::IntArrayRef dilation=1, bool ceil_mode=false);
TORCH_API at::Tensor mkldnn_max_pool3d(const at::Tensor & self, at::IntArrayRef kernel_size, at::IntArrayRef stride={}, at::IntArrayRef padding=0, at::IntArrayRef dilation=1, bool ceil_mode=false);
TORCH_API at::Tensor mkldnn_max_pool3d_backward(const at::Tensor & grad_output, const at::Tensor & output, const at::Tensor & input, at::IntArrayRef kernel_size, at::IntArrayRef stride={}, at::IntArrayRef padding=0, at::IntArrayRef dilation=1, bool ceil_mode=false);
TORCH_API at::Tensor quantized_max_pool1d(const at::Tensor & self, at::IntArrayRef kernel_size, at::IntArrayRef stride={}, at::IntArrayRef padding=0, at::IntArrayRef dilation=1, bool ceil_mode=false);
TORCH_API at::Tensor quantized_max_pool2d(const at::Tensor & self, at::IntArrayRef kernel_size, at::IntArrayRef stride={}, at::IntArrayRef padding=0, at::IntArrayRef dilation=1, bool ceil_mode=false);
TORCH_API at::Tensor max_pool3d(const at::Tensor & self, at::IntArrayRef kernel_size, at::IntArrayRef stride={}, at::IntArrayRef padding=0, at::IntArrayRef dilation=1, bool ceil_mode=false);
TORCH_API at::Tensor mean(const at::Tensor & self, c10::optional<at::ScalarType> dtype=c10::nullopt);
struct TORCH_API structured_mean_out : public at::meta::structured_mean_dim {
void impl(const at::Tensor & self, at::IntArrayRef dim, bool keepdim, c10::optional<at::ScalarType> dtype, const at::Tensor & out);
};
TORCH_API at::Tensor mean_quantized_cpu(const at::Tensor & self, at::IntArrayRef dim, bool keepdim=false, c10::optional<at::ScalarType> dtype=c10::nullopt);
TORCH_API at::Tensor & mean_out_quantized_cpu(const at::Tensor & self, at::IntArrayRef dim, bool keepdim, c10::optional<at::ScalarType> dtype, at::Tensor & out);
TORCH_API at::Tensor mean(const at::Tensor & self, at::DimnameList dim, bool keepdim=false, c10::optional<at::ScalarType> dtype=c10::nullopt);
TORCH_API at::Tensor & mean_out(const at::Tensor & self, at::DimnameList dim, bool keepdim, c10::optional<at::ScalarType> dtype, at::Tensor & out);
TORCH_API at::Tensor nanmean(const at::Tensor & self, at::IntArrayRef dim={}, bool keepdim=false, c10::optional<at::ScalarType> dtype=c10::nullopt);
TORCH_API at::Tensor & nanmean_out(const at::Tensor & self, at::IntArrayRef dim, bool keepdim, c10::optional<at::ScalarType> dtype, at::Tensor & out);
TORCH_API at::Tensor median_cpu(const at::Tensor & self);
TORCH_API at::Tensor median_cuda(const at::Tensor & self);
TORCH_API ::std::tuple<at::Tensor,at::Tensor> median(const at::Tensor & self, int64_t dim, bool keepdim=false);
TORCH_API ::std::tuple<at::Tensor &,at::Tensor &> median_out_cpu(const at::Tensor & self, int64_t dim, bool keepdim, at::Tensor & values, at::Tensor & indices);
TORCH_API ::std::tuple<at::Tensor &,at::Tensor &> median_out_cuda(const at::Tensor & self, int64_t dim, bool keepdim, at::Tensor & values, at::Tensor & indices);
TORCH_API ::std::tuple<at::Tensor,at::Tensor> median(const at::Tensor & self, at::Dimname dim, bool keepdim=false);
TORCH_API ::std::tuple<at::Tensor &,at::Tensor &> median_out(const at::Tensor & self, at::Dimname dim, bool keepdim, at::Tensor & values, at::Tensor & indices);
TORCH_API at::Tensor nanmedian_cpu(const at::Tensor & self);
TORCH_API at::Tensor nanmedian_cuda(const at::Tensor & self);
TORCH_API ::std::tuple<at::Tensor,at::Tensor> nanmedian(const at::Tensor & self, int64_t dim, bool keepdim=false);
TORCH_API ::std::tuple<at::Tensor &,at::Tensor &> nanmedian_out_cpu(const at::Tensor & self, int64_t dim, bool keepdim, at::Tensor & values, at::Tensor & indices);
TORCH_API ::std::tuple<at::Tensor &,at::Tensor &> nanmedian_out_cuda(const at::Tensor & self, int64_t dim, bool keepdim, at::Tensor & values, at::Tensor & indices);
TORCH_API ::std::tuple<at::Tensor,at::Tensor> nanmedian(const at::Tensor & self, at::Dimname dim, bool keepdim=false);
TORCH_API ::std::tuple<at::Tensor &,at::Tensor &> nanmedian_out(const at::Tensor & self, at::Dimname dim, bool keepdim, at::Tensor & values, at::Tensor & indices);
TORCH_API ::std::tuple<at::Tensor,at::Tensor> min(const at::Tensor & self, int64_t dim, bool keepdim=false);
TORCH_API ::std::tuple<at::Tensor &,at::Tensor &> min_out(const at::Tensor & self, int64_t dim, bool keepdim, at::Tensor & min, at::Tensor & min_indices);
TORCH_API ::std::tuple<at::Tensor,at::Tensor> min(const at::Tensor & self, at::Dimname dim, bool keepdim=false);
TORCH_API ::std::tuple<at::Tensor &,at::Tensor &> min_out(const at::Tensor & self, at::Dimname dim, bool keepdim, at::Tensor & min, at::Tensor & min_indices);
TORCH_API at::Tensor amin(const at::Tensor & self, at::IntArrayRef dim={}, bool keepdim=false);
TORCH_API at::Tensor & amin_out(const at::Tensor & self, at::IntArrayRef dim, bool keepdim, at::Tensor & out);
TORCH_API at::Tensor mkldnn_convolution(const at::Tensor & self, const at::Tensor & weight, const c10::optional<at::Tensor> & bias, at::IntArrayRef padding, at::IntArrayRef stride, at::IntArrayRef dilation, int64_t groups);
TORCH_API at::Tensor mkldnn_convolution_backward_input(at::IntArrayRef self_size, const at::Tensor & grad_output, const at::Tensor & weight, at::IntArrayRef padding, at::IntArrayRef stride, at::IntArrayRef dilation, int64_t groups, bool bias_defined);
TORCH_API ::std::tuple<at::Tensor,at::Tensor> mkldnn_convolution_backward_weights(at::IntArrayRef weight_size, const at::Tensor & grad_output, const at::Tensor & self, at::IntArrayRef padding, at::IntArrayRef stride, at::IntArrayRef dilation, int64_t groups, bool bias_defined);
TORCH_API ::std::tuple<at::Tensor,at::Tensor,at::Tensor> mkldnn_convolution_backward(const at::Tensor & self, const at::Tensor & grad_output, const at::Tensor & weight, at::IntArrayRef padding, at::IntArrayRef stride, at::IntArrayRef dilation, int64_t groups, ::std::array<bool,3> output_mask);
TORCH_API ::std::tuple<at::Tensor,at::Tensor,at::Tensor> miopen_batch_norm(const at::Tensor & input, const at::Tensor & weight, const c10::optional<at::Tensor> & bias, const c10::optional<at::Tensor> & running_mean, const c10::optional<at::Tensor> & running_var, bool training, double exponential_average_factor, double epsilon);
TORCH_API ::std::tuple<at::Tensor,at::Tensor,at::Tensor> miopen_batch_norm_backward(const at::Tensor & input, const at::Tensor & grad_output, const at::Tensor & weight, const c10::optional<at::Tensor> & running_mean, const c10::optional<at::Tensor> & running_var, const c10::optional<at::Tensor> & save_mean, const c10::optional<at::Tensor> & save_var, double epsilon);
TORCH_API at::Tensor miopen_convolution(const at::Tensor & self, const at::Tensor & weight, const c10::optional<at::Tensor> & bias, at::IntArrayRef padding, at::IntArrayRef stride, at::IntArrayRef dilation, int64_t groups, bool benchmark, bool deterministic);
TORCH_API at::Tensor miopen_convolution_backward_input(at::IntArrayRef self_size, const at::Tensor & grad_output, const at::Tensor & weight, at::IntArrayRef padding, at::IntArrayRef stride, at::IntArrayRef dilation, int64_t groups, bool benchmark, bool deterministic);
TORCH_API ::std::tuple<at::Tensor,at::Tensor,at::Tensor> miopen_convolution_backward(const at::Tensor & self, const at::Tensor & grad_output, const at::Tensor & weight, at::IntArrayRef padding, at::IntArrayRef stride, at::IntArrayRef dilation, int64_t groups, bool benchmark, bool deterministic, ::std::array<bool,3> output_mask);
TORCH_API at::Tensor miopen_convolution_backward_bias(const at::Tensor & grad_output);
TORCH_API at::Tensor miopen_convolution_backward_weight(at::IntArrayRef weight_size, const at::Tensor & grad_output, const at::Tensor & self, at::IntArrayRef padding, at::IntArrayRef stride, at::IntArrayRef dilation, int64_t groups, bool benchmark, bool deterministic);
TORCH_API at::Tensor miopen_convolution_transpose(const at::Tensor & self, const at::Tensor & weight, const c10::optional<at::Tensor> & bias, at::IntArrayRef padding, at::IntArrayRef output_padding, at::IntArrayRef stride, at::IntArrayRef dilation, int64_t groups, bool benchmark, bool deterministic);
TORCH_API ::std::tuple<at::Tensor,at::Tensor,at::Tensor> miopen_convolution_transpose_backward(const at::Tensor & self, const at::Tensor & grad_output, const at::Tensor & weight, at::IntArrayRef padding, at::IntArrayRef output_padding, at::IntArrayRef stride, at::IntArrayRef dilation, int64_t groups, bool benchmark, bool deterministic, ::std::array<bool,3> output_mask);
TORCH_API at::Tensor miopen_convolution_transpose_backward_input(const at::Tensor & grad_output, const at::Tensor & weight, at::IntArrayRef padding, at::IntArrayRef stride, at::IntArrayRef dilation, int64_t groups, bool benchmark, bool deterministic);
TORCH_API at::Tensor miopen_convolution_transpose_backward_weight(at::IntArrayRef weight_size, const at::Tensor & grad_output, const at::Tensor & self, at::IntArrayRef padding, at::IntArrayRef stride, at::IntArrayRef dilation, int64_t groups, bool benchmark, bool deterministic);
TORCH_API at::Tensor miopen_depthwise_convolution(const at::Tensor & self, const at::Tensor & weight, const c10::optional<at::Tensor> & bias, at::IntArrayRef padding, at::IntArrayRef stride, at::IntArrayRef dilation, int64_t groups, bool benchmark, bool deterministic);
TORCH_API at::Tensor miopen_depthwise_convolution_backward_input(at::IntArrayRef self_size, const at::Tensor & grad_output, const at::Tensor & weight, at::IntArrayRef padding, at::IntArrayRef stride, at::IntArrayRef dilation, int64_t groups, bool benchmark, bool deterministic);
TORCH_API ::std::tuple<at::Tensor,at::Tensor,at::Tensor> miopen_depthwise_convolution_backward(const at::Tensor & self, const at::Tensor & grad_output, const at::Tensor & weight, at::IntArrayRef padding, at::IntArrayRef stride, at::IntArrayRef dilation, int64_t groups, bool benchmark, bool deterministic, ::std::array<bool,3> output_mask);
TORCH_API at::Tensor miopen_depthwise_convolution_backward_weight(at::IntArrayRef weight_size, const at::Tensor & grad_output, const at::Tensor & self, at::IntArrayRef padding, at::IntArrayRef stride, at::IntArrayRef dilation, int64_t groups, bool benchmark, bool deterministic);
TORCH_API ::std::tuple<at::Tensor,at::Tensor,at::Tensor,at::Tensor,at::Tensor> miopen_rnn(const at::Tensor & input, at::TensorList weight, int64_t weight_stride0, const at::Tensor & hx, const c10::optional<at::Tensor> & cx, int64_t mode, int64_t hidden_size, int64_t num_layers, bool batch_first, double dropout, bool train, bool bidirectional, at::IntArrayRef batch_sizes, const c10::optional<at::Tensor> & dropout_state);
TORCH_API ::std::tuple<at::Tensor,at::Tensor,at::Tensor,::std::vector<at::Tensor>> miopen_rnn_backward(const at::Tensor & input, at::TensorList weight, int64_t weight_stride0, const at::Tensor & weight_buf, const at::Tensor & hx, const c10::optional<at::Tensor> & cx, const at::Tensor & output, const c10::optional<at::Tensor> & grad_output, const c10::optional<at::Tensor> & grad_hy, const c10::optional<at::Tensor> & grad_cy, int64_t mode, int64_t hidden_size, int64_t num_layers, bool batch_first, double dropout, bool train, bool bidirectional, at::IntArrayRef batch_sizes, const c10::optional<at::Tensor> & dropout_state, const at::Tensor & reserve, ::std::array<bool,4> output_mask);
struct TORCH_API structured_mm_out_cpu : public at::meta::structured_mm {
void impl(const at::Tensor & self, const at::Tensor & mat2, const at::Tensor & out);
};
struct TORCH_API structured_mm_out_cuda : public at::meta::structured_mm {
void impl(const at::Tensor & self, const at::Tensor & mat2, const at::Tensor & out);
};
TORCH_API at::Tensor _sparse_mm(const at::Tensor & self, const at::Tensor & mat2);
TORCH_API at::Tensor & _sparse_mm_out(const at::Tensor & self, const at::Tensor & mat2, at::Tensor & out);
TORCH_API at::Tensor & _sparse_csr_mm_out(const at::Tensor & self, const at::Tensor & mat2, at::Tensor & out);
TORCH_API at::Tensor _sparse_mm(const at::Tensor & sparse, const at::Tensor & dense);
TORCH_API at::Tensor sparse_sparse_matmul_cpu(const at::Tensor & self, const at::Tensor & other);
TORCH_API at::Tensor sparse_sparse_matmul_cuda(const at::Tensor & self, const at::Tensor & other);
TORCH_API at::Tensor sparse_mask_helper_cpu(const at::Tensor & t, const at::Tensor & mask_indices);
TORCH_API at::Tensor sparse_mask_helper_cuda(const at::Tensor & t, const at::Tensor & mask_indices);
TORCH_API ::std::tuple<at::Tensor &,at::Tensor &> mode_out(const at::Tensor & self, int64_t dim, bool keepdim, at::Tensor & values, at::Tensor & indices);
TORCH_API ::std::tuple<at::Tensor,at::Tensor> mode(const at::Tensor & self, int64_t dim=-1, bool keepdim=false);
TORCH_API ::std::tuple<at::Tensor,at::Tensor> mode(const at::Tensor & self, at::Dimname dim, bool keepdim=false);
TORCH_API ::std::tuple<at::Tensor &,at::Tensor &> mode_out(const at::Tensor & self, at::Dimname dim, bool keepdim, at::Tensor & values, at::Tensor & indices);
struct TORCH_API structured_mul_out : public at::meta::structured_mul_Tensor {
void impl(const at::Tensor & self, const at::Tensor & other, const at::Tensor & out);
};
TORCH_API at::Tensor mul_sparse(const at::Tensor & self, const at::Tensor & other);
TORCH_API at::Tensor & mul_out_sparse_cpu(const at::Tensor & self, const at::Tensor & other, at::Tensor & out);
TORCH_API at::Tensor & mul_sparse_(at::Tensor & self, const at::Tensor & other);
TORCH_API at::Tensor & mul_out_sparse_cuda(const at::Tensor & self, const at::Tensor & other, at::Tensor & out);
TORCH_API at::Tensor mkldnn_mul(const at::Tensor & self, const at::Tensor & other);
TORCH_API at::Tensor & mkldnn_mul_out(const at::Tensor & self, const at::Tensor & other, at::Tensor & out);
TORCH_API at::Tensor & mkldnn_mul_(at::Tensor & self, const at::Tensor & other);
TORCH_API at::Tensor mul(const at::Tensor & self, const at::Scalar & other);
TORCH_API at::Tensor & mul_(at::Tensor & self, const at::Scalar & other);
TORCH_API at::Tensor multiply(const at::Tensor & self, const at::Tensor & other);
TORCH_API at::Tensor & multiply_out(const at::Tensor & self, const at::Tensor & other, at::Tensor & out);
TORCH_API at::Tensor & multiply_(at::Tensor & self, const at::Tensor & other);
TORCH_API at::Tensor multiply(const at::Tensor & self, const at::Scalar & other);
TORCH_API at::Tensor & multiply_(at::Tensor & self, const at::Scalar & other);
TORCH_API at::Tensor & mv_out(const at::Tensor & self, const at::Tensor & vec, at::Tensor & out);
TORCH_API at::Tensor mv(const at::Tensor & self, const at::Tensor & vec);
TORCH_API at::Tensor mv_sparse(const at::Tensor & self, const at::Tensor & vec);
TORCH_API at::Tensor mvlgamma(const at::Tensor & self, int64_t p);
TORCH_API at::Tensor & mvlgamma_(at::Tensor & self, int64_t p);
TORCH_API at::Tensor & mvlgamma_out(const at::Tensor & self, int64_t p, at::Tensor & out);
TORCH_API at::Tensor narrow_copy_dense(const at::Tensor & self, int64_t dim, int64_t start, int64_t length);
TORCH_API at::Tensor narrow_copy_dense_cpu(const at::Tensor & self, int64_t dim, int64_t start, int64_t length);
TORCH_API at::Tensor & narrow_copy_dense_cpu_out(const at::Tensor & self, int64_t dim, int64_t start, int64_t length, at::Tensor & out);
TORCH_API at::Tensor narrow_copy_sparse(const at::Tensor & self, int64_t dim, int64_t start, int64_t length);
TORCH_API at::Tensor narrow(const at::Tensor & self, int64_t dim, int64_t start, int64_t length);
TORCH_API at::Tensor narrow(const at::Tensor & self, int64_t dim, const at::Tensor & start, int64_t length);
TORCH_API ::std::tuple<at::Tensor,at::Tensor,at::Tensor> batch_norm_cpu(const at::Tensor & input, const c10::optional<at::Tensor> & weight, const c10::optional<at::Tensor> & bias, const c10::optional<at::Tensor> & running_mean, const c10::optional<at::Tensor> & running_var, bool training, double momentum, double eps);
TORCH_API ::std::tuple<at::Tensor,at::Tensor,at::Tensor> batch_norm_cuda(const at::Tensor & input, const c10::optional<at::Tensor> & weight, const c10::optional<at::Tensor> & bias, const c10::optional<at::Tensor> & running_mean, const c10::optional<at::Tensor> & running_var, bool training, double momentum, double eps);
TORCH_API ::std::tuple<at::Tensor &,at::Tensor &,at::Tensor &> batch_norm_cuda_out(const at::Tensor & input, const c10::optional<at::Tensor> & weight, const c10::optional<at::Tensor> & bias, const c10::optional<at::Tensor> & running_mean, const c10::optional<at::Tensor> & running_var, bool training, double momentum, double eps, at::Tensor & out, at::Tensor & save_mean, at::Tensor & save_invstd);
TORCH_API ::std::tuple<at::Tensor,at::Tensor,at::Tensor> mkldnn_batch_norm(const at::Tensor & input, const c10::optional<at::Tensor> & weight, const c10::optional<at::Tensor> & bias, const c10::optional<at::Tensor> & running_mean, const c10::optional<at::Tensor> & running_var, bool training, double momentum, double eps);
TORCH_API ::std::tuple<at::Tensor,at::Tensor> batch_norm_stats_cuda(const at::Tensor & input, double eps);
TORCH_API at::Tensor batch_norm_elemt_cuda(const at::Tensor & input, const c10::optional<at::Tensor> & weight, const c10::optional<at::Tensor> & bias, const at::Tensor & mean, const at::Tensor & invstd, double eps);
TORCH_API at::Tensor & batch_norm_elemt_cuda_out(const at::Tensor & input, const c10::optional<at::Tensor> & weight, const c10::optional<at::Tensor> & bias, const at::Tensor & mean, const at::Tensor & invstd, double eps, at::Tensor & out);
TORCH_API ::std::tuple<at::Tensor,at::Tensor> batch_norm_gather_stats_cuda(const at::Tensor & input, const at::Tensor & mean, const at::Tensor & invstd, const c10::optional<at::Tensor> & running_mean, const c10::optional<at::Tensor> & running_var, double momentum, double eps, int64_t count);
TORCH_API ::std::tuple<at::Tensor,at::Tensor> batch_norm_gather_stats_with_counts_cuda(const at::Tensor & input, const at::Tensor & mean, const at::Tensor & invstd, const c10::optional<at::Tensor> & running_mean, const c10::optional<at::Tensor> & running_var, double momentum, double eps, const at::Tensor & counts);
TORCH_API ::std::tuple<at::Tensor,at::Tensor,at::Tensor> batch_norm_backward_cpu(const at::Tensor & grad_out, const at::Tensor & input, const c10::optional<at::Tensor> & weight, const c10::optional<at::Tensor> & running_mean, const c10::optional<at::Tensor> & running_var, const c10::optional<at::Tensor> & save_mean, const c10::optional<at::Tensor> & save_invstd, bool train, double eps, ::std::array<bool,3> output_mask);
TORCH_API ::std::tuple<at::Tensor,at::Tensor,at::Tensor> batch_norm_backward_cuda(const at::Tensor & grad_out, const at::Tensor & input, const c10::optional<at::Tensor> & weight, const c10::optional<at::Tensor> & running_mean, const c10::optional<at::Tensor> & running_var, const c10::optional<at::Tensor> & save_mean, const c10::optional<at::Tensor> & save_invstd, bool train, double eps, ::std::array<bool,3> output_mask);
TORCH_API ::std::tuple<at::Tensor,at::Tensor,at::Tensor> mkldnn_batch_norm_backward(const at::Tensor & grad_out, const at::Tensor & input, const c10::optional<at::Tensor> & weight, const c10::optional<at::Tensor> & running_mean, const c10::optional<at::Tensor> & running_var, const c10::optional<at::Tensor> & save_mean, const c10::optional<at::Tensor> & save_invstd, bool train, double eps, ::std::array<bool,3> output_mask);
TORCH_API ::std::tuple<at::Tensor,at::Tensor,at::Tensor,at::Tensor> batch_norm_backward_reduce_cuda(const at::Tensor & grad_out, const at::Tensor & input, const at::Tensor & mean, const at::Tensor & invstd, const c10::optional<at::Tensor> & weight, bool input_g, bool weight_g, bool bias_g);
TORCH_API at::Tensor batch_norm_backward_elemt_cuda(const at::Tensor & grad_out, const at::Tensor & input, const at::Tensor & mean, const at::Tensor & invstd, const c10::optional<at::Tensor> & weight, const at::Tensor & mean_dy, const at::Tensor & mean_dy_xmu, const at::Tensor & count);
TORCH_API ::std::tuple<at::Tensor,at::Tensor> batch_norm_update_stats_cpu(const at::Tensor & input, const c10::optional<at::Tensor> & running_mean, const c10::optional<at::Tensor> & running_var, double momentum);
TORCH_API ::std::tuple<at::Tensor,at::Tensor> batch_norm_update_stats_cuda(const at::Tensor & input, const c10::optional<at::Tensor> & running_mean, const c10::optional<at::Tensor> & running_var, double momentum);
TORCH_API bool is_vulkan_available();
TORCH_API bool _nnpack_available();
TORCH_API at::Tensor _nnpack_spatial_convolution(const at::Tensor & input, const at::Tensor & weight, const c10::optional<at::Tensor> & bias, at::IntArrayRef padding, at::IntArrayRef stride=1);
TORCH_API ::std::tuple<at::Tensor,at::Tensor,at::Tensor> _nnpack_spatial_convolution_backward(const at::Tensor & input, const at::Tensor & grad_output, const at::Tensor & weight, at::IntArrayRef padding, ::std::array<bool,3> output_mask);
TORCH_API at::Tensor _nnpack_spatial_convolution_backward_input(const at::Tensor & input, const at::Tensor & grad_output, const at::Tensor & weight, at::IntArrayRef padding);
TORCH_API at::Tensor _nnpack_spatial_convolution_backward_weight(const at::Tensor & input, at::IntArrayRef weightsize, const at::Tensor & grad_output, at::IntArrayRef padding);
TORCH_API at::Tensor ones(at::IntArrayRef size, c10::optional<at::DimnameList> names, c10::optional<at::ScalarType> dtype={}, c10::optional<at::Layout> layout={}, c10::optional<at::Device> device={}, c10::optional<bool> pin_memory={});
TORCH_API at::Tensor ones(at::IntArrayRef size, c10::optional<at::ScalarType> dtype={}, c10::optional<at::Layout> layout={}, c10::optional<at::Device> device={}, c10::optional<bool> pin_memory={});
TORCH_API at::Tensor & ones_out(at::IntArrayRef size, at::Tensor & out);
TORCH_API at::Tensor ones_like(const at::Tensor & self, c10::optional<at::ScalarType> dtype={}, c10::optional<at::Layout> layout={}, c10::optional<at::Device> device={}, c10::optional<bool> pin_memory={}, c10::optional<at::MemoryFormat> memory_format=c10::nullopt);
TORCH_API at::Tensor pairwise_distance(const at::Tensor & x1, const at::Tensor & x2, double p=2, double eps=1e-06, bool keepdim=false);
TORCH_API at::Tensor cdist(const at::Tensor & x1, const at::Tensor & x2, double p=2, c10::optional<int64_t> compute_mode=c10::nullopt);
TORCH_API at::Tensor _euclidean_dist(const at::Tensor & x1, const at::Tensor & x2);
TORCH_API at::Tensor _cdist_forward(const at::Tensor & x1, const at::Tensor & x2, double p, c10::optional<int64_t> compute_mode);
TORCH_API at::Tensor _cdist_backward(const at::Tensor & grad, const at::Tensor & x1, const at::Tensor & x2, double p, const at::Tensor & cdist);
TORCH_API at::Tensor pdist(const at::Tensor & self, double p=2);
TORCH_API at::Tensor _pdist_forward(const at::Tensor & self, double p=2);
TORCH_API at::Tensor _pdist_backward(const at::Tensor & grad, const at::Tensor & self, double p, const at::Tensor & pdist);
TORCH_API at::Tensor cosine_similarity(const at::Tensor & x1, const at::Tensor & x2, int64_t dim=1, double eps=1e-08);
TORCH_API at::Tensor permute(const at::Tensor & self, at::IntArrayRef dims);
TORCH_API at::Tensor movedim(const at::Tensor & self, at::IntArrayRef source, at::IntArrayRef destination);
TORCH_API at::Tensor movedim(const at::Tensor & self, int64_t source, int64_t destination);
TORCH_API at::Tensor moveaxis(const at::Tensor & self, at::IntArrayRef source, at::IntArrayRef destination);
TORCH_API at::Tensor moveaxis(const at::Tensor & self, int64_t source, int64_t destination);
TORCH_API at::Tensor numpy_T(const at::Tensor & self);
TORCH_API at::Tensor pixel_shuffle(const at::Tensor & self, int64_t upscale_factor);
TORCH_API at::Tensor pixel_unshuffle(const at::Tensor & self, int64_t downscale_factor);
TORCH_API at::Tensor channel_shuffle(const at::Tensor & self, int64_t groups);
TORCH_API at::Tensor channel_shuffle_quantized_cpu(const at::Tensor & self, int64_t groups);
TORCH_API bool is_pinned_default(const at::Tensor & self, c10::optional<at::Device> device=c10::nullopt);
TORCH_API bool is_pinned_cuda(const at::Tensor & self, c10::optional<at::Device> device=c10::nullopt);
TORCH_API at::Tensor pin_memory(const at::Tensor & self, c10::optional<at::Device> device=c10::nullopt);
TORCH_API at::Tensor _pin_memory_cuda(const at::Tensor & self, c10::optional<at::Device> device=c10::nullopt);
TORCH_API at::Tensor pinverse(const at::Tensor & self, double rcond=1e-15);
TORCH_API at::Tensor poisson_nll_loss(const at::Tensor & input, const at::Tensor & target, bool log_input, bool full, double eps, int64_t reduction);
TORCH_API at::Tensor rad2deg(const at::Tensor & self);
TORCH_API at::Tensor & rad2deg_out(const at::Tensor & self, at::Tensor & out);
TORCH_API at::Tensor & rad2deg_(at::Tensor & self);
TORCH_API at::Tensor deg2rad(const at::Tensor & self);
TORCH_API at::Tensor & deg2rad_out(const at::Tensor & self, at::Tensor & out);
TORCH_API at::Tensor & deg2rad_(at::Tensor & self);
TORCH_API at::Tensor scalar_tensor(const at::Scalar & s, c10::optional<at::ScalarType> dtype={}, c10::optional<at::Layout> layout={}, c10::optional<at::Device> device={}, c10::optional<bool> pin_memory={});
TORCH_API at::Tensor rand(at::IntArrayRef size, c10::optional<at::DimnameList> names, c10::optional<at::ScalarType> dtype={}, c10::optional<at::Layout> layout={}, c10::optional<at::Device> device={}, c10::optional<bool> pin_memory={});
TORCH_API at::Tensor rand(at::IntArrayRef size, c10::optional<at::Generator> generator, c10::optional<at::DimnameList> names, c10::optional<at::ScalarType> dtype={}, c10::optional<at::Layout> layout={}, c10::optional<at::Device> device={}, c10::optional<bool> pin_memory={});
TORCH_API at::Tensor rand(at::IntArrayRef size, c10::optional<at::ScalarType> dtype={}, c10::optional<at::Layout> layout={}, c10::optional<at::Device> device={}, c10::optional<bool> pin_memory={});
TORCH_API at::Tensor rand(at::IntArrayRef size, c10::optional<at::Generator> generator, c10::optional<at::ScalarType> dtype={}, c10::optional<at::Layout> layout={}, c10::optional<at::Device> device={}, c10::optional<bool> pin_memory={});
TORCH_API at::Tensor & rand_out(at::IntArrayRef size, at::Tensor & out);
TORCH_API at::Tensor & rand_out(at::IntArrayRef size, c10::optional<at::Generator> generator, at::Tensor & out);
TORCH_API at::Tensor rand_like(const at::Tensor & self, c10::optional<at::ScalarType> dtype={}, c10::optional<at::Layout> layout={}, c10::optional<at::Device> device={}, c10::optional<bool> pin_memory={}, c10::optional<at::MemoryFormat> memory_format=c10::nullopt);
TORCH_API at::Tensor randint(int64_t high, at::IntArrayRef size, c10::optional<at::ScalarType> dtype={}, c10::optional<at::Layout> layout={}, c10::optional<at::Device> device={}, c10::optional<bool> pin_memory={});
TORCH_API at::Tensor randint(int64_t high, at::IntArrayRef size, c10::optional<at::Generator> generator, c10::optional<at::ScalarType> dtype={}, c10::optional<at::Layout> layout={}, c10::optional<at::Device> device={}, c10::optional<bool> pin_memory={});
TORCH_API at::Tensor randint(int64_t low, int64_t high, at::IntArrayRef size, c10::optional<at::ScalarType> dtype={}, c10::optional<at::Layout> layout={}, c10::optional<at::Device> device={}, c10::optional<bool> pin_memory={});
TORCH_API at::Tensor randint(int64_t low, int64_t high, at::IntArrayRef size, c10::optional<at::Generator> generator, c10::optional<at::ScalarType> dtype={}, c10::optional<at::Layout> layout={}, c10::optional<at::Device> device={}, c10::optional<bool> pin_memory={});
TORCH_API at::Tensor & randint_out(int64_t high, at::IntArrayRef size, at::Tensor & out);
TORCH_API at::Tensor & randint_out(int64_t high, at::IntArrayRef size, c10::optional<at::Generator> generator, at::Tensor & out);
TORCH_API at::Tensor & randint_out(int64_t low, int64_t high, at::IntArrayRef size, at::Tensor & out);
TORCH_API at::Tensor & randint_out(int64_t low, int64_t high, at::IntArrayRef size, c10::optional<at::Generator> generator, at::Tensor & out);
TORCH_API at::Tensor randint_like(const at::Tensor & self, int64_t high, c10::optional<at::ScalarType> dtype={}, c10::optional<at::Layout> layout={}, c10::optional<at::Device> device={}, c10::optional<bool> pin_memory={}, c10::optional<at::MemoryFormat> memory_format=c10::nullopt);
TORCH_API at::Tensor randint_like(const at::Tensor & self, int64_t low, int64_t high, c10::optional<at::ScalarType> dtype={}, c10::optional<at::Layout> layout={}, c10::optional<at::Device> device={}, c10::optional<bool> pin_memory={}, c10::optional<at::MemoryFormat> memory_format=c10::nullopt);
TORCH_API at::Tensor randn(at::IntArrayRef size, c10::optional<at::ScalarType> dtype={}, c10::optional<at::Layout> layout={}, c10::optional<at::Device> device={}, c10::optional<bool> pin_memory={});
TORCH_API at::Tensor randn(at::IntArrayRef size, c10::optional<at::Generator> generator, c10::optional<at::ScalarType> dtype={}, c10::optional<at::Layout> layout={}, c10::optional<at::Device> device={}, c10::optional<bool> pin_memory={});
TORCH_API at::Tensor randn(at::IntArrayRef size, c10::optional<at::DimnameList> names, c10::optional<at::ScalarType> dtype={}, c10::optional<at::Layout> layout={}, c10::optional<at::Device> device={}, c10::optional<bool> pin_memory={});
TORCH_API at::Tensor randn(at::IntArrayRef size, c10::optional<at::Generator> generator, c10::optional<at::DimnameList> names, c10::optional<at::ScalarType> dtype={}, c10::optional<at::Layout> layout={}, c10::optional<at::Device> device={}, c10::optional<bool> pin_memory={});
TORCH_API at::Tensor & randn_out(at::IntArrayRef size, at::Tensor & out);
TORCH_API at::Tensor & randn_out(at::IntArrayRef size, c10::optional<at::Generator> generator, at::Tensor & out);
TORCH_API at::Tensor randn_like(const at::Tensor & self, c10::optional<at::ScalarType> dtype={}, c10::optional<at::Layout> layout={}, c10::optional<at::Device> device={}, c10::optional<bool> pin_memory={}, c10::optional<at::MemoryFormat> memory_format=c10::nullopt);
TORCH_API at::Tensor randperm(int64_t n, c10::optional<at::ScalarType> dtype={}, c10::optional<at::Layout> layout={}, c10::optional<at::Device> device={}, c10::optional<bool> pin_memory={});
TORCH_API at::Tensor randperm(int64_t n, c10::optional<at::Generator> generator, c10::optional<at::ScalarType> dtype={}, c10::optional<at::Layout> layout={}, c10::optional<at::Device> device={}, c10::optional<bool> pin_memory={});
TORCH_API at::Tensor & randperm_out(int64_t n, at::Tensor & out);
TORCH_API at::Tensor & randperm_out_cpu(int64_t n, c10::optional<at::Generator> generator, at::Tensor & out);
TORCH_API at::Tensor & randperm_out_cuda(int64_t n, c10::optional<at::Generator> generator, at::Tensor & out);
TORCH_API at::Tensor range(const at::Scalar & start, const at::Scalar & end, const at::Scalar & step=1, c10::optional<at::ScalarType> dtype={}, c10::optional<at::Layout> layout={}, c10::optional<at::Device> device={}, c10::optional<bool> pin_memory={});
TORCH_API at::Tensor range(const at::Scalar & start, const at::Scalar & end, c10::optional<at::ScalarType> dtype={}, c10::optional<at::Layout> layout={}, c10::optional<at::Device> device={}, c10::optional<bool> pin_memory={});
TORCH_API at::Tensor & range_cpu_out(const at::Scalar & start, const at::Scalar & end, const at::Scalar & step, at::Tensor & out);
TORCH_API at::Tensor & range_cuda_out(const at::Scalar & start, const at::Scalar & end, const at::Scalar & step, at::Tensor & out);
TORCH_API at::Tensor ravel(const at::Tensor & self);
struct TORCH_API structured_reciprocal_out : public at::meta::structured_reciprocal {
void impl(const at::Tensor & self, const at::Tensor & out);
};
struct TORCH_API structured_neg_out : public at::meta::structured_neg {
void impl(const at::Tensor & self, const at::Tensor & out);
};
TORCH_API at::Tensor neg_sparse(const at::Tensor & self);
TORCH_API at::Tensor & neg_out_sparse(const at::Tensor & self, at::Tensor & out);
TORCH_API at::Tensor & neg_sparse_(at::Tensor & self);
TORCH_API at::Tensor negative(const at::Tensor & self);
TORCH_API at::Tensor & negative_out(const at::Tensor & self, at::Tensor & out);
TORCH_API at::Tensor & negative_(at::Tensor & self);
TORCH_API at::Tensor repeat(const at::Tensor & self, at::IntArrayRef repeats);
TORCH_API at::Tensor repeat_interleave_cpu(const at::Tensor & repeats, c10::optional<int64_t> output_size=c10::nullopt);
TORCH_API at::Tensor repeat_interleave_cuda(const at::Tensor & repeats, c10::optional<int64_t> output_size=c10::nullopt);
TORCH_API at::Tensor repeat_interleave(const at::Tensor & self, const at::Tensor & repeats, c10::optional<int64_t> dim=c10::nullopt, c10::optional<int64_t> output_size=c10::nullopt);
TORCH_API at::Tensor repeat_interleave(const at::Tensor & self, int64_t repeats, c10::optional<int64_t> dim=c10::nullopt, c10::optional<int64_t> output_size=c10::nullopt);
TORCH_API at::Tensor reshape(const at::Tensor & self, at::IntArrayRef shape);
TORCH_API at::Tensor _reshape_alias(const at::Tensor & self, at::IntArrayRef size, at::IntArrayRef stride);
TORCH_API at::Tensor mkldnn_reshape(const at::Tensor & self, at::IntArrayRef shape);
TORCH_API at::Tensor reshape_as(const at::Tensor & self, const at::Tensor & other);
struct TORCH_API structured_round_out : public at::meta::structured_round {
void impl(const at::Tensor & self, const at::Tensor & out);
};
TORCH_API at::Tensor rrelu(const at::Tensor & self, const at::Scalar & lower=0.125, const at::Scalar & upper=0.3333333333333333, bool training=false, c10::optional<at::Generator> generator=c10::nullopt);
TORCH_API at::Tensor & rrelu_(at::Tensor & self, const at::Scalar & lower=0.125, const at::Scalar & upper=0.3333333333333333, bool training=false, c10::optional<at::Generator> generator=c10::nullopt);
TORCH_API at::Tensor relu(const at::Tensor & self);
TORCH_API at::Tensor mkldnn_relu(const at::Tensor & self);
TORCH_API at::Tensor relu_quantized_cpu(const at::Tensor & self);
TORCH_API at::Tensor & relu_(at::Tensor & self);
TORCH_API at::Tensor & mkldnn_relu_(at::Tensor & self);
TORCH_API at::Tensor & relu_quantized_cpu_(at::Tensor & self);
TORCH_API at::Tensor relu6(const at::Tensor & self);
TORCH_API at::Tensor & relu6_(at::Tensor & self);
TORCH_API at::Tensor prelu_cpu(const at::Tensor & self, const at::Tensor & weight);
TORCH_API at::Tensor prelu_cuda(const at::Tensor & self, const at::Tensor & weight);
TORCH_API ::std::tuple<at::Tensor,at::Tensor> prelu_backward_cpu(const at::Tensor & grad_output, const at::Tensor & self, const at::Tensor & weight);
TORCH_API ::std::tuple<at::Tensor,at::Tensor> prelu_backward_cuda(const at::Tensor & grad_output, const at::Tensor & self, const at::Tensor & weight);
struct TORCH_API structured_gelu_out_cpu : public at::meta::structured_gelu {
void impl(const at::Tensor & self, const at::Tensor & out);
};
struct TORCH_API structured_gelu_out_cuda : public at::meta::structured_gelu {
void impl(const at::Tensor & self, const at::Tensor & out);
};
TORCH_API at::Tensor mkldnn_gelu(const at::Tensor & self);
struct TORCH_API structured_gelu_backward_out_cpu : public at::meta::structured_gelu_backward {
void impl(const at::Tensor & grad, const at::Tensor & self, const at::Tensor & grad_input);
};
struct TORCH_API structured_gelu_backward_out_cuda : public at::meta::structured_gelu_backward {
void impl(const at::Tensor & grad, const at::Tensor & self, const at::Tensor & grad_input);
};
TORCH_API at::Tensor mkldnn_gelu_backward(const at::Tensor & grad, const at::Tensor & self);
TORCH_API at::Tensor infinitely_differentiable_gelu_backward(const at::Tensor & grad, const at::Tensor & self);
struct TORCH_API structured_hardshrink_out : public at::meta::structured_hardshrink {
void impl(const at::Tensor & self, const at::Scalar & lambd, const at::Tensor & out);
};
struct TORCH_API structured_hardshrink_backward_out : public at::meta::structured_hardshrink_backward {
void impl(const at::Tensor & grad_out, const at::Tensor & self, const at::Scalar & lambd, const at::Tensor & grad_input);
};
struct TORCH_API structured_rsqrt_out : public at::meta::structured_rsqrt {
void impl(const at::Tensor & self, const at::Tensor & out);
};
TORCH_API at::Tensor select(const at::Tensor & self, at::Dimname dim, int64_t index);
TORCH_API at::Tensor select(const at::Tensor & self, int64_t dim, int64_t index);
TORCH_API at::Tensor select_backward(const at::Tensor & grad_output, at::IntArrayRef input_sizes, int64_t dim, int64_t index);
TORCH_API at::Tensor selu(const at::Tensor & self);
TORCH_API at::Tensor & selu_(at::Tensor & self);
TORCH_API at::Tensor celu(const at::Tensor & self, const at::Scalar & alpha=1.0);
TORCH_API at::Tensor & celu_(at::Tensor & self, const at::Scalar & alpha=1.0);
TORCH_API at::Tensor silu(const at::Tensor & self);
TORCH_API at::Tensor & silu_(at::Tensor & self);
struct TORCH_API structured_silu_out : public at::meta::structured_silu {
void impl(const at::Tensor & self, const at::Tensor & out);
};
TORCH_API at::Tensor math_silu_backward(const at::Tensor & grad_output, const at::Tensor & self);
struct TORCH_API structured_silu_backward_out : public at::meta::structured_silu_backward {
void impl(const at::Tensor & grad_output, const at::Tensor & self, const at::Tensor & grad_input);
};
TORCH_API at::Tensor mish(const at::Tensor & self);
TORCH_API at::Tensor & mish_(at::Tensor & self);
struct TORCH_API structured_mish_out : public at::meta::structured_mish {
void impl(const at::Tensor & self, const at::Tensor & out);
};
TORCH_API at::Tensor math_mish_backward(const at::Tensor & grad_output, const at::Tensor & self);
TORCH_API at::Tensor mish_backward(const at::Tensor & grad_output, const at::Tensor & self);
struct TORCH_API structured_sigmoid_out : public at::meta::structured_sigmoid {
void impl(const at::Tensor & self, const at::Tensor & out);
};
TORCH_API at::Tensor mkldnn_sigmoid(const at::Tensor & self);
TORCH_API at::Tensor & mkldnn_sigmoid_(at::Tensor & self);
TORCH_API at::Tensor sigmoid_quantized_cpu(const at::Tensor & self);
TORCH_API at::Tensor logit(const at::Tensor & self, c10::optional<double> eps=c10::nullopt);
TORCH_API at::Tensor & logit_out(const at::Tensor & self, c10::optional<double> eps, at::Tensor & out);
TORCH_API at::Tensor & logit_(at::Tensor & self, c10::optional<double> eps=c10::nullopt);
struct TORCH_API structured_sin_out : public at::meta::structured_sin {
void impl(const at::Tensor & self, const at::Tensor & out);
};
struct TORCH_API structured_sinc_out : public at::meta::structured_sinc {
void impl(const at::Tensor & self, const at::Tensor & out);
};
struct TORCH_API structured_sinh_out : public at::meta::structured_sinh {
void impl(const at::Tensor & self, const at::Tensor & out);
};
TORCH_API at::Tensor detach(const at::Tensor & self);
TORCH_API at::Tensor & detach_(at::Tensor & self);
TORCH_API int64_t size(const at::Tensor & self, int64_t dim);
TORCH_API int64_t size(const at::Tensor & self, at::Dimname dim);
TORCH_API at::Tensor slice(const at::Tensor & self, int64_t dim=0, c10::optional<int64_t> start=c10::nullopt, c10::optional<int64_t> end=c10::nullopt, int64_t step=1);
TORCH_API at::Tensor slice_backward(const at::Tensor & grad_output, at::IntArrayRef input_sizes, int64_t dim, int64_t start, int64_t end, int64_t step);
TORCH_API ::std::tuple<at::Tensor,at::Tensor> slogdet(const at::Tensor & self);
TORCH_API at::Tensor smm(const at::Tensor & self, const at::Tensor & mat2);
TORCH_API at::Tensor softmax(const at::Tensor & self, int64_t dim, c10::optional<at::ScalarType> dtype=c10::nullopt);
TORCH_API at::Tensor softmax(const at::Tensor & self, at::Dimname dim, c10::optional<at::ScalarType> dtype=c10::nullopt);
struct TORCH_API structured_softmax_cpu_out : public at::meta::structured__softmax {
void impl(const at::Tensor & self, int64_t dim, bool half_to_float, const at::Tensor & out);
};
struct TORCH_API structured_softmax_cuda_out : public at::meta::structured__softmax {
void impl(const at::Tensor & self, int64_t dim, bool half_to_float, const at::Tensor & out);
};
TORCH_API at::Tensor mkldnn_softmax(const at::Tensor & self, int64_t dim, bool half_to_float);
struct TORCH_API structured_softmax_backward_cpu_out : public at::meta::structured__softmax_backward_data {
void impl(const at::Tensor & grad_output, const at::Tensor & output, int64_t dim, const at::Tensor & self, const at::Tensor & grad_input);
};
struct TORCH_API structured_softmax_backward_cuda_out : public at::meta::structured__softmax_backward_data {
void impl(const at::Tensor & grad_output, const at::Tensor & output, int64_t dim, const at::Tensor & self, const at::Tensor & grad_input);
};
TORCH_API ::std::vector<at::Tensor> unsafe_split(const at::Tensor & self, int64_t split_size, int64_t dim=0);
TORCH_API ::std::vector<at::Tensor> split(const at::Tensor & self, int64_t split_size, int64_t dim=0);
TORCH_API ::std::vector<at::Tensor> unsafe_split_with_sizes(const at::Tensor & self, at::IntArrayRef split_sizes, int64_t dim=0);
TORCH_API ::std::vector<at::Tensor> split_with_sizes(const at::Tensor & self, at::IntArrayRef split_sizes, int64_t dim=0);
TORCH_API ::std::vector<at::Tensor> hsplit(const at::Tensor & self, int64_t sections);
TORCH_API ::std::vector<at::Tensor> hsplit(const at::Tensor & self, at::IntArrayRef indices);
TORCH_API ::std::vector<at::Tensor> vsplit(const at::Tensor & self, int64_t sections);
TORCH_API ::std::vector<at::Tensor> vsplit(const at::Tensor & self, at::IntArrayRef indices);
TORCH_API ::std::vector<at::Tensor> dsplit(const at::Tensor & self, int64_t sections);
TORCH_API ::std::vector<at::Tensor> dsplit(const at::Tensor & self, at::IntArrayRef indices);
TORCH_API at::Tensor squeeze(const at::Tensor & self);
TORCH_API at::Tensor & squeeze_(at::Tensor & self);
TORCH_API at::Tensor squeeze(const at::Tensor & self, int64_t dim);
TORCH_API at::Tensor & squeeze_(at::Tensor & self, int64_t dim);
TORCH_API at::Tensor squeeze(const at::Tensor & self, at::Dimname dim);
TORCH_API at::Tensor & squeeze_(at::Tensor & self, at::Dimname dim);
TORCH_API at::Tensor sspaddmm(const at::Tensor & self, const at::Tensor & mat1, const at::Tensor & mat2, const at::Scalar & beta=1, const at::Scalar & alpha=1);
TORCH_API at::Tensor & _sspaddmm_out_only_sparse(const at::Tensor & self, const at::Tensor & mat1, const at::Tensor & mat2, const at::Scalar & beta, const at::Scalar & alpha, at::Tensor & out);
TORCH_API at::Tensor & _sspaddmm_out_only_sparse_cuda(const at::Tensor & self, const at::Tensor & mat1, const at::Tensor & mat2, const at::Scalar & beta, const at::Scalar & alpha, at::Tensor & out);
TORCH_API at::Tensor & _sspaddmm_out_cpu(const at::Tensor & self, const at::Tensor & mat1, const at::Tensor & mat2, const at::Scalar & beta, const at::Scalar & alpha, at::Tensor & out);
TORCH_API at::Tensor & _sspaddmm_out_cuda(const at::Tensor & self, const at::Tensor & mat1, const at::Tensor & mat2, const at::Scalar & beta, const at::Scalar & alpha, at::Tensor & out);
TORCH_API at::Tensor stack(at::TensorList tensors, int64_t dim=0);
TORCH_API at::Tensor & stack_out(at::TensorList tensors, int64_t dim, at::Tensor & out);
TORCH_API at::Tensor _stack(at::TensorList tensors, int64_t dim=0);
TORCH_API at::Tensor & _stack_out(at::TensorList tensors, int64_t dim, at::Tensor & out);
TORCH_API at::Tensor _stack_cpu(at::TensorList tensors, int64_t dim=0);
TORCH_API at::Tensor & _stack_out_cpu(at::TensorList tensors, int64_t dim, at::Tensor & out);
TORCH_API at::Tensor hstack(at::TensorList tensors);
TORCH_API at::Tensor & hstack_out(at::TensorList tensors, at::Tensor & out);
TORCH_API at::Tensor vstack(at::TensorList tensors);
TORCH_API at::Tensor & vstack_out(at::TensorList tensors, at::Tensor & out);
TORCH_API at::Tensor dstack(at::TensorList tensors);
TORCH_API at::Tensor & dstack_out(at::TensorList tensors, at::Tensor & out);
TORCH_API at::Tensor stft(const at::Tensor & self, int64_t n_fft, c10::optional<int64_t> hop_length=c10::nullopt, c10::optional<int64_t> win_length=c10::nullopt, const c10::optional<at::Tensor> & window={}, bool normalized=false, c10::optional<bool> onesided=c10::nullopt, c10::optional<bool> return_complex=c10::nullopt);
TORCH_API at::Tensor istft(const at::Tensor & self, int64_t n_fft, c10::optional<int64_t> hop_length=c10::nullopt, c10::optional<int64_t> win_length=c10::nullopt, const c10::optional<at::Tensor> & window={}, bool center=true, bool normalized=false, c10::optional<bool> onesided=c10::nullopt, c10::optional<int64_t> length=c10::nullopt, bool return_complex=false);
TORCH_API int64_t stride(const at::Tensor & self, int64_t dim);
TORCH_API int64_t stride(const at::Tensor & self, at::Dimname dim);
TORCH_API at::Tensor sum(const at::Tensor & self, c10::optional<at::ScalarType> dtype=c10::nullopt);
struct TORCH_API structured_sum_out : public at::meta::structured_sum_dim_IntList {
void impl(const at::Tensor & self, at::IntArrayRef dim, bool keepdim, c10::optional<at::ScalarType> dtype, const at::Tensor & out);
};
TORCH_API at::Tensor sum(const at::Tensor & self, at::DimnameList dim, bool keepdim=false, c10::optional<at::ScalarType> dtype=c10::nullopt);
TORCH_API at::Tensor & sum_out(const at::Tensor & self, at::DimnameList dim, bool keepdim, c10::optional<at::ScalarType> dtype, at::Tensor & out);
TORCH_API at::Tensor nansum(const at::Tensor & self, c10::optional<at::ScalarType> dtype=c10::nullopt);
TORCH_API at::Tensor nansum(const at::Tensor & self, at::IntArrayRef dim, bool keepdim=false, c10::optional<at::ScalarType> dtype=c10::nullopt);
TORCH_API at::Tensor & nansum_out(const at::Tensor & self, at::IntArrayRef dim, bool keepdim, c10::optional<at::ScalarType> dtype, at::Tensor & out);
TORCH_API at::Tensor sum_to_size(const at::Tensor & self, at::IntArrayRef size);
struct TORCH_API structured_sqrt_out : public at::meta::structured_sqrt {
void impl(const at::Tensor & self, const at::Tensor & out);
};
TORCH_API at::Tensor sqrt_sparse(const at::Tensor & self);
TORCH_API at::Tensor & sqrt_out_sparse(const at::Tensor & self, at::Tensor & out);
TORCH_API at::Tensor square(const at::Tensor & self);
TORCH_API at::Tensor & square_(at::Tensor & self);
TORCH_API at::Tensor & square_out(const at::Tensor & self, at::Tensor & out);
TORCH_API at::Tensor std(const at::Tensor & self, bool unbiased=true);
TORCH_API at::Tensor std(const at::Tensor & self, at::IntArrayRef dim, bool unbiased=true, bool keepdim=false);
TORCH_API at::Tensor & std_out(const at::Tensor & self, at::IntArrayRef dim, bool unbiased, bool keepdim, at::Tensor & out);
TORCH_API at::Tensor std(const at::Tensor & self, c10::optional<at::IntArrayRef> dim, c10::optional<int64_t> correction, bool keepdim=false);
TORCH_API at::Tensor & std_out(const at::Tensor & self, c10::optional<at::IntArrayRef> dim, c10::optional<int64_t> correction, bool keepdim, at::Tensor & out);
TORCH_API ::std::tuple<at::Tensor,at::Tensor> std_mean(const at::Tensor & self, bool unbiased=true);
TORCH_API ::std::tuple<at::Tensor,at::Tensor> std_mean(const at::Tensor & self, at::IntArrayRef dim, bool unbiased=true, bool keepdim=false);
TORCH_API ::std::tuple<at::Tensor,at::Tensor> std_mean(const at::Tensor & self, c10::optional<at::IntArrayRef> dim, c10::optional<int64_t> correction, bool keepdim=false);
TORCH_API ::std::tuple<at::Tensor,at::Tensor> std_mean(const at::Tensor & self, at::DimnameList dim, bool unbiased=true, bool keepdim=false);
TORCH_API ::std::tuple<at::Tensor,at::Tensor> std_mean(const at::Tensor & self, at::DimnameList dim, c10::optional<int64_t> correction, bool keepdim=false);
TORCH_API at::Tensor std(const at::Tensor & self, at::DimnameList dim, bool unbiased=true, bool keepdim=false);
TORCH_API at::Tensor & std_out(const at::Tensor & self, at::DimnameList dim, bool unbiased, bool keepdim, at::Tensor & out);
TORCH_API at::Tensor std(const at::Tensor & self, at::DimnameList dim, c10::optional<int64_t> correction, bool keepdim=false);
TORCH_API at::Tensor & std_out(const at::Tensor & self, at::DimnameList dim, c10::optional<int64_t> correction, bool keepdim, at::Tensor & out);
TORCH_API at::Tensor prod(const at::Tensor & self, c10::optional<at::ScalarType> dtype=c10::nullopt);
struct TORCH_API structured_prod_out : public at::meta::structured_prod_dim_int {
void impl(const at::Tensor & self, int64_t dim, bool keepdim, c10::optional<at::ScalarType> dtype, const at::Tensor & out);
};
TORCH_API at::Tensor prod(const at::Tensor & self, at::Dimname dim, bool keepdim=false, c10::optional<at::ScalarType> dtype=c10::nullopt);
TORCH_API at::Tensor & prod_out(const at::Tensor & self, at::Dimname dim, bool keepdim, c10::optional<at::ScalarType> dtype, at::Tensor & out);
TORCH_API at::Tensor t(const at::Tensor & self);
TORCH_API at::Tensor & t_(at::Tensor & self);
struct TORCH_API structured_tan_out : public at::meta::structured_tan {
void impl(const at::Tensor & self, const at::Tensor & out);
};
struct TORCH_API structured_tanh_out : public at::meta::structured_tanh {
void impl(const at::Tensor & self, const at::Tensor & out);
};
TORCH_API at::Tensor mkldnn_tanh(const at::Tensor & self);
TORCH_API at::Tensor & mkldnn_tanh_(at::Tensor & self);
TORCH_API at::Tensor tanh_quantized_cpu(const at::Tensor & self);
TORCH_API at::Tensor tensordot(const at::Tensor & self, const at::Tensor & other, at::IntArrayRef dims_self, at::IntArrayRef dims_other);
TORCH_API at::Tensor & tensordot_out(const at::Tensor & self, const at::Tensor & other, at::IntArrayRef dims_self, at::IntArrayRef dims_other, at::Tensor & out);
struct TORCH_API structured_threshold_out : public at::meta::structured_threshold {
void impl(const at::Tensor & self, const at::Scalar & threshold, const at::Scalar & value, const at::Tensor & out);
};
TORCH_API at::Tensor threshold_quantized_cpu(const at::Tensor & self, const at::Scalar & threshold, const at::Scalar & value);
struct TORCH_API structured_threshold_backward_out : public at::meta::structured_threshold_backward {
void impl(const at::Tensor & grad_output, const at::Tensor & self, const at::Scalar & threshold, const at::Tensor & grad_input);
};
TORCH_API at::Tensor mkldnn_relu_backward(const at::Tensor & grad_output, const at::Tensor & self, const at::Scalar & threshold);
TORCH_API at::Tensor tile(const at::Tensor & self, at::IntArrayRef dims);
TORCH_API at::Tensor transpose(const at::Tensor & self, int64_t dim0, int64_t dim1);
TORCH_API at::Tensor & transpose_(at::Tensor & self, int64_t dim0, int64_t dim1);
TORCH_API at::Tensor transpose(const at::Tensor & self, at::Dimname dim0, at::Dimname dim1);
TORCH_API at::Tensor mkldnn_transpose(const at::Tensor & self, int64_t dim0, int64_t dim1);
TORCH_API at::Tensor & mkldnn_transpose_(at::Tensor & self, int64_t dim0, int64_t dim1);
TORCH_API at::Tensor one_hot(const at::Tensor & self, int64_t num_classes=-1);
TORCH_API at::Tensor flip(const at::Tensor & self, at::IntArrayRef dims);
TORCH_API at::Tensor fliplr(const at::Tensor & self);
TORCH_API at::Tensor flipud(const at::Tensor & self);
TORCH_API at::Tensor roll_cpu(const at::Tensor & self, at::IntArrayRef shifts, at::IntArrayRef dims={});
TORCH_API at::Tensor roll_cuda(const at::Tensor & self, at::IntArrayRef shifts, at::IntArrayRef dims={});
TORCH_API at::Tensor rot90(const at::Tensor & self, int64_t k=1, at::IntArrayRef dims={0,1});
TORCH_API at::Tensor trapezoid(const at::Tensor & y, const at::Tensor & x, int64_t dim=-1);
TORCH_API at::Tensor trapezoid(const at::Tensor & y, const at::Scalar & dx=1, int64_t dim=-1);
TORCH_API at::Tensor trapz(const at::Tensor & y, const at::Tensor & x, int64_t dim=-1);
TORCH_API at::Tensor trapz(const at::Tensor & y, double dx=1, int64_t dim=-1);
TORCH_API at::Tensor _trilinear(const at::Tensor & i1, const at::Tensor & i2, const at::Tensor & i3, at::IntArrayRef expand1, at::IntArrayRef expand2, at::IntArrayRef expand3, at::IntArrayRef sumdim, int64_t unroll_dim=1);
TORCH_API at::Tensor triplet_margin_loss(const at::Tensor & anchor, const at::Tensor & positive, const at::Tensor & negative, double margin=1.0, double p=2, double eps=1e-06, bool swap=false, int64_t reduction=at::Reduction::Mean);
TORCH_API at::Tensor trunc(const at::Tensor & self);
TORCH_API at::Tensor & trunc_(at::Tensor & self);
struct TORCH_API structured_trunc_out : public at::meta::structured_trunc {
void impl(const at::Tensor & self, const at::Tensor & out);
};
TORCH_API at::Tensor fix(const at::Tensor & self);
TORCH_API at::Tensor & fix_out(const at::Tensor & self, at::Tensor & out);
TORCH_API at::Tensor & fix_(at::Tensor & self);
TORCH_API at::Tensor type_as(const at::Tensor & self, const at::Tensor & other);
TORCH_API bool _has_compatible_shallow_copy_type(const at::Tensor & self, const at::Tensor & from);
TORCH_API ::std::tuple<at::Tensor,at::Tensor> _unique_cpu(const at::Tensor & self, bool sorted=true, bool return_inverse=false);
TORCH_API ::std::tuple<at::Tensor,at::Tensor> _unique_cuda(const at::Tensor & self, bool sorted=true, bool return_inverse=false);
TORCH_API ::std::tuple<at::Tensor,at::Tensor,at::Tensor> unique_dim_cpu(const at::Tensor & self, int64_t dim, bool sorted=true, bool return_inverse=false, bool return_counts=false);
TORCH_API ::std::tuple<at::Tensor,at::Tensor,at::Tensor> unique_dim_cuda(const at::Tensor & self, int64_t dim, bool sorted=true, bool return_inverse=false, bool return_counts=false);
TORCH_API ::std::tuple<at::Tensor,at::Tensor,at::Tensor> unique_consecutive_cpu(const at::Tensor & self, bool return_inverse=false, bool return_counts=false, c10::optional<int64_t> dim=c10::nullopt);
TORCH_API ::std::tuple<at::Tensor,at::Tensor,at::Tensor> unique_consecutive_cuda(const at::Tensor & self, bool return_inverse=false, bool return_counts=false, c10::optional<int64_t> dim=c10::nullopt);
TORCH_API ::std::tuple<at::Tensor,at::Tensor,at::Tensor> unique_dim_consecutive_cpu(const at::Tensor & self, int64_t dim, bool return_inverse=false, bool return_counts=false);
TORCH_API ::std::tuple<at::Tensor,at::Tensor,at::Tensor> unique_dim_consecutive_cuda(const at::Tensor & self, int64_t dim, bool return_inverse=false, bool return_counts=false);
TORCH_API ::std::tuple<at::Tensor,at::Tensor,at::Tensor> _unique2_cpu(const at::Tensor & self, bool sorted=true, bool return_inverse=false, bool return_counts=false);
TORCH_API ::std::tuple<at::Tensor,at::Tensor,at::Tensor> _unique2_cuda(const at::Tensor & self, bool sorted=true, bool return_inverse=false, bool return_counts=false);
TORCH_API at::Tensor _unsafe_view(const at::Tensor & self, at::IntArrayRef size);
TORCH_API at::Tensor unsqueeze(const at::Tensor & self, int64_t dim);
TORCH_API at::Tensor & unsqueeze_(at::Tensor & self, int64_t dim);
TORCH_API at::Tensor vander(const at::Tensor & x, c10::optional<int64_t> N=c10::nullopt, bool increasing=false);
TORCH_API at::Tensor var(const at::Tensor & self, bool unbiased=true);
TORCH_API at::Tensor var(const at::Tensor & self, at::IntArrayRef dim, bool unbiased=true, bool keepdim=false);
TORCH_API at::Tensor & var_out(const at::Tensor & self, at::IntArrayRef dim, bool unbiased, bool keepdim, at::Tensor & out);
TORCH_API at::Tensor var(const at::Tensor & self, c10::optional<at::IntArrayRef> dim, c10::optional<int64_t> correction, bool keepdim=false);
TORCH_API at::Tensor & var_out(const at::Tensor & self, c10::optional<at::IntArrayRef> dim, c10::optional<int64_t> correction, bool keepdim, at::Tensor & out);
TORCH_API at::Tensor var(const at::Tensor & self, at::DimnameList dim, bool unbiased=true, bool keepdim=false);
TORCH_API at::Tensor & var_out(const at::Tensor & self, at::DimnameList dim, bool unbiased, bool keepdim, at::Tensor & out);
TORCH_API at::Tensor var(const at::Tensor & self, at::DimnameList dim, c10::optional<int64_t> correction, bool keepdim=false);
TORCH_API at::Tensor & var_out(const at::Tensor & self, at::DimnameList dim, c10::optional<int64_t> correction, bool keepdim, at::Tensor & out);
TORCH_API ::std::tuple<at::Tensor,at::Tensor> var_mean(const at::Tensor & self, bool unbiased=true);
TORCH_API ::std::tuple<at::Tensor,at::Tensor> var_mean(const at::Tensor & self, at::IntArrayRef dim, bool unbiased=true, bool keepdim=false);
TORCH_API ::std::tuple<at::Tensor,at::Tensor> var_mean(const at::Tensor & self, c10::optional<at::IntArrayRef> dim, c10::optional<int64_t> correction, bool keepdim=false);
TORCH_API ::std::tuple<at::Tensor,at::Tensor> var_mean(const at::Tensor & self, at::DimnameList dim, bool unbiased=true, bool keepdim=false);
TORCH_API ::std::tuple<at::Tensor,at::Tensor> var_mean(const at::Tensor & self, at::DimnameList dim, c10::optional<int64_t> correction, bool keepdim=false);
TORCH_API at::Tensor view_as(const at::Tensor & self, const at::Tensor & other);
TORCH_API at::Tensor where(const at::Tensor & condition, const at::Tensor & self, const at::Tensor & other);
TORCH_API at::Tensor where(const at::Tensor & condition, const at::Scalar & self, const at::Tensor & other);
TORCH_API at::Tensor where(const at::Tensor & condition, const at::Tensor & self, const at::Scalar & other);
TORCH_API at::Tensor where(const at::Tensor & condition, const at::Scalar & self, const at::Scalar & other);
TORCH_API ::std::vector<at::Tensor> where(const at::Tensor & condition);
TORCH_API at::Tensor _s_where(const at::Tensor & condition, const at::Tensor & self, const at::Tensor & other);
TORCH_API at::Tensor norm_except_dim(const at::Tensor & v, int64_t pow=2, int64_t dim=0);
TORCH_API at::Tensor _weight_norm(const at::Tensor & v, const at::Tensor & g, int64_t dim=0);
TORCH_API ::std::tuple<at::Tensor,at::Tensor> weight_norm_cuda(const at::Tensor & v, const at::Tensor & g, int64_t dim=0);
TORCH_API ::std::tuple<at::Tensor,at::Tensor> weight_norm_cuda_backward(const at::Tensor & grad_w, const at::Tensor & saved_v, const at::Tensor & saved_g, const at::Tensor & saved_norms, int64_t dim);
TORCH_API ::std::tuple<at::Tensor,at::Tensor> _weight_norm_differentiable_backward(const at::Tensor & grad_w, const at::Tensor & saved_v, const at::Tensor & saved_g, const at::Tensor & saved_norms, int64_t dim);
TORCH_API at::Tensor zeros(at::IntArrayRef size, c10::optional<at::DimnameList> names, c10::optional<at::ScalarType> dtype={}, c10::optional<at::Layout> layout={}, c10::optional<at::Device> device={}, c10::optional<bool> pin_memory={});
TORCH_API at::Tensor zeros(at::IntArrayRef size, c10::optional<at::ScalarType> dtype={}, c10::optional<at::Layout> layout={}, c10::optional<at::Device> device={}, c10::optional<bool> pin_memory={});
TORCH_API at::Tensor & zeros_out(at::IntArrayRef size, at::Tensor & out);
TORCH_API at::Tensor zeros_like(const at::Tensor & self, c10::optional<at::ScalarType> dtype={}, c10::optional<at::Layout> layout={}, c10::optional<at::Device> device={}, c10::optional<bool> pin_memory={}, c10::optional<at::MemoryFormat> memory_format=c10::nullopt);
TORCH_API at::Tensor _standard_gamma_grad_cpu(const at::Tensor & self, const at::Tensor & output);
TORCH_API at::Tensor _standard_gamma_grad_cuda(const at::Tensor & self, const at::Tensor & output);
TORCH_API at::Tensor _s_gamma_cpu(const at::Tensor & self, c10::optional<at::Generator> generator=c10::nullopt);
TORCH_API at::Tensor _s_gamma_cuda(const at::Tensor & self, c10::optional<at::Generator> generator=c10::nullopt);
TORCH_API at::Tensor _dirichlet_grad_cpu(const at::Tensor & x, const at::Tensor & alpha, const at::Tensor & total);
TORCH_API at::Tensor _dirichlet_grad_cuda(const at::Tensor & x, const at::Tensor & alpha, const at::Tensor & total);
TORCH_API at::Tensor _s_dirichlet_cpu(const at::Tensor & self, c10::optional<at::Generator> generator=c10::nullopt);
TORCH_API at::Tensor _s_dirichlet_cuda(const at::Tensor & self, c10::optional<at::Generator> generator=c10::nullopt);
TORCH_API at::Tensor _s_poisson_cpu(const at::Tensor & self, c10::optional<at::Generator> generator=c10::nullopt);
TORCH_API at::Tensor _s_poisson_cuda(const at::Tensor & self, c10::optional<at::Generator> generator=c10::nullopt);
TORCH_API at::Tensor _s_binomial_cpu(const at::Tensor & count, const at::Tensor & prob, c10::optional<at::Generator> generator=c10::nullopt);
TORCH_API at::Tensor _s_binomial_cuda(const at::Tensor & count, const at::Tensor & prob, c10::optional<at::Generator> generator=c10::nullopt);
TORCH_API at::Tensor norm_sparse(const at::Tensor & self, const at::Scalar & p=2);
TORCH_API at::Tensor norm_sparse(const at::Tensor & self, const c10::optional<at::Scalar> & p, at::IntArrayRef dim, bool keepdim, c10::optional<at::ScalarType> dtype);
TORCH_API at::Tensor _sparse_sum(const at::Tensor & self);
TORCH_API at::Tensor _sparse_sum(const at::Tensor & self, at::ScalarType dtype);
TORCH_API at::Tensor _sparse_sum(const at::Tensor & self, at::IntArrayRef dim);
TORCH_API at::Tensor _sparse_sum(const at::Tensor & self, at::IntArrayRef dim, at::ScalarType dtype);
TORCH_API at::Tensor _sparse_sum_backward_cpu(const at::Tensor & grad, const at::Tensor & self, at::IntArrayRef dim);
TORCH_API at::Tensor _sparse_sum_backward_cuda(const at::Tensor & grad, const at::Tensor & self, at::IntArrayRef dim);
TORCH_API at::Tensor _sparse_softmax(const at::Tensor & self, int64_t dim, c10::optional<at::ScalarType> dtype=c10::nullopt);
TORCH_API at::Tensor _sparse_softmax(const at::Tensor & self, at::Dimname dim, c10::optional<at::ScalarType> dtype=c10::nullopt);
TORCH_API at::Tensor softmax_sparse_cpu(const at::Tensor & self, int64_t dim, bool half_to_float);
TORCH_API at::Tensor softmax_sparse_cuda(const at::Tensor & self, int64_t dim, bool half_to_float);
TORCH_API at::Tensor softmax_backward_sparse_cpu(const at::Tensor & grad_output, const at::Tensor & output, int64_t dim, const at::Tensor & self);
TORCH_API at::Tensor softmax_backward_sparse_cuda(const at::Tensor & grad_output, const at::Tensor & output, int64_t dim, const at::Tensor & self);
TORCH_API at::Tensor _sparse_log_softmax(const at::Tensor & self, int64_t dim, c10::optional<at::ScalarType> dtype=c10::nullopt);
TORCH_API at::Tensor _sparse_log_softmax(const at::Tensor & self, at::Dimname dim, c10::optional<at::ScalarType> dtype=c10::nullopt);
TORCH_API at::Tensor log_softmax_sparse_cpu(const at::Tensor & self, int64_t dim, bool half_to_float);
TORCH_API at::Tensor log_softmax_sparse_cuda(const at::Tensor & self, int64_t dim, bool half_to_float);
TORCH_API at::Tensor log_softmax_backward_sparse_cpu(const at::Tensor & grad_output, const at::Tensor & output, int64_t dim, const at::Tensor & self);
TORCH_API at::Tensor log_softmax_backward_sparse_cuda(const at::Tensor & grad_output, const at::Tensor & output, int64_t dim, const at::Tensor & self);
TORCH_API at::Tensor norm(const at::Tensor & self, const c10::optional<at::Scalar> & p, at::ScalarType dtype);
TORCH_API at::Tensor norm(const at::Tensor & self, const at::Scalar & p=2);
struct TORCH_API structured_norm_dtype_out : public at::meta::structured_norm_ScalarOpt_dim_dtype {
void impl(const at::Tensor & self, at::OptionalScalarRef p, at::IntArrayRef dim, bool keepdim, at::ScalarType dtype, const at::Tensor & out);
};
TORCH_API at::Tensor sparse_dtype_norm(const at::Tensor & self, const c10::optional<at::Scalar> & p, at::IntArrayRef dim, bool keepdim, at::ScalarType dtype);
struct TORCH_API structured_norm_out : public at::meta::structured_norm_ScalarOpt_dim {
void impl(const at::Tensor & self, at::OptionalScalarRef p, at::IntArrayRef dim, bool keepdim, const at::Tensor & out);
};
TORCH_API at::Tensor sparse_norm(const at::Tensor & self, const c10::optional<at::Scalar> & p, at::IntArrayRef dim, bool keepdim=false);
TORCH_API at::Tensor norm(const at::Tensor & self, const c10::optional<at::Scalar> & p, at::DimnameList dim, bool keepdim, at::ScalarType dtype);
TORCH_API at::Tensor & norm_out(const at::Tensor & self, const c10::optional<at::Scalar> & p, at::DimnameList dim, bool keepdim, at::ScalarType dtype, at::Tensor & out);
TORCH_API at::Tensor norm(const at::Tensor & self, const c10::optional<at::Scalar> & p, at::DimnameList dim, bool keepdim=false);
TORCH_API at::Tensor & norm_out(const at::Tensor & self, const c10::optional<at::Scalar> & p, at::DimnameList dim, bool keepdim, at::Tensor & out);
TORCH_API ::std::tuple<at::Tensor,at::Tensor> frexp(const at::Tensor & self);
TORCH_API ::std::tuple<at::Tensor &,at::Tensor &> frexp_out(const at::Tensor & self, at::Tensor & mantissa, at::Tensor & exponent);
TORCH_API at::Tensor frobenius_norm(const at::Tensor & self);
TORCH_API at::Tensor frobenius_norm(const at::Tensor & self, at::IntArrayRef dim, bool keepdim=false);
TORCH_API at::Tensor & frobenius_norm_out(const at::Tensor & self, at::IntArrayRef dim, bool keepdim, at::Tensor & out);
TORCH_API at::Tensor nuclear_norm(const at::Tensor & self, bool keepdim=false);
TORCH_API at::Tensor & nuclear_norm_out(const at::Tensor & self, bool keepdim, at::Tensor & out);
TORCH_API at::Tensor nuclear_norm(const at::Tensor & self, at::IntArrayRef dim, bool keepdim=false);
TORCH_API at::Tensor & nuclear_norm_out(const at::Tensor & self, at::IntArrayRef dim, bool keepdim, at::Tensor & out);
TORCH_API at::Tensor clone(const at::Tensor & self, c10::optional<at::MemoryFormat> memory_format=c10::nullopt);
TORCH_API at::Tensor clone_sparse(const at::Tensor & self, c10::optional<at::MemoryFormat> memory_format=c10::nullopt);
TORCH_API at::Tensor mkldnn_clone(const at::Tensor & self, c10::optional<at::MemoryFormat> memory_format=c10::nullopt);
TORCH_API at::Tensor quantized_clone(const at::Tensor & self, c10::optional<at::MemoryFormat> memory_format=c10::nullopt);
TORCH_API at::Tensor positive(const at::Tensor & self);
TORCH_API const at::Tensor & resize_as_(const at::Tensor & self, const at::Tensor & the_template, c10::optional<at::MemoryFormat> memory_format=c10::nullopt);
TORCH_API const at::Tensor & resize_as_sparse_(const at::Tensor & self, const at::Tensor & the_template);
TORCH_API const at::Tensor & resize_as_sparse_csr_(const at::Tensor & self, const at::Tensor & the_template);
TORCH_API at::Tensor & zero_(at::Tensor & self);
TORCH_API at::Tensor & zero_sparse_(at::Tensor & self);
TORCH_API at::Tensor & mkldnn_zero_(at::Tensor & self);
TORCH_API at::Tensor & zero_meta_(at::Tensor & self);
struct TORCH_API structured_sub_out : public at::meta::structured_sub_Tensor {
void impl(const at::Tensor & self, const at::Tensor & other, const at::Scalar & alpha, const at::Tensor & out);
};
TORCH_API at::Tensor sub_sparse(const at::Tensor & self, const at::Tensor & other, const at::Scalar & alpha=1);
TORCH_API at::Tensor & sub_out_sparse(const at::Tensor & self, const at::Tensor & other, const at::Scalar & alpha, at::Tensor & out);
TORCH_API at::Tensor & sub_sparse_(at::Tensor & self, const at::Tensor & other, const at::Scalar & alpha=1);
TORCH_API at::Tensor sub(const at::Tensor & self, const at::Scalar & other, const at::Scalar & alpha=1);
TORCH_API at::Tensor & sub_(at::Tensor & self, const at::Scalar & other, const at::Scalar & alpha=1);
TORCH_API at::Tensor subtract(const at::Tensor & self, const at::Tensor & other, const at::Scalar & alpha=1);
TORCH_API at::Tensor & subtract_out(const at::Tensor & self, const at::Tensor & other, const at::Scalar & alpha, at::Tensor & out);
TORCH_API at::Tensor & subtract_(at::Tensor & self, const at::Tensor & other, const at::Scalar & alpha=1);
TORCH_API at::Tensor subtract(const at::Tensor & self, const at::Scalar & other, const at::Scalar & alpha=1);
TORCH_API at::Tensor & subtract_(at::Tensor & self, const at::Scalar & other, const at::Scalar & alpha=1);
TORCH_API at::Tensor rsub(const at::Tensor & self, const at::Tensor & other, const at::Scalar & alpha=1);
struct TORCH_API structured_heaviside_out : public at::meta::structured_heaviside {
void impl(const at::Tensor & self, const at::Tensor & values, const at::Tensor & out);
};
TORCH_API at::Tensor rsub(const at::Tensor & self, const at::Scalar & other, const at::Scalar & alpha=1);
TORCH_API at::Tensor _sparse_addmm(const at::Tensor & self, const at::Tensor & sparse, const at::Tensor & dense, const at::Scalar & beta=1, const at::Scalar & alpha=1);
struct TORCH_API structured_addmm_out_cpu : public at::meta::structured_addmm {
void impl(const at::Tensor & self, const at::Tensor & mat1, const at::Tensor & mat2, const at::Scalar & beta, const at::Scalar & alpha, const at::Tensor & out);
};
struct TORCH_API structured_addmm_out_cuda : public at::meta::structured_addmm {
void impl(const at::Tensor & self, const at::Tensor & mat1, const at::Tensor & mat2, const at::Scalar & beta, const at::Scalar & alpha, const at::Tensor & out);
};
TORCH_API at::Tensor addmm_sparse_dense_cpu(const at::Tensor & self, const at::Tensor & mat1, const at::Tensor & mat2, const at::Scalar & beta=1, const at::Scalar & alpha=1);
TORCH_API at::Tensor & addmm_out_sparse_dense_cpu(const at::Tensor & self, const at::Tensor & mat1, const at::Tensor & mat2, const at::Scalar & beta, const at::Scalar & alpha, at::Tensor & out);
TORCH_API at::Tensor & s_addmm_sparse_dense_cpu_(at::Tensor & self, const at::Tensor & mat1, const at::Tensor & mat2, const at::Scalar & beta=1, const at::Scalar & alpha=1);
TORCH_API at::Tensor addmm_sparse_dense_cuda(const at::Tensor & self, const at::Tensor & mat1, const at::Tensor & mat2, const at::Scalar & beta=1, const at::Scalar & alpha=1);
TORCH_API at::Tensor & addmm_out_sparse_dense_cuda(const at::Tensor & self, const at::Tensor & mat1, const at::Tensor & mat2, const at::Scalar & beta, const at::Scalar & alpha, at::Tensor & out);
TORCH_API at::Tensor & s_addmm_sparse_dense_cuda_(at::Tensor & self, const at::Tensor & mat1, const at::Tensor & mat2, const at::Scalar & beta=1, const at::Scalar & alpha=1);
TORCH_API at::Tensor addmm_sparse_csr_dense(const at::Tensor & self, const at::Tensor & mat1, const at::Tensor & mat2, const at::Scalar & beta=1, const at::Scalar & alpha=1);
TORCH_API at::Tensor & addmm_out_sparse_csr_dense_cpu(const at::Tensor & self, const at::Tensor & mat1, const at::Tensor & mat2, const at::Scalar & beta, const at::Scalar & alpha, at::Tensor & out);
TORCH_API at::Tensor & addmm_out_sparse_csr_dense_cuda(const at::Tensor & self, const at::Tensor & mat1, const at::Tensor & mat2, const at::Scalar & beta, const at::Scalar & alpha, at::Tensor & out);
TORCH_API at::Tensor sparse_csr_tensor(const at::Tensor & crow_indices, const at::Tensor & col_indices, const at::Tensor & values, at::IntArrayRef size, c10::optional<at::ScalarType> dtype={}, c10::optional<at::Layout> layout={}, c10::optional<at::Device> device={}, c10::optional<bool> pin_memory={});
TORCH_API at::Tensor sparse_csr_tensor(const at::Tensor & crow_indices, const at::Tensor & col_indices, const at::Tensor & values, c10::optional<at::ScalarType> dtype={}, c10::optional<at::Layout> layout={}, c10::optional<at::Device> device={}, c10::optional<bool> pin_memory={});
TORCH_API at::Tensor _sparse_csr_tensor_unsafe(const at::Tensor & crow_indices, const at::Tensor & col_indices, const at::Tensor & values, at::IntArrayRef size, c10::optional<at::ScalarType> dtype={}, c10::optional<at::Layout> layout={}, c10::optional<at::Device> device={}, c10::optional<bool> pin_memory={});
TORCH_API at::Tensor sparse_coo_tensor(at::IntArrayRef size, c10::optional<at::ScalarType> dtype={}, c10::optional<at::Layout> layout={}, c10::optional<at::Device> device={}, c10::optional<bool> pin_memory={});
TORCH_API at::Tensor sparse_coo_tensor(const at::Tensor & indices, const at::Tensor & values, c10::optional<at::ScalarType> dtype={}, c10::optional<at::Layout> layout={}, c10::optional<at::Device> device={}, c10::optional<bool> pin_memory={});
TORCH_API at::Tensor sparse_coo_tensor(const at::Tensor & indices, const at::Tensor & values, at::IntArrayRef size, c10::optional<at::ScalarType> dtype={}, c10::optional<at::Layout> layout={}, c10::optional<at::Device> device={}, c10::optional<bool> pin_memory={});
TORCH_API at::Tensor _sparse_coo_tensor_unsafe(const at::Tensor & indices, const at::Tensor & values, at::IntArrayRef size, c10::optional<at::ScalarType> dtype={}, c10::optional<at::Layout> layout={}, c10::optional<at::Device> device={}, c10::optional<bool> pin_memory={});
TORCH_API void _validate_sparse_coo_tensor_args(const at::Tensor & indices, const at::Tensor & values, at::IntArrayRef size);
TORCH_API void _validate_sparse_csr_tensor_args(const at::Tensor & crow_indices, const at::Tensor & col_indices, const at::Tensor & values, at::IntArrayRef size);
TORCH_API at::Tensor new_with_dims_sparse(int64_t sparse_dim, int64_t dense_dim, at::IntArrayRef size, c10::optional<at::ScalarType> dtype={}, c10::optional<at::Layout> layout={}, c10::optional<at::Device> device={}, c10::optional<bool> pin_memory={});
TORCH_API at::Tensor new_with_dims_and_tensor_sparse(int64_t sparse_dim, int64_t dense_dim, at::IntArrayRef size, const at::Tensor & indices, const at::Tensor & values, c10::optional<at::ScalarType> dtype={}, c10::optional<at::Layout> layout={}, c10::optional<at::Device> device={}, c10::optional<bool> pin_memory={});
TORCH_API const at::Tensor & sparse_resize_(const at::Tensor & self, at::IntArrayRef size, int64_t sparse_dim, int64_t dense_dim);
TORCH_API const at::Tensor & sparse_resize_and_clear_(const at::Tensor & self, at::IntArrayRef size, int64_t sparse_dim, int64_t dense_dim);
TORCH_API at::Tensor sparse_mask_cpu(const at::Tensor & self, const at::Tensor & mask);
TORCH_API at::Tensor sparse_mask_cuda(const at::Tensor & self, const at::Tensor & mask);
TORCH_API ::std::vector<at::Tensor> _to_cpu(at::TensorList tensors);
TORCH_API at::Tensor sparse_to_dense(const at::Tensor & self, c10::optional<at::ScalarType> dtype=c10::nullopt);
TORCH_API at::Tensor mkldnn_to_dense(const at::Tensor & self, c10::optional<at::ScalarType> dtype=c10::nullopt);
TORCH_API at::Tensor to_dense_backward(const at::Tensor & grad, const at::Tensor & input);
TORCH_API int64_t sparse_dim_sparse(const at::Tensor & self);
TORCH_API int64_t sparse_dim_sparse(const at::Tensor & self);
TORCH_API int64_t dense_dim_sparse(const at::Tensor & self);
TORCH_API int64_t dense_dim_sparse(const at::Tensor & self);
TORCH_API int64_t _nnz_sparse(const at::Tensor & self);
TORCH_API int64_t _nnz_sparse_csr(const at::Tensor & self);
TORCH_API at::Tensor coalesce(const at::Tensor & self);
TORCH_API at::Tensor _coalesce_sparse_cpu(const at::Tensor & self);
TORCH_API at::Tensor _coalesce_sparse_cuda(const at::Tensor & self);
TORCH_API bool is_coalesced_sparse(const at::Tensor & self);
TORCH_API at::Tensor _indices_sparse(const at::Tensor & self);
TORCH_API at::Tensor _values_sparse(const at::Tensor & self);
TORCH_API at::Tensor & _coalesced_sparse_(at::Tensor & self, bool coalesced);
TORCH_API at::Tensor indices_sparse(const at::Tensor & self);
TORCH_API at::Tensor values_sparse(const at::Tensor & self);
TORCH_API at::Tensor values_sparse_csr(const at::Tensor & self);
TORCH_API at::Tensor crow_indices_sparse_csr(const at::Tensor & self);
TORCH_API at::Tensor col_indices_sparse_csr(const at::Tensor & self);
TORCH_API at::Tensor hspmm_sparse_cpu(const at::Tensor & mat1, const at::Tensor & mat2);
TORCH_API at::Tensor & hspmm_out_sparse_cpu(const at::Tensor & mat1, const at::Tensor & mat2, at::Tensor & out);
TORCH_API at::Tensor hspmm_sparse_cuda(const at::Tensor & mat1, const at::Tensor & mat2);
TORCH_API at::Tensor & hspmm_out_sparse_cuda(const at::Tensor & mat1, const at::Tensor & mat2, at::Tensor & out);
TORCH_API at::Tensor & copy_sparse_(at::Tensor & self, const at::Tensor & src, bool non_blocking=false);
TORCH_API ::std::vector<at::Tensor> unbind(const at::Tensor & self, int64_t dim=0);
TORCH_API ::std::vector<at::Tensor> unbind(const at::Tensor & self, at::Dimname dim);
TORCH_API at::Tensor dense_to_sparse(const at::Tensor & self, int64_t sparse_dim);
TORCH_API at::Tensor dense_to_sparse(const at::Tensor & self);
TORCH_API at::Tensor dense_to_mkldnn(const at::Tensor & self, c10::optional<at::ScalarType> dtype=c10::nullopt);
TORCH_API at::Tensor mkldnn_reorder_conv2d_weight(const at::Tensor & self, at::IntArrayRef padding=0, at::IntArrayRef stride=1, at::IntArrayRef dilation=1, int64_t groups=1);
TORCH_API at::Tensor mkldnn_reorder_conv3d_weight(const at::Tensor & self, at::IntArrayRef padding=0, at::IntArrayRef stride=1, at::IntArrayRef dilation=1, int64_t groups=1);
TORCH_API at::Tensor to_mkldnn_backward(const at::Tensor & grad, const at::Tensor & input);
TORCH_API at::Tensor quantize_per_tensor(const at::Tensor & self, double scale, int64_t zero_point, at::ScalarType dtype);
TORCH_API at::Tensor quantize_per_tensor_tensor_qparams(const at::Tensor & self, const at::Tensor & scale, const at::Tensor & zero_point, at::ScalarType dtype);
TORCH_API ::std::vector<at::Tensor> quantize_per_tensor_list_cpu(at::TensorList tensors, const at::Tensor & scales, const at::Tensor & zero_points, at::ScalarType dtype);
TORCH_API at::Tensor quantize_per_channel(const at::Tensor & self, const at::Tensor & scales, const at::Tensor & zero_points, int64_t axis, at::ScalarType dtype);
TORCH_API at::Tensor dequantize_cpu(const at::Tensor & self);
TORCH_API at::Tensor dequantize_quantized(const at::Tensor & self);
TORCH_API ::std::vector<at::Tensor> dequantize_tensors_quantized_cpu(at::TensorList tensors);
TORCH_API double q_scale_quant(const at::Tensor & self);
TORCH_API int64_t q_zero_point_quant(const at::Tensor & self);
TORCH_API at::Tensor q_per_channel_scales(const at::Tensor & self);
TORCH_API at::Tensor q_per_channel_zero_points(const at::Tensor & self);
TORCH_API int64_t q_per_channel_axis(const at::Tensor & self);
TORCH_API at::Tensor int_repr_quantized_cpu(const at::Tensor & self);
TORCH_API at::Tensor int_repr_quantized_cuda(const at::Tensor & self);
TORCH_API at::Tensor make_per_tensor_quantized_tensor_cpu(const at::Tensor & self, double scale, int64_t zero_point);
TORCH_API at::Tensor make_per_tensor_quantized_tensor_cuda(const at::Tensor & self, double scale, int64_t zero_point);
TORCH_API at::Tensor make_per_channel_quantized_tensor_cpu(const at::Tensor & self, const at::Tensor & scale, const at::Tensor & zero_point, int64_t axis);
TORCH_API at::Tensor make_per_channel_quantized_tensor_cuda(const at::Tensor & self, const at::Tensor & scale, const at::Tensor & zero_point, int64_t axis);
TORCH_API at::QScheme qscheme_quant(const at::Tensor & self);
TORCH_API at::Tensor fake_quantize_per_tensor_affine(const at::Tensor & self, double scale, int64_t zero_point, int64_t quant_min, int64_t quant_max);
TORCH_API at::Tensor fake_quantize_per_tensor_affine(const at::Tensor & self, const at::Tensor & scale, const at::Tensor & zero_point, int64_t quant_min, int64_t quant_max);
TORCH_API ::std::tuple<at::Tensor,at::Tensor> fake_quantize_per_tensor_affine_cachemask(const at::Tensor & self, double scale, int64_t zero_point, int64_t quant_min, int64_t quant_max);
TORCH_API ::std::tuple<at::Tensor,at::Tensor> _fake_quantize_per_tensor_affine_cachemask_tensor_qparams(const at::Tensor & self, const at::Tensor & scale, const at::Tensor & zero_point, const at::Tensor & fake_quant_enabled, int64_t quant_min, int64_t quant_max);
TORCH_API at::Tensor fake_quantize_per_tensor_affine_cachemask_backward(const at::Tensor & grad, const at::Tensor & mask);
TORCH_API at::Tensor _fake_quantize_learnable_per_tensor_affine(const at::Tensor & self, const at::Tensor & scale, const at::Tensor & zero_point, int64_t quant_min, int64_t quant_max, double grad_factor=1.0);
TORCH_API ::std::tuple<at::Tensor,at::Tensor,at::Tensor> _fake_quantize_learnable_per_tensor_affine_backward(const at::Tensor & grad, const at::Tensor & self, const at::Tensor & scale, const at::Tensor & zero_point, int64_t quant_min, int64_t quant_max, double grad_factor=1.0);
TORCH_API at::Tensor fake_quantize_per_channel_affine(const at::Tensor & self, const at::Tensor & scale, const at::Tensor & zero_point, int64_t axis, int64_t quant_min, int64_t quant_max);
TORCH_API ::std::tuple<at::Tensor,at::Tensor> fake_quantize_per_channel_affine_cachemask(const at::Tensor & self, const at::Tensor & scale, const at::Tensor & zero_point, int64_t axis, int64_t quant_min, int64_t quant_max);
TORCH_API at::Tensor fake_quantize_per_channel_affine_cachemask_backward(const at::Tensor & grad, const at::Tensor & mask);
TORCH_API at::Tensor _fake_quantize_learnable_per_channel_affine(const at::Tensor & self, const at::Tensor & scale, const at::Tensor & zero_point, int64_t axis, int64_t quant_min, int64_t quant_max, double grad_factor=1.0);
TORCH_API ::std::tuple<at::Tensor,at::Tensor,at::Tensor> _fake_quantize_learnable_per_channel_affine_backward(const at::Tensor & grad, const at::Tensor & self, const at::Tensor & scale, const at::Tensor & zero_point, int64_t axis, int64_t quant_min, int64_t quant_max, double grad_factor=1.0);
TORCH_API at::Tensor fused_moving_avg_obs_fake_quant(const at::Tensor & self, const at::Tensor & observer_on, const at::Tensor & fake_quant_on, at::Tensor & running_min, at::Tensor & running_max, at::Tensor & scale, at::Tensor & zero_point, double averaging_const, int64_t quant_min, int64_t quant_max, int64_t ch_axis, bool per_row_fake_quant=false, bool symmetric_quant=false);
TORCH_API ::std::tuple<at::Tensor,at::Tensor> fused_moving_avg_obs_fake_quant_cpu(const at::Tensor & self, const at::Tensor & observer_on, const at::Tensor & fake_quant_on, at::Tensor & running_min, at::Tensor & running_max, at::Tensor & scale, at::Tensor & zero_point, double averaging_const, int64_t quant_min, int64_t quant_max, int64_t ch_axis, bool per_row_fake_quant=false, bool symmetric_quant=false);
TORCH_API ::std::tuple<at::Tensor,at::Tensor> fused_moving_avg_obs_fake_quant_cuda(const at::Tensor & self, const at::Tensor & observer_on, const at::Tensor & fake_quant_on, at::Tensor & running_min, at::Tensor & running_max, at::Tensor & scale, at::Tensor & zero_point, double averaging_const, int64_t quant_min, int64_t quant_max, int64_t ch_axis, bool per_row_fake_quant=false, bool symmetric_quant=false);
TORCH_API ::std::tuple<double,int64_t> _choose_qparams_per_tensor(const at::Tensor & self, bool reduce_range=false);
TORCH_API at::Tensor _saturate_weight_to_fp16(const at::Tensor & weight);
TORCH_API ::std::tuple<at::Tensor,at::Tensor> choose_qparams_optimized(const at::Tensor & input, int64_t numel, int64_t n_bins, double ratio, int64_t bit_width);
TORCH_API at::Tensor _to_copy(const at::Tensor & self, c10::optional<at::ScalarType> dtype={}, c10::optional<at::Layout> layout={}, c10::optional<at::Device> device={}, c10::optional<bool> pin_memory={}, bool non_blocking=false, c10::optional<at::MemoryFormat> memory_format=c10::nullopt);
TORCH_API at::Tensor to(const at::Tensor & self, c10::optional<at::ScalarType> dtype={}, c10::optional<at::Layout> layout={}, c10::optional<at::Device> device={}, c10::optional<bool> pin_memory={}, bool non_blocking=false, bool copy=false, c10::optional<at::MemoryFormat> memory_format=c10::nullopt);
TORCH_API at::Tensor to(const at::Tensor & self, at::Device device, at::ScalarType dtype, bool non_blocking=false, bool copy=false, c10::optional<at::MemoryFormat> memory_format=c10::nullopt);
TORCH_API at::Tensor to(const at::Tensor & self, at::ScalarType dtype, bool non_blocking=false, bool copy=false, c10::optional<at::MemoryFormat> memory_format=c10::nullopt);
TORCH_API at::Tensor to(const at::Tensor & self, const at::Tensor & other, bool non_blocking=false, bool copy=false, c10::optional<at::MemoryFormat> memory_format=c10::nullopt);
TORCH_API ::std::vector<at::Tensor> meshgrid(at::TensorList tensors);
TORCH_API ::std::vector<at::Tensor> meshgrid(at::TensorList tensors, c10::string_view indexing);
TORCH_API at::Tensor cartesian_prod(at::TensorList tensors);
TORCH_API at::Tensor combinations(const at::Tensor & self, int64_t r=2, bool with_replacement=false);
TORCH_API at::Scalar item(const at::Tensor & self);
TORCH_API at::ScalarType result_type(const at::Tensor & tensor, const at::Tensor & other);
TORCH_API at::ScalarType result_type(const at::Tensor & tensor, const at::Scalar & other);
TORCH_API at::ScalarType result_type(const at::Scalar & scalar, const at::Tensor & tensor);
TORCH_API at::ScalarType result_type(const at::Scalar & scalar1, const at::Scalar & scalar2);
TORCH_API bool can_cast(at::ScalarType from, at::ScalarType to);
TORCH_API at::ScalarType promote_types(at::ScalarType type1, at::ScalarType type2);
TORCH_API at::Scalar _local_scalar_dense_cpu(const at::Tensor & self);
TORCH_API at::Scalar _local_scalar_dense_cuda(const at::Tensor & self);
TORCH_API ::std::tuple<at::Tensor,at::Tensor,at::Tensor> _thnn_fused_lstm_cell_cuda(const at::Tensor & input_gates, const at::Tensor & hidden_gates, const at::Tensor & cx, const c10::optional<at::Tensor> & input_bias={}, const c10::optional<at::Tensor> & hidden_bias={});
TORCH_API ::std::tuple<at::Tensor,at::Tensor,at::Tensor,at::Tensor,at::Tensor> _thnn_fused_lstm_cell_backward_cuda(const c10::optional<at::Tensor> & grad_hy, const c10::optional<at::Tensor> & grad_cy, const at::Tensor & cx, const at::Tensor & cy, const at::Tensor & workspace, bool has_bias);
TORCH_API ::std::tuple<at::Tensor,at::Tensor,at::Tensor,at::Tensor,at::Tensor> _thnn_differentiable_lstm_cell_backward(const c10::optional<at::Tensor> & grad_hy, const c10::optional<at::Tensor> & grad_cy, const at::Tensor & input_gates, const at::Tensor & hidden_gates, const c10::optional<at::Tensor> & input_bias, const c10::optional<at::Tensor> & hidden_bias, const at::Tensor & cx, const at::Tensor & cy);
TORCH_API ::std::tuple<at::Tensor,at::Tensor> _thnn_fused_gru_cell_cuda(const at::Tensor & input_gates, const at::Tensor & hidden_gates, const at::Tensor & hx, const c10::optional<at::Tensor> & input_bias={}, const c10::optional<at::Tensor> & hidden_bias={});
TORCH_API ::std::tuple<at::Tensor,at::Tensor,at::Tensor,at::Tensor,at::Tensor> _thnn_fused_gru_cell_backward_cuda(const at::Tensor & grad_hy, const at::Tensor & workspace, bool has_bias);
TORCH_API ::std::tuple<at::Tensor,at::Tensor,at::Tensor,at::Tensor,at::Tensor> _thnn_differentiable_gru_cell_backward(const at::Tensor & grad_hy, const at::Tensor & input_gates, const at::Tensor & hidden_gates, const at::Tensor & hx, const c10::optional<at::Tensor> & input_bias, const c10::optional<at::Tensor> & hidden_bias);
TORCH_API ::std::tuple<at::Tensor,at::Tensor,at::Tensor> lstm(const at::Tensor & input, at::TensorList hx, at::TensorList params, bool has_biases, int64_t num_layers, double dropout, bool train, bool bidirectional, bool batch_first);
TORCH_API ::std::tuple<at::Tensor,at::Tensor,at::Tensor> lstm(const at::Tensor & data, const at::Tensor & batch_sizes, at::TensorList hx, at::TensorList params, bool has_biases, int64_t num_layers, double dropout, bool train, bool bidirectional);
TORCH_API ::std::tuple<at::Tensor,at::Tensor> gru(const at::Tensor & input, const at::Tensor & hx, at::TensorList params, bool has_biases, int64_t num_layers, double dropout, bool train, bool bidirectional, bool batch_first);
TORCH_API ::std::tuple<at::Tensor,at::Tensor> gru(const at::Tensor & data, const at::Tensor & batch_sizes, const at::Tensor & hx, at::TensorList params, bool has_biases, int64_t num_layers, double dropout, bool train, bool bidirectional);
TORCH_API ::std::tuple<at::Tensor,at::Tensor> rnn_tanh(const at::Tensor & input, const at::Tensor & hx, at::TensorList params, bool has_biases, int64_t num_layers, double dropout, bool train, bool bidirectional, bool batch_first);
TORCH_API ::std::tuple<at::Tensor,at::Tensor> rnn_tanh(const at::Tensor & data, const at::Tensor & batch_sizes, const at::Tensor & hx, at::TensorList params, bool has_biases, int64_t num_layers, double dropout, bool train, bool bidirectional);
TORCH_API ::std::tuple<at::Tensor,at::Tensor> rnn_relu(const at::Tensor & input, const at::Tensor & hx, at::TensorList params, bool has_biases, int64_t num_layers, double dropout, bool train, bool bidirectional, bool batch_first);
TORCH_API ::std::tuple<at::Tensor,at::Tensor> rnn_relu(const at::Tensor & data, const at::Tensor & batch_sizes, const at::Tensor & hx, at::TensorList params, bool has_biases, int64_t num_layers, double dropout, bool train, bool bidirectional);
TORCH_API ::std::tuple<at::Tensor,at::Tensor> lstm_cell(const at::Tensor & input, at::TensorList hx, const at::Tensor & w_ih, const at::Tensor & w_hh, const c10::optional<at::Tensor> & b_ih={}, const c10::optional<at::Tensor> & b_hh={});
TORCH_API at::Tensor gru_cell(const at::Tensor & input, const at::Tensor & hx, const at::Tensor & w_ih, const at::Tensor & w_hh, const c10::optional<at::Tensor> & b_ih={}, const c10::optional<at::Tensor> & b_hh={});
TORCH_API at::Tensor rnn_tanh_cell(const at::Tensor & input, const at::Tensor & hx, const at::Tensor & w_ih, const at::Tensor & w_hh, const c10::optional<at::Tensor> & b_ih={}, const c10::optional<at::Tensor> & b_hh={});
TORCH_API at::Tensor rnn_relu_cell(const at::Tensor & input, const at::Tensor & hx, const at::Tensor & w_ih, const at::Tensor & w_hh, const c10::optional<at::Tensor> & b_ih={}, const c10::optional<at::Tensor> & b_hh={});
TORCH_API ::std::tuple<at::Tensor,at::Tensor> quantized_lstm_cell(const at::Tensor & input, at::TensorList hx, const at::Tensor & w_ih, const at::Tensor & w_hh, const at::Tensor & b_ih, const at::Tensor & b_hh, const at::Tensor & packed_ih, const at::Tensor & packed_hh, const at::Tensor & col_offsets_ih, const at::Tensor & col_offsets_hh, const at::Scalar & scale_ih, const at::Scalar & scale_hh, const at::Scalar & zero_point_ih, const at::Scalar & zero_point_hh);
TORCH_API at::Tensor quantized_gru_cell(const at::Tensor & input, const at::Tensor & hx, const at::Tensor & w_ih, const at::Tensor & w_hh, const at::Tensor & b_ih, const at::Tensor & b_hh, const at::Tensor & packed_ih, const at::Tensor & packed_hh, const at::Tensor & col_offsets_ih, const at::Tensor & col_offsets_hh, const at::Scalar & scale_ih, const at::Scalar & scale_hh, const at::Scalar & zero_point_ih, const at::Scalar & zero_point_hh);
TORCH_API at::Tensor quantized_rnn_relu_cell(const at::Tensor & input, const at::Tensor & hx, const at::Tensor & w_ih, const at::Tensor & w_hh, const at::Tensor & b_ih, const at::Tensor & b_hh, const at::Tensor & packed_ih, const at::Tensor & packed_hh, const at::Tensor & col_offsets_ih, const at::Tensor & col_offsets_hh, const at::Scalar & scale_ih, const at::Scalar & scale_hh, const at::Scalar & zero_point_ih, const at::Scalar & zero_point_hh);
TORCH_API at::Tensor quantized_rnn_tanh_cell(const at::Tensor & input, const at::Tensor & hx, const at::Tensor & w_ih, const at::Tensor & w_hh, const at::Tensor & b_ih, const at::Tensor & b_hh, const at::Tensor & packed_ih, const at::Tensor & packed_hh, const at::Tensor & col_offsets_ih, const at::Tensor & col_offsets_hh, const at::Scalar & scale_ih, const at::Scalar & scale_hh, const at::Scalar & zero_point_ih, const at::Scalar & zero_point_hh);
TORCH_API ::std::tuple<at::Tensor,at::Tensor> _pack_padded_sequence(const at::Tensor & input, const at::Tensor & lengths, bool batch_first);
TORCH_API at::Tensor _pack_padded_sequence_backward(const at::Tensor & grad, at::IntArrayRef input_size, const at::Tensor & batch_sizes, bool batch_first);
TORCH_API ::std::tuple<at::Tensor,at::Tensor> _pad_packed_sequence(const at::Tensor & data, const at::Tensor & batch_sizes, bool batch_first, const at::Scalar & padding_value, int64_t total_length);
TORCH_API at::Tensor & set_(at::Tensor & self, at::Storage source);
TORCH_API at::Tensor & set_storage_cpu_(at::Tensor & self, at::Storage source, int64_t storage_offset, at::IntArrayRef size, at::IntArrayRef stride={});
TORCH_API at::Tensor & set_storage_cuda_(at::Tensor & self, at::Storage source, int64_t storage_offset, at::IntArrayRef size, at::IntArrayRef stride={});
TORCH_API at::Tensor & set_storage_quantized_(at::Tensor & self, at::Storage source, int64_t storage_offset, at::IntArrayRef size, at::IntArrayRef stride={});
TORCH_API at::Tensor & set_tensor_(at::Tensor & self, const at::Tensor & source);
TORCH_API at::Tensor & set_cpu_(at::Tensor & self);
TORCH_API at::Tensor & set_cuda_(at::Tensor & self);
TORCH_API bool is_set_to(const at::Tensor & self, const at::Tensor & tensor);
TORCH_API at::Tensor & masked_fill__cpu(at::Tensor & self, const at::Tensor & mask, const at::Scalar & value);
TORCH_API at::Tensor & masked_fill__cuda(at::Tensor & self, const at::Tensor & mask, const at::Scalar & value);
TORCH_API at::Tensor masked_fill(const at::Tensor & self, const at::Tensor & mask, const at::Scalar & value);
TORCH_API at::Tensor & masked_fill__cpu(at::Tensor & self, const at::Tensor & mask, const at::Tensor & value);
TORCH_API at::Tensor & masked_fill__cuda(at::Tensor & self, const at::Tensor & mask, const at::Tensor & value);
TORCH_API at::Tensor masked_fill(const at::Tensor & self, const at::Tensor & mask, const at::Tensor & value);
TORCH_API at::Tensor & masked_scatter__cpu(at::Tensor & self, const at::Tensor & mask, const at::Tensor & source);
TORCH_API at::Tensor & masked_scatter__cuda(at::Tensor & self, const at::Tensor & mask, const at::Tensor & source);
TORCH_API at::Tensor masked_scatter(const at::Tensor & self, const at::Tensor & mask, const at::Tensor & source);
TORCH_API at::Tensor view(const at::Tensor & self, at::IntArrayRef size);
TORCH_API at::Tensor mkldnn_view(const at::Tensor & self, at::IntArrayRef size);
TORCH_API at::Tensor view_dtype(const at::Tensor & self, at::ScalarType dtype);
TORCH_API at::Tensor & put_(at::Tensor & self, const at::Tensor & index, const at::Tensor & source, bool accumulate=false);
TORCH_API at::Tensor put(const at::Tensor & self, const at::Tensor & index, const at::Tensor & source, bool accumulate=false);
TORCH_API at::Tensor & index_add_(at::Tensor & self, int64_t dim, const at::Tensor & index, const at::Tensor & source);
TORCH_API at::Tensor index_add(const at::Tensor & self, int64_t dim, const at::Tensor & index, const at::Tensor & source);
TORCH_API at::Tensor & index_add_cpu_(at::Tensor & self, int64_t dim, const at::Tensor & index, const at::Tensor & source, const at::Scalar & alpha);
TORCH_API at::Tensor & index_add_cuda_(at::Tensor & self, int64_t dim, const at::Tensor & index, const at::Tensor & source, const at::Scalar & alpha);
TORCH_API at::Tensor index_add(const at::Tensor & self, int64_t dim, const at::Tensor & index, const at::Tensor & source, const at::Scalar & alpha);
TORCH_API at::Tensor index_add(const at::Tensor & self, at::Dimname dim, const at::Tensor & index, const at::Tensor & source, const at::Scalar & alpha=1);
TORCH_API at::Tensor & index_fill_(at::Tensor & self, int64_t dim, const at::Tensor & index, const at::Scalar & value);
TORCH_API at::Tensor index_fill(const at::Tensor & self, int64_t dim, const at::Tensor & index, const at::Scalar & value);
TORCH_API at::Tensor & index_fill_(at::Tensor & self, int64_t dim, const at::Tensor & index, const at::Tensor & value);
TORCH_API at::Tensor index_fill(const at::Tensor & self, int64_t dim, const at::Tensor & index, const at::Tensor & value);
TORCH_API at::Tensor & index_fill_(at::Tensor & self, at::Dimname dim, const at::Tensor & index, const at::Scalar & value);
TORCH_API at::Tensor index_fill(const at::Tensor & self, at::Dimname dim, const at::Tensor & index, const at::Scalar & value);
TORCH_API at::Tensor & index_fill_(at::Tensor & self, at::Dimname dim, const at::Tensor & index, const at::Tensor & value);
TORCH_API at::Tensor index_fill(const at::Tensor & self, at::Dimname dim, const at::Tensor & index, const at::Tensor & value);
struct TORCH_API structured_scatter_src_out : public at::meta::structured_scatter_src {
void impl(const at::Tensor & self, int64_t dim, const at::Tensor & index, const at::Tensor & src, const at::Tensor & out);
};
struct TORCH_API structured_scatter_value_out : public at::meta::structured_scatter_value {
void impl(const at::Tensor & self, int64_t dim, const at::Tensor & index, const at::Scalar & value, const at::Tensor & out);
};
struct TORCH_API structured_scatter_reduce_out : public at::meta::structured_scatter_reduce {
void impl(const at::Tensor & self, int64_t dim, const at::Tensor & index, const at::Tensor & src, c10::string_view reduce, const at::Tensor & out);
};
struct TORCH_API structured_scatter_value_reduce_out : public at::meta::structured_scatter_value_reduce {
void impl(const at::Tensor & self, int64_t dim, const at::Tensor & index, const at::Scalar & value, c10::string_view reduce, const at::Tensor & out);
};
TORCH_API at::Tensor scatter(const at::Tensor & self, at::Dimname dim, const at::Tensor & index, const at::Tensor & src);
TORCH_API at::Tensor scatter(const at::Tensor & self, at::Dimname dim, const at::Tensor & index, const at::Scalar & value);
struct TORCH_API structured_scatter_add : public at::meta::structured_scatter_add {
void impl(const at::Tensor & self, int64_t dim, const at::Tensor & index, const at::Tensor & src, const at::Tensor & out);
};
TORCH_API at::Tensor scatter_add(const at::Tensor & self, at::Dimname dim, const at::Tensor & index, const at::Tensor & src);
TORCH_API at::Tensor & eq_(at::Tensor & self, const at::Scalar & other);
struct TORCH_API structured_eq_Scalar_out : public at::meta::structured_eq_Scalar {
void impl(const at::Tensor & self, const at::Scalar & other, const at::Tensor & out);
};
TORCH_API at::Tensor eq_quantized_cpu(const at::Tensor & self, const at::Scalar & other);
TORCH_API at::Tensor & eq_out_quantized_cpu(const at::Tensor & self, const at::Scalar & other, at::Tensor & out);
TORCH_API at::Tensor & eq_(at::Tensor & self, const at::Tensor & other);
struct TORCH_API structured_eq_Tensor_out : public at::meta::structured_eq_Tensor {
void impl(const at::Tensor & self, const at::Tensor & other, const at::Tensor & out);
};
TORCH_API at::Tensor eq_quantized_cpu(const at::Tensor & self, const at::Tensor & other);
TORCH_API at::Tensor & eq_out_quantized_cpu(const at::Tensor & self, const at::Tensor & other, at::Tensor & out);
struct TORCH_API structured_bitwise_and_out : public at::meta::structured_bitwise_and_Tensor {
void impl(const at::Tensor & self, const at::Tensor & other, const at::Tensor & out);
};
TORCH_API at::Tensor & bitwise_and_(at::Tensor & self, const at::Scalar & other);
TORCH_API at::Tensor bitwise_and(const at::Tensor & self, const at::Scalar & other);
TORCH_API at::Tensor & bitwise_and_out(const at::Tensor & self, const at::Scalar & other, at::Tensor & out);
TORCH_API at::Tensor __and__(const at::Tensor & self, const at::Scalar & other);
TORCH_API at::Tensor & __iand__(at::Tensor & self, const at::Scalar & other);
TORCH_API at::Tensor __and__(const at::Tensor & self, const at::Tensor & other);
TORCH_API at::Tensor & __iand__(at::Tensor & self, const at::Tensor & other);
struct TORCH_API structured_bitwise_or_out : public at::meta::structured_bitwise_or_Tensor {
void impl(const at::Tensor & self, const at::Tensor & other, const at::Tensor & out);
};
TORCH_API at::Tensor bitwise_or(const at::Tensor & self, const at::Scalar & other);
TORCH_API at::Tensor & bitwise_or_(at::Tensor & self, const at::Scalar & other);
TORCH_API at::Tensor & bitwise_or_out(const at::Tensor & self, const at::Scalar & other, at::Tensor & out);
TORCH_API at::Tensor __or__(const at::Tensor & self, const at::Scalar & other);
TORCH_API at::Tensor & __ior__(at::Tensor & self, const at::Scalar & other);
TORCH_API at::Tensor __or__(const at::Tensor & self, const at::Tensor & other);
TORCH_API at::Tensor & __ior__(at::Tensor & self, const at::Tensor & other);
struct TORCH_API structured_bitwise_xor_out : public at::meta::structured_bitwise_xor_Tensor {
void impl(const at::Tensor & self, const at::Tensor & other, const at::Tensor & out);
};
TORCH_API at::Tensor bitwise_xor(const at::Tensor & self, const at::Scalar & other);
TORCH_API at::Tensor & bitwise_xor_(at::Tensor & self, const at::Scalar & other);
TORCH_API at::Tensor & bitwise_xor_out(const at::Tensor & self, const at::Scalar & other, at::Tensor & out);
TORCH_API at::Tensor __xor__(const at::Tensor & self, const at::Scalar & other);
TORCH_API at::Tensor & __ixor__(at::Tensor & self, const at::Scalar & other);
TORCH_API at::Tensor __xor__(const at::Tensor & self, const at::Tensor & other);
TORCH_API at::Tensor & __ixor__(at::Tensor & self, const at::Tensor & other);
TORCH_API at::Tensor __lshift__(const at::Tensor & self, const at::Scalar & other);
TORCH_API at::Tensor & __ilshift__(at::Tensor & self, const at::Scalar & other);
TORCH_API at::Tensor __lshift__(const at::Tensor & self, const at::Tensor & other);
TORCH_API at::Tensor & __ilshift__(at::Tensor & self, const at::Tensor & other);
struct TORCH_API structured_bitwise_left_shift_out : public at::meta::structured_bitwise_left_shift_Tensor {
void impl(const at::Tensor & self, const at::Tensor & other, const at::Tensor & out);
};
TORCH_API at::Tensor bitwise_left_shift(const at::Tensor & self, const at::Scalar & other);
TORCH_API at::Tensor & bitwise_left_shift_out(const at::Tensor & self, const at::Scalar & other, at::Tensor & out);
TORCH_API at::Tensor & bitwise_left_shift_(at::Tensor & self, const at::Scalar & other);
TORCH_API at::Tensor bitwise_left_shift(const at::Scalar & self, const at::Tensor & other);
TORCH_API at::Tensor __rshift__(const at::Tensor & self, const at::Scalar & other);
TORCH_API at::Tensor & __irshift__(at::Tensor & self, const at::Scalar & other);
TORCH_API at::Tensor __rshift__(const at::Tensor & self, const at::Tensor & other);
TORCH_API at::Tensor & __irshift__(at::Tensor & self, const at::Tensor & other);
struct TORCH_API structured_bitwise_right_shift_out : public at::meta::structured_bitwise_right_shift_Tensor {
void impl(const at::Tensor & self, const at::Tensor & other, const at::Tensor & out);
};
TORCH_API at::Tensor bitwise_right_shift(const at::Tensor & self, const at::Scalar & other);
TORCH_API at::Tensor & bitwise_right_shift_out(const at::Tensor & self, const at::Scalar & other, at::Tensor & out);
TORCH_API at::Tensor & bitwise_right_shift_(at::Tensor & self, const at::Scalar & other);
TORCH_API at::Tensor bitwise_right_shift(const at::Scalar & self, const at::Tensor & other);
TORCH_API at::Tensor tril(const at::Tensor & self, int64_t diagonal=0);
TORCH_API at::Tensor & tril_cpu_out(const at::Tensor & self, int64_t diagonal, at::Tensor & out);
TORCH_API at::Tensor & tril_cpu_(at::Tensor & self, int64_t diagonal=0);
TORCH_API at::Tensor & tril_cuda_out(const at::Tensor & self, int64_t diagonal, at::Tensor & out);
TORCH_API at::Tensor & tril_cuda_(at::Tensor & self, int64_t diagonal=0);
TORCH_API at::Tensor triu(const at::Tensor & self, int64_t diagonal=0);
TORCH_API at::Tensor & triu_cpu_out(const at::Tensor & self, int64_t diagonal, at::Tensor & out);
TORCH_API at::Tensor & triu_cpu_(at::Tensor & self, int64_t diagonal=0);
TORCH_API at::Tensor & triu_cuda_out(const at::Tensor & self, int64_t diagonal, at::Tensor & out);
TORCH_API at::Tensor & triu_cuda_(at::Tensor & self, int64_t diagonal=0);
struct TORCH_API structured_digamma_out : public at::meta::structured_digamma {
void impl(const at::Tensor & self, const at::Tensor & out);
};
TORCH_API at::Tensor lerp_cpu_scalar(const at::Tensor & self, const at::Tensor & end, const at::Scalar & weight);
TORCH_API at::Tensor & lerp_cpu_scalar_out(const at::Tensor & self, const at::Tensor & end, const at::Scalar & weight, at::Tensor & out);
TORCH_API at::Tensor & lerp_cpu_scalar_(at::Tensor & self, const at::Tensor & end, const at::Scalar & weight);
TORCH_API at::Tensor lerp_cuda_scalar(const at::Tensor & self, const at::Tensor & end, const at::Scalar & weight);
TORCH_API at::Tensor & lerp_cuda_scalar_out(const at::Tensor & self, const at::Tensor & end, const at::Scalar & weight, at::Tensor & out);
TORCH_API at::Tensor & lerp_cuda_scalar_(at::Tensor & self, const at::Tensor & end, const at::Scalar & weight);
TORCH_API at::Tensor lerp_cpu_tensor(const at::Tensor & self, const at::Tensor & end, const at::Tensor & weight);
TORCH_API at::Tensor & lerp_cpu_tensor_out(const at::Tensor & self, const at::Tensor & end, const at::Tensor & weight, at::Tensor & out);
TORCH_API at::Tensor & lerp_cpu_tensor_(at::Tensor & self, const at::Tensor & end, const at::Tensor & weight);
TORCH_API at::Tensor lerp_cuda_tensor(const at::Tensor & self, const at::Tensor & end, const at::Tensor & weight);
TORCH_API at::Tensor & lerp_cuda_tensor_out(const at::Tensor & self, const at::Tensor & end, const at::Tensor & weight, at::Tensor & out);
TORCH_API at::Tensor & lerp_cuda_tensor_(at::Tensor & self, const at::Tensor & end, const at::Tensor & weight);
TORCH_API at::Tensor addbmm(const at::Tensor & self, const at::Tensor & batch1, const at::Tensor & batch2, const at::Scalar & beta=1, const at::Scalar & alpha=1);
TORCH_API at::Tensor & addbmm_out(const at::Tensor & self, const at::Tensor & batch1, const at::Tensor & batch2, const at::Scalar & beta, const at::Scalar & alpha, at::Tensor & out);
TORCH_API at::Tensor & addbmm_(at::Tensor & self, const at::Tensor & batch1, const at::Tensor & batch2, const at::Scalar & beta=1, const at::Scalar & alpha=1);
TORCH_API at::Tensor & random_(at::Tensor & self, int64_t from, c10::optional<int64_t> to, c10::optional<at::Generator> generator=c10::nullopt);
TORCH_API at::Tensor & random_meta_(at::Tensor & self, int64_t from, c10::optional<int64_t> to, c10::optional<at::Generator> generator=c10::nullopt);
TORCH_API at::Tensor & random_(at::Tensor & self, int64_t to, c10::optional<at::Generator> generator=c10::nullopt);
TORCH_API at::Tensor & random_meta_(at::Tensor & self, int64_t to, c10::optional<at::Generator> generator=c10::nullopt);
TORCH_API at::Tensor & random_(at::Tensor & self, c10::optional<at::Generator> generator=c10::nullopt);
TORCH_API at::Tensor & random_meta_(at::Tensor & self, c10::optional<at::Generator> generator=c10::nullopt);
TORCH_API at::Tensor & uniform_(at::Tensor & self, double from=0, double to=1, c10::optional<at::Generator> generator=c10::nullopt);
TORCH_API at::Tensor & uniform_meta_(at::Tensor & self, double from=0, double to=1, c10::optional<at::Generator> generator=c10::nullopt);
TORCH_API at::Tensor & cauchy_(at::Tensor & self, double median=0, double sigma=1, c10::optional<at::Generator> generator=c10::nullopt);
TORCH_API at::Tensor & log_normal_(at::Tensor & self, double mean=1, double std=2, c10::optional<at::Generator> generator=c10::nullopt);
TORCH_API at::Tensor & exponential_(at::Tensor & self, double lambd=1, c10::optional<at::Generator> generator=c10::nullopt);
TORCH_API at::Tensor & geometric_(at::Tensor & self, double p, c10::optional<at::Generator> generator=c10::nullopt);
TORCH_API at::Tensor diag(const at::Tensor & self, int64_t diagonal=0);
TORCH_API at::Tensor & diag_cpu_out(const at::Tensor & self, int64_t diagonal, at::Tensor & out);
TORCH_API at::Tensor & diag_cuda_out(const at::Tensor & self, int64_t diagonal, at::Tensor & out);
TORCH_API at::Tensor diag_backward(const at::Tensor & grad, at::IntArrayRef input_sizes, int64_t diagonal);
TORCH_API at::Tensor cross(const at::Tensor & self, const at::Tensor & other, c10::optional<int64_t> dim=c10::nullopt);
TORCH_API at::Tensor & cross_out(const at::Tensor & self, const at::Tensor & other, c10::optional<int64_t> dim, at::Tensor & out);
TORCH_API at::Tensor tril_indices_cpu(int64_t row, int64_t col, int64_t offset=0, c10::optional<at::ScalarType> dtype={}, c10::optional<at::Layout> layout={}, c10::optional<at::Device> device={}, c10::optional<bool> pin_memory={});
TORCH_API at::Tensor tril_indices_cuda(int64_t row, int64_t col, int64_t offset=0, c10::optional<at::ScalarType> dtype={}, c10::optional<at::Layout> layout={}, c10::optional<at::Device> device={}, c10::optional<bool> pin_memory={});
TORCH_API at::Tensor triu_indices_cpu(int64_t row, int64_t col, int64_t offset=0, c10::optional<at::ScalarType> dtype={}, c10::optional<at::Layout> layout={}, c10::optional<at::Device> device={}, c10::optional<bool> pin_memory={});
TORCH_API at::Tensor triu_indices_cuda(int64_t row, int64_t col, int64_t offset=0, c10::optional<at::ScalarType> dtype={}, c10::optional<at::Layout> layout={}, c10::optional<at::Device> device={}, c10::optional<bool> pin_memory={});
TORCH_API at::Tensor trace_cpu(const at::Tensor & self);
TORCH_API at::Tensor trace_cuda(const at::Tensor & self);
TORCH_API at::Tensor trace_backward(const at::Tensor & grad, at::IntArrayRef sizes);
TORCH_API at::Tensor & ne_(at::Tensor & self, const at::Scalar & other);
struct TORCH_API structured_ne_Scalar_out : public at::meta::structured_ne_Scalar {
void impl(const at::Tensor & self, const at::Scalar & other, const at::Tensor & out);
};
TORCH_API at::Tensor ne_quantized_cpu(const at::Tensor & self, const at::Scalar & other);
TORCH_API at::Tensor & ne_out_quantized_cpu(const at::Tensor & self, const at::Scalar & other, at::Tensor & out);
TORCH_API at::Tensor & ne_(at::Tensor & self, const at::Tensor & other);
struct TORCH_API structured_ne_Tensor_out : public at::meta::structured_ne_Tensor {
void impl(const at::Tensor & self, const at::Tensor & other, const at::Tensor & out);
};
TORCH_API at::Tensor ne_quantized_cpu(const at::Tensor & self, const at::Tensor & other);
TORCH_API at::Tensor & ne_out_quantized_cpu(const at::Tensor & self, const at::Tensor & other, at::Tensor & out);
TORCH_API at::Tensor not_equal(const at::Tensor & self, const at::Scalar & other);
TORCH_API at::Tensor & not_equal_out(const at::Tensor & self, const at::Scalar & other, at::Tensor & out);
TORCH_API at::Tensor & not_equal_(at::Tensor & self, const at::Scalar & other);
TORCH_API at::Tensor not_equal(const at::Tensor & self, const at::Tensor & other);
TORCH_API at::Tensor & not_equal_out(const at::Tensor & self, const at::Tensor & other, at::Tensor & out);
TORCH_API at::Tensor & not_equal_(at::Tensor & self, const at::Tensor & other);
TORCH_API at::Tensor & ge_(at::Tensor & self, const at::Scalar & other);
struct TORCH_API structured_ge_Scalar_out : public at::meta::structured_ge_Scalar {
void impl(const at::Tensor & self, const at::Scalar & other, const at::Tensor & out);
};
TORCH_API at::Tensor ge_quantized_cpu(const at::Tensor & self, const at::Scalar & other);
TORCH_API at::Tensor & ge_out_quantized_cpu(const at::Tensor & self, const at::Scalar & other, at::Tensor & out);
TORCH_API at::Tensor & ge_(at::Tensor & self, const at::Tensor & other);
struct TORCH_API structured_ge_Tensor_out : public at::meta::structured_ge_Tensor {
void impl(const at::Tensor & self, const at::Tensor & other, const at::Tensor & out);
};
TORCH_API at::Tensor ge_quantized_cpu(const at::Tensor & self, const at::Tensor & other);
TORCH_API at::Tensor & ge_out_quantized_cpu(const at::Tensor & self, const at::Tensor & other, at::Tensor & out);
TORCH_API at::Tensor greater_equal(const at::Tensor & self, const at::Scalar & other);
TORCH_API at::Tensor & greater_equal_out(const at::Tensor & self, const at::Scalar & other, at::Tensor & out);
TORCH_API at::Tensor & greater_equal_(at::Tensor & self, const at::Scalar & other);
TORCH_API at::Tensor greater_equal(const at::Tensor & self, const at::Tensor & other);
TORCH_API at::Tensor & greater_equal_out(const at::Tensor & self, const at::Tensor & other, at::Tensor & out);
TORCH_API at::Tensor & greater_equal_(at::Tensor & self, const at::Tensor & other);
TORCH_API at::Tensor & le_(at::Tensor & self, const at::Scalar & other);
struct TORCH_API structured_le_Scalar_out : public at::meta::structured_le_Scalar {
void impl(const at::Tensor & self, const at::Scalar & other, const at::Tensor & out);
};
TORCH_API at::Tensor le_quantized_cpu(const at::Tensor & self, const at::Scalar & other);
TORCH_API at::Tensor & le_out_quantized_cpu(const at::Tensor & self, const at::Scalar & other, at::Tensor & out);
TORCH_API at::Tensor & le_(at::Tensor & self, const at::Tensor & other);
struct TORCH_API structured_le_Tensor_out : public at::meta::structured_le_Tensor {
void impl(const at::Tensor & self, const at::Tensor & other, const at::Tensor & out);
};
TORCH_API at::Tensor le_quantized_cpu(const at::Tensor & self, const at::Tensor & other);
TORCH_API at::Tensor & le_out_quantized_cpu(const at::Tensor & self, const at::Tensor & other, at::Tensor & out);
TORCH_API at::Tensor less_equal(const at::Tensor & self, const at::Scalar & other);
TORCH_API at::Tensor & less_equal_out(const at::Tensor & self, const at::Scalar & other, at::Tensor & out);
TORCH_API at::Tensor & less_equal_(at::Tensor & self, const at::Scalar & other);
TORCH_API at::Tensor less_equal(const at::Tensor & self, const at::Tensor & other);
TORCH_API at::Tensor & less_equal_out(const at::Tensor & self, const at::Tensor & other, at::Tensor & out);
TORCH_API at::Tensor & less_equal_(at::Tensor & self, const at::Tensor & other);
TORCH_API at::Tensor & gt_(at::Tensor & self, const at::Scalar & other);
struct TORCH_API structured_gt_Scalar_out : public at::meta::structured_gt_Scalar {
void impl(const at::Tensor & self, const at::Scalar & other, const at::Tensor & out);
};
TORCH_API at::Tensor gt_quantized_cpu(const at::Tensor & self, const at::Scalar & other);
TORCH_API at::Tensor & gt_out_quantized_cpu(const at::Tensor & self, const at::Scalar & other, at::Tensor & out);
TORCH_API at::Tensor & gt_(at::Tensor & self, const at::Tensor & other);
struct TORCH_API structured_gt_Tensor_out : public at::meta::structured_gt_Tensor {
void impl(const at::Tensor & self, const at::Tensor & other, const at::Tensor & out);
};
TORCH_API at::Tensor gt_quantized_cpu(const at::Tensor & self, const at::Tensor & other);
TORCH_API at::Tensor & gt_out_quantized_cpu(const at::Tensor & self, const at::Tensor & other, at::Tensor & out);
TORCH_API at::Tensor greater(const at::Tensor & self, const at::Scalar & other);
TORCH_API at::Tensor & greater_out(const at::Tensor & self, const at::Scalar & other, at::Tensor & out);
TORCH_API at::Tensor & greater_(at::Tensor & self, const at::Scalar & other);
TORCH_API at::Tensor greater(const at::Tensor & self, const at::Tensor & other);
TORCH_API at::Tensor & greater_out(const at::Tensor & self, const at::Tensor & other, at::Tensor & out);
TORCH_API at::Tensor & greater_(at::Tensor & self, const at::Tensor & other);
TORCH_API at::Tensor & lt_(at::Tensor & self, const at::Scalar & other);
struct TORCH_API structured_lt_Scalar_out : public at::meta::structured_lt_Scalar {
void impl(const at::Tensor & self, const at::Scalar & other, const at::Tensor & out);
};
TORCH_API at::Tensor lt_quantized_cpu(const at::Tensor & self, const at::Scalar & other);
TORCH_API at::Tensor & lt_out_quantized_cpu(const at::Tensor & self, const at::Scalar & other, at::Tensor & out);
TORCH_API at::Tensor & lt_(at::Tensor & self, const at::Tensor & other);
struct TORCH_API structured_lt_Tensor_out : public at::meta::structured_lt_Tensor {
void impl(const at::Tensor & self, const at::Tensor & other, const at::Tensor & out);
};
TORCH_API at::Tensor lt_quantized_cpu(const at::Tensor & self, const at::Tensor & other);
TORCH_API at::Tensor & lt_out_quantized_cpu(const at::Tensor & self, const at::Tensor & other, at::Tensor & out);
TORCH_API at::Tensor less(const at::Tensor & self, const at::Scalar & other);
TORCH_API at::Tensor & less_out(const at::Tensor & self, const at::Scalar & other, at::Tensor & out);
TORCH_API at::Tensor & less_(at::Tensor & self, const at::Scalar & other);
TORCH_API at::Tensor less(const at::Tensor & self, const at::Tensor & other);
TORCH_API at::Tensor & less_out(const at::Tensor & self, const at::Tensor & other, at::Tensor & out);
TORCH_API at::Tensor & less_(at::Tensor & self, const at::Tensor & other);
TORCH_API at::Tensor take(const at::Tensor & self, const at::Tensor & index);
TORCH_API at::Tensor & take_out(const at::Tensor & self, const at::Tensor & index, at::Tensor & out);
TORCH_API at::Tensor take_along_dim(const at::Tensor & self, const at::Tensor & indices, c10::optional<int64_t> dim=c10::nullopt);
TORCH_API at::Tensor & take_along_dim_out(const at::Tensor & self, const at::Tensor & indices, c10::optional<int64_t> dim, at::Tensor & out);
TORCH_API at::Tensor index_select_cpu_(const at::Tensor & self, int64_t dim, const at::Tensor & index);
TORCH_API at::Tensor & index_select_out_cpu_(const at::Tensor & self, int64_t dim, const at::Tensor & index, at::Tensor & out);
TORCH_API at::Tensor index_select_cuda(const at::Tensor & self, int64_t dim, const at::Tensor & index);
TORCH_API at::Tensor & index_select_out_cuda(const at::Tensor & self, int64_t dim, const at::Tensor & index, at::Tensor & out);
TORCH_API at::Tensor index_select_sparse(const at::Tensor & self, int64_t dim, const at::Tensor & index);
TORCH_API at::Tensor index_select(const at::Tensor & self, at::Dimname dim, const at::Tensor & index);
TORCH_API at::Tensor & index_select_out(const at::Tensor & self, at::Dimname dim, const at::Tensor & index, at::Tensor & out);
TORCH_API at::Tensor index_select_backward(const at::Tensor & grad, at::IntArrayRef self_sizes, int64_t dim, const at::Tensor & index);
TORCH_API at::Tensor masked_select_cpu(const at::Tensor & self, const at::Tensor & mask);
TORCH_API at::Tensor & masked_select_out_cpu(const at::Tensor & self, const at::Tensor & mask, at::Tensor & out);
TORCH_API at::Tensor masked_select_cuda(const at::Tensor & self, const at::Tensor & mask);
TORCH_API at::Tensor & masked_select_out_cuda(const at::Tensor & self, const at::Tensor & mask, at::Tensor & out);
TORCH_API at::Tensor masked_select_backward(const at::Tensor & grad, const at::Tensor & input, const at::Tensor & mask);
TORCH_API at::Tensor nonzero_cpu(const at::Tensor & self);
TORCH_API at::Tensor & nonzero_out_cpu(const at::Tensor & self, at::Tensor & out);
TORCH_API at::Tensor nonzero_cuda(const at::Tensor & self);
TORCH_API at::Tensor & nonzero_out_cuda(const at::Tensor & self, at::Tensor & out);
TORCH_API ::std::vector<at::Tensor> nonzero_numpy(const at::Tensor & self);
struct TORCH_API structured_gather_out : public at::meta::structured_gather {
void impl(const at::Tensor & self, int64_t dim, const at::Tensor & index, bool sparse_grad, const at::Tensor & out);
};
TORCH_API at::Tensor gather_backward(const at::Tensor & grad, const at::Tensor & self, int64_t dim, const at::Tensor & index, bool sparse_grad);
TORCH_API at::Tensor gather(const at::Tensor & self, at::Dimname dim, const at::Tensor & index, bool sparse_grad=false);
TORCH_API at::Tensor & gather_out(const at::Tensor & self, at::Dimname dim, const at::Tensor & index, bool sparse_grad, at::Tensor & out);
TORCH_API at::Tensor _gather_sparse_backward(const at::Tensor & self, int64_t dim, const at::Tensor & index, const at::Tensor & grad);
struct TORCH_API structured_addcmul_out : public at::meta::structured_addcmul {
void impl(const at::Tensor & self, const at::Tensor & tensor1, const at::Tensor & tensor2, const at::Scalar & value, const at::Tensor & out);
};
struct TORCH_API structured_addcdiv_out : public at::meta::structured_addcdiv {
void impl(const at::Tensor & self, const at::Tensor & tensor1, const at::Tensor & tensor2, const at::Scalar & value, const at::Tensor & out);
};
TORCH_API at::Tensor cross_entropy_loss(const at::Tensor & self, const at::Tensor & target, const c10::optional<at::Tensor> & weight={}, int64_t reduction=at::Reduction::Mean, int64_t ignore_index=-100, double label_smoothing=0.0);
TORCH_API ::std::tuple<at::Tensor,at::Tensor> legacy_lstsq(const at::Tensor & self, const at::Tensor & A);
TORCH_API ::std::tuple<at::Tensor &,at::Tensor &> legacy_lstsq_out(const at::Tensor & self, const at::Tensor & A, at::Tensor & X, at::Tensor & qr);
TORCH_API ::std::tuple<at::Tensor,at::Tensor> legacy_lstsq_cuda(const at::Tensor & self, const at::Tensor & A);
TORCH_API ::std::tuple<at::Tensor &,at::Tensor &> legacy_lstsq_out_cuda(const at::Tensor & self, const at::Tensor & A, at::Tensor & X, at::Tensor & qr);
TORCH_API ::std::tuple<at::Tensor,at::Tensor> triangular_solve(const at::Tensor & self, const at::Tensor & A, bool upper=true, bool transpose=false, bool unitriangular=false);
TORCH_API ::std::tuple<at::Tensor &,at::Tensor &> triangular_solve_out(const at::Tensor & self, const at::Tensor & A, bool upper, bool transpose, bool unitriangular, at::Tensor & X, at::Tensor & M);
TORCH_API ::std::tuple<at::Tensor,at::Tensor> symeig(const at::Tensor & self, bool eigenvectors=false, bool upper=true);
TORCH_API ::std::tuple<at::Tensor &,at::Tensor &> symeig_out(const at::Tensor & self, bool eigenvectors, bool upper, at::Tensor & e, at::Tensor & V);
TORCH_API ::std::tuple<at::Tensor,at::Tensor> _symeig_helper_cpu(const at::Tensor & self, bool eigenvectors, bool upper);
TORCH_API ::std::tuple<at::Tensor,at::Tensor> _symeig_helper_cuda(const at::Tensor & self, bool eigenvectors, bool upper);
TORCH_API ::std::tuple<at::Tensor,at::Tensor> eig(const at::Tensor & self, bool eigenvectors=false);
TORCH_API ::std::tuple<at::Tensor &,at::Tensor &> eig_out(const at::Tensor & self, bool eigenvectors, at::Tensor & e, at::Tensor & v);
TORCH_API ::std::tuple<at::Tensor,at::Tensor,at::Tensor> svd(const at::Tensor & self, bool some=true, bool compute_uv=true);
TORCH_API ::std::tuple<at::Tensor &,at::Tensor &,at::Tensor &> svd_out(const at::Tensor & self, bool some, bool compute_uv, at::Tensor & U, at::Tensor & S, at::Tensor & V);
TORCH_API ::std::tuple<at::Tensor,at::Tensor,at::Tensor> _svd_helper_cpu(const at::Tensor & self, bool some, bool compute_uv);
TORCH_API ::std::tuple<at::Tensor,at::Tensor,at::Tensor> _svd_helper_cuda(const at::Tensor & self, bool some, bool compute_uv);
TORCH_API at::Tensor swapaxes(const at::Tensor & self, int64_t axis0, int64_t axis1);
TORCH_API at::Tensor & swapaxes_(at::Tensor & self, int64_t axis0, int64_t axis1);
TORCH_API at::Tensor swapdims(const at::Tensor & self, int64_t dim0, int64_t dim1);
TORCH_API at::Tensor & swapdims_(at::Tensor & self, int64_t dim0, int64_t dim1);
TORCH_API at::Tensor cholesky(const at::Tensor & self, bool upper=false);
TORCH_API at::Tensor & cholesky_out(const at::Tensor & self, bool upper, at::Tensor & out);
TORCH_API at::Tensor cholesky_solve(const at::Tensor & self, const at::Tensor & input2, bool upper=false);
TORCH_API at::Tensor & cholesky_solve_out(const at::Tensor & self, const at::Tensor & input2, bool upper, at::Tensor & out);
TORCH_API at::Tensor _cholesky_solve_helper_cpu(const at::Tensor & self, const at::Tensor & A, bool upper);
TORCH_API at::Tensor _cholesky_solve_helper_cuda(const at::Tensor & self, const at::Tensor & A, bool upper);
TORCH_API ::std::tuple<at::Tensor,at::Tensor> solve(const at::Tensor & self, const at::Tensor & A);
TORCH_API ::std::tuple<at::Tensor &,at::Tensor &> solve_out(const at::Tensor & self, const at::Tensor & A, at::Tensor & solution, at::Tensor & lu);
TORCH_API ::std::tuple<at::Tensor,at::Tensor> _solve_helper_cpu(const at::Tensor & self, const at::Tensor & A);
TORCH_API ::std::tuple<at::Tensor,at::Tensor> _solve_helper_cuda(const at::Tensor & self, const at::Tensor & A);
TORCH_API at::Tensor cholesky_inverse(const at::Tensor & self, bool upper=false);
TORCH_API at::Tensor & cholesky_inverse_out(const at::Tensor & self, bool upper, at::Tensor & out);
TORCH_API ::std::tuple<at::Tensor,at::Tensor> qr(const at::Tensor & self, bool some=true);
TORCH_API ::std::tuple<at::Tensor &,at::Tensor &> qr_out(const at::Tensor & self, bool some, at::Tensor & Q, at::Tensor & R);
TORCH_API ::std::tuple<at::Tensor,at::Tensor> geqrf(const at::Tensor & self);
TORCH_API ::std::tuple<at::Tensor &,at::Tensor &> geqrf_out(const at::Tensor & self, at::Tensor & a, at::Tensor & tau);
TORCH_API at::Tensor orgqr(const at::Tensor & self, const at::Tensor & input2);
TORCH_API at::Tensor & orgqr_out(const at::Tensor & self, const at::Tensor & input2, at::Tensor & out);
TORCH_API at::Tensor ormqr(const at::Tensor & self, const at::Tensor & input2, const at::Tensor & input3, bool left=true, bool transpose=false);
TORCH_API at::Tensor & ormqr_out(const at::Tensor & self, const at::Tensor & input2, const at::Tensor & input3, bool left, bool transpose, at::Tensor & out);
TORCH_API ::std::tuple<at::Tensor,at::Tensor,at::Tensor> _lu_with_info(const at::Tensor & self, bool pivot=true, bool check_errors=true);
TORCH_API at::Tensor lu_solve(const at::Tensor & self, const at::Tensor & LU_data, const at::Tensor & LU_pivots);
TORCH_API at::Tensor & lu_solve_out(const at::Tensor & self, const at::Tensor & LU_data, const at::Tensor & LU_pivots, at::Tensor & out);
TORCH_API ::std::tuple<at::Tensor,at::Tensor,at::Tensor> lu_unpack(const at::Tensor & LU_data, const at::Tensor & LU_pivots, bool unpack_data=true, bool unpack_pivots=true);
TORCH_API ::std::tuple<at::Tensor &,at::Tensor &,at::Tensor &> lu_unpack_out(const at::Tensor & LU_data, const at::Tensor & LU_pivots, bool unpack_data, bool unpack_pivots, at::Tensor & P, at::Tensor & L, at::Tensor & U);
TORCH_API at::Tensor multinomial(const at::Tensor & self, int64_t num_samples, bool replacement=false, c10::optional<at::Generator> generator=c10::nullopt);
TORCH_API at::Tensor & multinomial_out(const at::Tensor & self, int64_t num_samples, bool replacement, c10::optional<at::Generator> generator, at::Tensor & out);
struct TORCH_API structured_lgamma_out : public at::meta::structured_lgamma {
void impl(const at::Tensor & self, const at::Tensor & out);
};
struct TORCH_API structured_polygamma_out : public at::meta::structured_polygamma {
void impl(int64_t n, const at::Tensor & self, const at::Tensor & out);
};
TORCH_API at::Tensor & polygamma_(at::Tensor & self, int64_t n);
struct TORCH_API structured_erfinv_out : public at::meta::structured_erfinv {
void impl(const at::Tensor & self, const at::Tensor & out);
};
struct TORCH_API structured_i0_out : public at::meta::structured_i0 {
void impl(const at::Tensor & self, const at::Tensor & out);
};
TORCH_API at::Tensor sign(const at::Tensor & self);
TORCH_API at::Tensor & sign_(at::Tensor & self);
struct TORCH_API structured_sign_out : public at::meta::structured_sign {
void impl(const at::Tensor & self, const at::Tensor & out);
};
struct TORCH_API structured_signbit_out : public at::meta::structured_signbit {
void impl(const at::Tensor & self, const at::Tensor & out);
};
TORCH_API at::Tensor dist(const at::Tensor & self, const at::Tensor & other, const at::Scalar & p=2);
struct TORCH_API structured_atan2_out : public at::meta::structured_atan2 {
void impl(const at::Tensor & self, const at::Tensor & other, const at::Tensor & out);
};
TORCH_API at::Tensor histogram_histc_cpu(const at::Tensor & self, int64_t bins=100, const at::Scalar & min=0, const at::Scalar & max=0);
TORCH_API at::Tensor & histogram_histc_cpu_out(const at::Tensor & self, int64_t bins, const at::Scalar & min, const at::Scalar & max, at::Tensor & out);
TORCH_API at::Tensor _histc_cuda(const at::Tensor & self, int64_t bins=100, const at::Scalar & min=0, const at::Scalar & max=0);
TORCH_API at::Tensor & _histc_out_cuda(const at::Tensor & self, int64_t bins, const at::Scalar & min, const at::Scalar & max, at::Tensor & out);
TORCH_API ::std::tuple<at::Tensor,at::Tensor> histogram_cpu(const at::Tensor & self, const at::Tensor & bins, const c10::optional<at::Tensor> & weight={}, bool density=false);
TORCH_API ::std::tuple<at::Tensor &,at::Tensor &> histogram_out_cpu(const at::Tensor & self, const at::Tensor & bins, const c10::optional<at::Tensor> & weight, bool density, at::Tensor & hist, at::Tensor & bin_edges);
TORCH_API ::std::tuple<at::Tensor,at::Tensor> histogram_cpu(const at::Tensor & self, int64_t bins=100, c10::optional<at::ArrayRef<double>> range=c10::nullopt, const c10::optional<at::Tensor> & weight={}, bool density=false);
TORCH_API ::std::tuple<at::Tensor &,at::Tensor &> histogram_out_cpu(const at::Tensor & self, int64_t bins, c10::optional<at::ArrayRef<double>> range, const c10::optional<at::Tensor> & weight, bool density, at::Tensor & hist, at::Tensor & bin_edges);
TORCH_API at::Tensor fmod(const at::Tensor & self, const at::Scalar & other);
TORCH_API at::Tensor & fmod_out(const at::Tensor & self, const at::Scalar & other, at::Tensor & out);
TORCH_API at::Tensor & fmod_(at::Tensor & self, const at::Scalar & other);
struct TORCH_API structured_fmod_out : public at::meta::structured_fmod_Tensor {
void impl(const at::Tensor & self, const at::Tensor & other, const at::Tensor & out);
};
TORCH_API at::Tensor & hypot_(at::Tensor & self, const at::Tensor & other);
struct TORCH_API structured_hypot_out : public at::meta::structured_hypot {
void impl(const at::Tensor & self, const at::Tensor & other, const at::Tensor & out);
};
struct TORCH_API structured_igamma_out : public at::meta::structured_igamma {
void impl(const at::Tensor & self, const at::Tensor & other, const at::Tensor & out);
};
struct TORCH_API structured_igammac_out : public at::meta::structured_igammac {
void impl(const at::Tensor & self, const at::Tensor & other, const at::Tensor & out);
};
TORCH_API at::Tensor & nextafter_(at::Tensor & self, const at::Tensor & other);
struct TORCH_API structured_nextafter_out : public at::meta::structured_nextafter {
void impl(const at::Tensor & self, const at::Tensor & other, const at::Tensor & out);
};
TORCH_API at::Tensor remainder(const at::Tensor & self, const at::Scalar & other);
TORCH_API at::Tensor & remainder_out(const at::Tensor & self, const at::Scalar & other, at::Tensor & out);
TORCH_API at::Tensor & remainder_(at::Tensor & self, const at::Scalar & other);
struct TORCH_API structured_remainder_out : public at::meta::structured_remainder_Tensor {
void impl(const at::Tensor & self, const at::Tensor & other, const at::Tensor & out);
};
TORCH_API at::Tensor remainder(const at::Scalar & self, const at::Tensor & other);
TORCH_API at::Tensor min(const at::Tensor & self);
TORCH_API at::Tensor min_quantized_cpu(const at::Tensor & self);
struct TORCH_API structured_fmin_out : public at::meta::structured_fmin {
void impl(const at::Tensor & self, const at::Tensor & other, const at::Tensor & out);
};
TORCH_API at::Tensor max(const at::Tensor & self);
TORCH_API at::Tensor max_quantized_cpu(const at::Tensor & self);
struct TORCH_API structured_fmax_out : public at::meta::structured_fmax {
void impl(const at::Tensor & self, const at::Tensor & other, const at::Tensor & out);
};
struct TORCH_API structured_maximum_out : public at::meta::structured_maximum {
void impl(const at::Tensor & self, const at::Tensor & other, const at::Tensor & out);
};
TORCH_API at::Tensor max(const at::Tensor & self, const at::Tensor & other);
TORCH_API at::Tensor & max_out(const at::Tensor & self, const at::Tensor & other, at::Tensor & out);
struct TORCH_API structured_minimum_out : public at::meta::structured_minimum {
void impl(const at::Tensor & self, const at::Tensor & other, const at::Tensor & out);
};
TORCH_API at::Tensor min(const at::Tensor & self, const at::Tensor & other);
TORCH_API at::Tensor & min_out(const at::Tensor & self, const at::Tensor & other, at::Tensor & out);
TORCH_API at::Tensor quantile(const at::Tensor & self, double q, c10::optional<int64_t> dim=c10::nullopt, bool keepdim=false);
TORCH_API at::Tensor & quantile_out(const at::Tensor & self, double q, c10::optional<int64_t> dim, bool keepdim, at::Tensor & out);
TORCH_API at::Tensor quantile(const at::Tensor & self, const at::Tensor & q, c10::optional<int64_t> dim=c10::nullopt, bool keepdim=false);
TORCH_API at::Tensor & quantile_out(const at::Tensor & self, const at::Tensor & q, c10::optional<int64_t> dim, bool keepdim, at::Tensor & out);
TORCH_API at::Tensor nanquantile(const at::Tensor & self, double q, c10::optional<int64_t> dim=c10::nullopt, bool keepdim=false);
TORCH_API at::Tensor & nanquantile_out(const at::Tensor & self, double q, c10::optional<int64_t> dim, bool keepdim, at::Tensor & out);
TORCH_API at::Tensor nanquantile(const at::Tensor & self, const at::Tensor & q, c10::optional<int64_t> dim=c10::nullopt, bool keepdim=false);
TORCH_API at::Tensor & nanquantile_out(const at::Tensor & self, const at::Tensor & q, c10::optional<int64_t> dim, bool keepdim, at::Tensor & out);
TORCH_API at::Tensor quantile(const at::Tensor & self, double q, c10::optional<int64_t> dim, bool keepdim, c10::string_view interpolation);
TORCH_API at::Tensor & quantile_out(const at::Tensor & self, double q, c10::optional<int64_t> dim, bool keepdim, c10::string_view interpolation, at::Tensor & out);
TORCH_API at::Tensor quantile(const at::Tensor & self, const at::Tensor & q, c10::optional<int64_t> dim, bool keepdim, c10::string_view interpolation);
TORCH_API at::Tensor & quantile_out(const at::Tensor & self, const at::Tensor & q, c10::optional<int64_t> dim, bool keepdim, c10::string_view interpolation, at::Tensor & out);
TORCH_API at::Tensor nanquantile(const at::Tensor & self, double q, c10::optional<int64_t> dim, bool keepdim, c10::string_view interpolation);
TORCH_API at::Tensor & nanquantile_out(const at::Tensor & self, double q, c10::optional<int64_t> dim, bool keepdim, c10::string_view interpolation, at::Tensor & out);
TORCH_API at::Tensor nanquantile(const at::Tensor & self, const at::Tensor & q, c10::optional<int64_t> dim, bool keepdim, c10::string_view interpolation);
TORCH_API at::Tensor & nanquantile_out(const at::Tensor & self, const at::Tensor & q, c10::optional<int64_t> dim, bool keepdim, c10::string_view interpolation, at::Tensor & out);
TORCH_API ::std::tuple<at::Tensor,at::Tensor> sort_cpu(const at::Tensor & self, int64_t dim=-1, bool descending=false);
TORCH_API ::std::tuple<at::Tensor &,at::Tensor &> sort_out_cpu(const at::Tensor & self, int64_t dim, bool descending, at::Tensor & values, at::Tensor & indices);
TORCH_API ::std::tuple<at::Tensor,at::Tensor> sort_cuda(const at::Tensor & self, int64_t dim=-1, bool descending=false);
TORCH_API ::std::tuple<at::Tensor &,at::Tensor &> sort_out_cuda(const at::Tensor & self, int64_t dim, bool descending, at::Tensor & values, at::Tensor & indices);
TORCH_API ::std::tuple<at::Tensor,at::Tensor> sort_quantized_cpu(const at::Tensor & self, int64_t dim=-1, bool descending=false);
TORCH_API ::std::tuple<at::Tensor,at::Tensor> sort_cpu_stable(const at::Tensor & self, c10::optional<bool> stable, int64_t dim=-1, bool descending=false);
TORCH_API ::std::tuple<at::Tensor &,at::Tensor &> sort_out_cpu_stable(const at::Tensor & self, c10::optional<bool> stable, int64_t dim, bool descending, at::Tensor & values, at::Tensor & indices);
TORCH_API ::std::tuple<at::Tensor,at::Tensor> sort_stable_cuda(const at::Tensor & self, c10::optional<bool> stable, int64_t dim=-1, bool descending=false);
TORCH_API ::std::tuple<at::Tensor &,at::Tensor &> sort_out_stable_cuda(const at::Tensor & self, c10::optional<bool> stable, int64_t dim, bool descending, at::Tensor & values, at::Tensor & indices);
TORCH_API ::std::tuple<at::Tensor,at::Tensor> sort_quantized_cpu_stable(const at::Tensor & self, c10::optional<bool> stable, int64_t dim=-1, bool descending=false);
TORCH_API ::std::tuple<at::Tensor,at::Tensor> sort(const at::Tensor & self, at::Dimname dim, bool descending=false);
TORCH_API ::std::tuple<at::Tensor &,at::Tensor &> sort_out(const at::Tensor & self, at::Dimname dim, bool descending, at::Tensor & values, at::Tensor & indices);
TORCH_API ::std::tuple<at::Tensor,at::Tensor> sort(const at::Tensor & self, c10::optional<bool> stable, at::Dimname dim, bool descending=false);
TORCH_API ::std::tuple<at::Tensor &,at::Tensor &> sort_out(const at::Tensor & self, c10::optional<bool> stable, at::Dimname dim, bool descending, at::Tensor & values, at::Tensor & indices);
TORCH_API at::Tensor msort(const at::Tensor & self);
TORCH_API at::Tensor & msort_out(const at::Tensor & self, at::Tensor & out);
TORCH_API at::Tensor argsort(const at::Tensor & self, int64_t dim=-1, bool descending=false);
TORCH_API at::Tensor argsort(const at::Tensor & self, at::Dimname dim, bool descending=false);
struct TORCH_API structured_topk_out_cpu : public at::meta::structured_topk {
void impl(const at::Tensor & self, int64_t k, int64_t dim, bool largest, bool sorted, const at::Tensor & values, const at::Tensor & indices);
};
struct TORCH_API structured_topk_out_cuda : public at::meta::structured_topk {
void impl(const at::Tensor & self, int64_t k, int64_t dim, bool largest, bool sorted, const at::Tensor & values, const at::Tensor & indices);
};
TORCH_API ::std::tuple<at::Tensor,at::Tensor> topk_quantized_cpu(const at::Tensor & self, int64_t k, int64_t dim=-1, bool largest=true, bool sorted=true);
struct TORCH_API structured_all_all_out : public at::meta::structured_all {
void impl(const at::Tensor & self, const at::Tensor & out);
};
struct TORCH_API structured_any_all_out : public at::meta::structured_any {
void impl(const at::Tensor & self, const at::Tensor & out);
};
TORCH_API at::Tensor any_sparse(const at::Tensor & self);
struct TORCH_API structured_renorm_out : public at::meta::structured_renorm {
void impl(const at::Tensor & self, const at::Scalar & p, int64_t dim, const at::Scalar & maxnorm, const at::Tensor & out);
};
TORCH_API at::Tensor unfold(const at::Tensor & self, int64_t dimension, int64_t size, int64_t step);
TORCH_API at::Tensor unfold_backward(const at::Tensor & grad_in, at::IntArrayRef input_sizes, int64_t dim, int64_t size, int64_t step);
TORCH_API bool cpu_equal(const at::Tensor & self, const at::Tensor & other);
TORCH_API bool cuda_equal(const at::Tensor & self, const at::Tensor & other);
TORCH_API bool equal_quantized_cpu(const at::Tensor & self, const at::Tensor & other);
struct TORCH_API structured_pow_Tensor_Tensor_out : public at::meta::structured_pow_Tensor_Tensor {
void impl(const at::Tensor & self, const at::Tensor & exponent, const at::Tensor & out);
};
struct TORCH_API structured_pow_Scalar_out : public at::meta::structured_pow_Scalar {
void impl(const at::Scalar & self, const at::Tensor & exponent, const at::Tensor & out);
};
struct TORCH_API structured_pow_Tensor_Scalar_out : public at::meta::structured_pow_Tensor_Scalar {
void impl(const at::Tensor & self, const at::Scalar & exponent, const at::Tensor & out);
};
TORCH_API at::Tensor pow_sparse_scalar(const at::Tensor & self, const at::Scalar & exponent);
TORCH_API at::Tensor & pow_out_sparse_scalar(const at::Tensor & self, const at::Scalar & exponent, at::Tensor & out);
TORCH_API at::Tensor float_power(const at::Tensor & self, const at::Tensor & exponent);
TORCH_API at::Tensor & float_power_out(const at::Tensor & self, const at::Tensor & exponent, at::Tensor & out);
TORCH_API at::Tensor & float_power_(at::Tensor & self, const at::Tensor & exponent);
TORCH_API at::Tensor float_power(const at::Scalar & self, const at::Tensor & exponent);
TORCH_API at::Tensor & float_power_out(const at::Scalar & self, const at::Tensor & exponent, at::Tensor & out);
TORCH_API at::Tensor float_power(const at::Tensor & self, const at::Scalar & exponent);
TORCH_API at::Tensor & float_power_out(const at::Tensor & self, const at::Scalar & exponent, at::Tensor & out);
TORCH_API at::Tensor & float_power_(at::Tensor & self, const at::Scalar & exponent);
TORCH_API at::Tensor & normal_(at::Tensor & self, double mean=0, double std=1, c10::optional<at::Generator> generator=c10::nullopt);
TORCH_API at::Tensor & normal_meta_(at::Tensor & self, double mean=0, double std=1, c10::optional<at::Generator> generator=c10::nullopt);
TORCH_API at::Tensor normal(const at::Tensor & mean, double std=1, c10::optional<at::Generator> generator=c10::nullopt);
TORCH_API at::Tensor & normal_out(const at::Tensor & mean, double std, c10::optional<at::Generator> generator, at::Tensor & out);
TORCH_API at::Tensor normal(double mean, const at::Tensor & std, c10::optional<at::Generator> generator=c10::nullopt);
TORCH_API at::Tensor & normal_out(double mean, const at::Tensor & std, c10::optional<at::Generator> generator, at::Tensor & out);
TORCH_API at::Tensor normal(const at::Tensor & mean, const at::Tensor & std, c10::optional<at::Generator> generator=c10::nullopt);
TORCH_API at::Tensor & normal_out(const at::Tensor & mean, const at::Tensor & std, c10::optional<at::Generator> generator, at::Tensor & out);
TORCH_API at::Tensor normal(double mean, double std, at::IntArrayRef size, c10::optional<at::Generator> generator=c10::nullopt, c10::optional<at::ScalarType> dtype={}, c10::optional<at::Layout> layout={}, c10::optional<at::Device> device={}, c10::optional<bool> pin_memory={});
TORCH_API at::Tensor & normal_out(double mean, double std, at::IntArrayRef size, c10::optional<at::Generator> generator, at::Tensor & out);
TORCH_API at::Tensor alias(const at::Tensor & self);
TORCH_API at::Tensor & _index_copy_impl_(at::Tensor & self, int64_t dim, const at::Tensor & index, const at::Tensor & source);
TORCH_API void _amp_foreach_non_finite_check_and_unscale_cuda_(at::TensorList self, at::Tensor & found_inf, const at::Tensor & inv_scale);
TORCH_API at::Tensor & _amp_update_scale_cuda_(at::Tensor & self, at::Tensor & growth_tracker, const at::Tensor & found_inf, double scale_growth_factor, double scale_backoff_factor, int64_t growth_interval);
TORCH_API at::Tensor _cat_cpu(at::TensorList tensors, int64_t dim=0);
TORCH_API at::Tensor & _cat_out_cpu(at::TensorList tensors, int64_t dim, at::Tensor & out);
TORCH_API at::Tensor cat_cuda(at::TensorList tensors, int64_t dim=0);
TORCH_API at::Tensor & cat_out_cuda(at::TensorList tensors, int64_t dim, at::Tensor & out);
TORCH_API at::Tensor cat_quantized_cpu(at::TensorList tensors, int64_t dim=0);
TORCH_API at::Tensor & cat_out_quantized_cpu(at::TensorList tensors, int64_t dim, at::Tensor & out);
TORCH_API ::std::vector<at::Tensor> foreach_tensor_add_scalar_kernel_slow(at::TensorList tensors, const at::Scalar & scalar);
TORCH_API ::std::vector<at::Tensor> foreach_tensor_add_scalar_kernel_cuda(at::TensorList tensors, const at::Scalar & scalar);
TORCH_API void foreach_tensor_add_scalar_kernel_slow_(at::TensorList self, const at::Scalar & scalar);
TORCH_API void foreach_tensor_add_scalar_kernel_cuda_(at::TensorList self, const at::Scalar & scalar);
TORCH_API ::std::vector<at::Tensor> foreach_tensor_sub_scalar_kernel_slow(at::TensorList tensors, const at::Scalar & scalar);
TORCH_API ::std::vector<at::Tensor> foreach_tensor_sub_scalar_kernel_cuda(at::TensorList tensors, const at::Scalar & scalar);
TORCH_API void foreach_tensor_sub_scalar_kernel_slow_(at::TensorList self, const at::Scalar & scalar);
TORCH_API void foreach_tensor_sub_scalar_kernel_cuda_(at::TensorList self, const at::Scalar & scalar);
TORCH_API ::std::vector<at::Tensor> foreach_tensor_mul_scalar_kernel_slow(at::TensorList tensors, const at::Scalar & scalar);
TORCH_API ::std::vector<at::Tensor> foreach_tensor_mul_scalar_kernel_cuda(at::TensorList tensors, const at::Scalar & scalar);
TORCH_API void foreach_tensor_mul_scalar_kernel_slow_(at::TensorList self, const at::Scalar & scalar);
TORCH_API void foreach_tensor_mul_scalar_kernel_cuda_(at::TensorList self, const at::Scalar & scalar);
TORCH_API ::std::vector<at::Tensor> foreach_tensor_div_scalar_kernel_slow(at::TensorList tensors, const at::Scalar & scalar);
TORCH_API ::std::vector<at::Tensor> foreach_tensor_div_scalar_kernel_cuda(at::TensorList tensors, const at::Scalar & scalar);
TORCH_API void foreach_tensor_div_scalar_kernel_slow_(at::TensorList self, const at::Scalar & scalar);
TORCH_API void foreach_tensor_div_scalar_kernel_cuda_(at::TensorList self, const at::Scalar & scalar);
TORCH_API ::std::vector<at::Tensor> foreach_tensor_add_list_kernel_slow(at::TensorList tensors1, at::TensorList tensors2, const at::Scalar & alpha=1);
TORCH_API ::std::vector<at::Tensor> foreach_tensor_add_list_kernel_cuda(at::TensorList tensors1, at::TensorList tensors2, const at::Scalar & alpha=1);
TORCH_API void foreach_tensor_add_list_kernel_slow_(at::TensorList self, at::TensorList other, const at::Scalar & alpha=1);
TORCH_API void foreach_tensor_add_list_kernel_cuda_(at::TensorList self, at::TensorList other, const at::Scalar & alpha=1);
TORCH_API ::std::vector<at::Tensor> foreach_tensor_sub_list_kernel_slow(at::TensorList tensors1, at::TensorList tensors2, const at::Scalar & alpha=1);
TORCH_API ::std::vector<at::Tensor> foreach_tensor_sub_list_kernel_cuda(at::TensorList tensors1, at::TensorList tensors2, const at::Scalar & alpha=1);
TORCH_API void foreach_tensor_sub_list_kernel_slow_(at::TensorList self, at::TensorList other, const at::Scalar & alpha=1);
TORCH_API void foreach_tensor_sub_list_kernel_cuda_(at::TensorList self, at::TensorList other, const at::Scalar & alpha=1);
TORCH_API ::std::vector<at::Tensor> foreach_tensor_mul_list_kernel_slow(at::TensorList tensors1, at::TensorList tensors2);
TORCH_API ::std::vector<at::Tensor> foreach_tensor_mul_list_kernel_cuda(at::TensorList tensors1, at::TensorList tensors2);
TORCH_API void foreach_tensor_mul_list_kernel_slow_(at::TensorList self, at::TensorList other);
TORCH_API void foreach_tensor_mul_list_kernel_cuda_(at::TensorList self, at::TensorList other);
TORCH_API ::std::vector<at::Tensor> foreach_tensor_div_list_kernel_slow(at::TensorList tensors1, at::TensorList tensors2);
TORCH_API ::std::vector<at::Tensor> foreach_tensor_div_list_kernel_cuda(at::TensorList tensors1, at::TensorList tensors2);
TORCH_API void foreach_tensor_div_list_kernel_slow_(at::TensorList self, at::TensorList other);
TORCH_API void foreach_tensor_div_list_kernel_cuda_(at::TensorList self, at::TensorList other);
TORCH_API ::std::vector<at::Tensor> foreach_tensor_add_scalarlist_kernel_slow(at::TensorList tensors, at::ArrayRef<at::Scalar> scalars);
TORCH_API ::std::vector<at::Tensor> foreach_tensor_add_scalarlist_kernel_cuda(at::TensorList tensors, at::ArrayRef<at::Scalar> scalars);
TORCH_API void foreach_tensor_add_scalarlist_kernel_slow_(at::TensorList self, at::ArrayRef<at::Scalar> scalars);
TORCH_API void foreach_tensor_add_scalarlist_kernel_cuda_(at::TensorList self, at::ArrayRef<at::Scalar> scalars);
TORCH_API ::std::vector<at::Tensor> foreach_tensor_sub_scalarlist_kernel_slow(at::TensorList tensors, at::ArrayRef<at::Scalar> scalars);
TORCH_API ::std::vector<at::Tensor> foreach_tensor_sub_scalarlist_kernel_cuda(at::TensorList tensors, at::ArrayRef<at::Scalar> scalars);
TORCH_API void foreach_tensor_sub_scalarlist_kernel_slow_(at::TensorList self, at::ArrayRef<at::Scalar> scalars);
TORCH_API void foreach_tensor_sub_scalarlist_kernel_cuda_(at::TensorList self, at::ArrayRef<at::Scalar> scalars);
TORCH_API ::std::vector<at::Tensor> foreach_tensor_div_scalarlist_kernel_slow(at::TensorList tensors, at::ArrayRef<at::Scalar> scalars);
TORCH_API ::std::vector<at::Tensor> foreach_tensor_div_scalarlist_kernel_cuda(at::TensorList tensors, at::ArrayRef<at::Scalar> scalars);
TORCH_API void foreach_tensor_div_scalarlist_kernel_slow_(at::TensorList self, at::ArrayRef<at::Scalar> scalars);
TORCH_API void foreach_tensor_div_scalarlist_kernel_cuda_(at::TensorList self, at::ArrayRef<at::Scalar> scalars);
TORCH_API ::std::vector<at::Tensor> foreach_tensor_mul_scalarlist_kernel_slow(at::TensorList tensors, at::ArrayRef<at::Scalar> scalars);
TORCH_API ::std::vector<at::Tensor> foreach_tensor_mul_scalarlist_kernel_cuda(at::TensorList tensors, at::ArrayRef<at::Scalar> scalars);
TORCH_API void foreach_tensor_mul_scalarlist_kernel_slow_(at::TensorList self, at::ArrayRef<at::Scalar> scalars);
TORCH_API void foreach_tensor_mul_scalarlist_kernel_cuda_(at::TensorList self, at::ArrayRef<at::Scalar> scalars);
TORCH_API ::std::vector<at::Tensor> foreach_tensor_exp_slow(at::TensorList tensors);
TORCH_API ::std::vector<at::Tensor> foreach_tensor_exp_cuda(at::TensorList tensors);
TORCH_API void foreach_tensor_zero_slow_(at::TensorList self);
TORCH_API void foreach_tensor_zero_cuda_(at::TensorList self);
TORCH_API void foreach_tensor_exp_slow_(at::TensorList self);
TORCH_API void foreach_tensor_exp_cuda_(at::TensorList self);
TORCH_API ::std::vector<at::Tensor> foreach_tensor_sqrt_slow(at::TensorList tensors);
TORCH_API ::std::vector<at::Tensor> foreach_tensor_sqrt_cuda(at::TensorList tensors);
TORCH_API void foreach_tensor_sqrt_slow_(at::TensorList self);
TORCH_API void foreach_tensor_sqrt_cuda_(at::TensorList self);
TORCH_API ::std::vector<at::Tensor> foreach_tensor_abs_slow(at::TensorList tensors);
TORCH_API ::std::vector<at::Tensor> foreach_tensor_abs_cuda(at::TensorList tensors);
TORCH_API void foreach_tensor_abs_slow_(at::TensorList self);
TORCH_API void foreach_tensor_abs_cuda_(at::TensorList self);
TORCH_API ::std::vector<at::Tensor> foreach_tensor_acos_slow(at::TensorList tensors);
TORCH_API ::std::vector<at::Tensor> foreach_tensor_acos_cuda(at::TensorList tensors);
TORCH_API void foreach_tensor_acos_slow_(at::TensorList self);
TORCH_API void foreach_tensor_acos_cuda_(at::TensorList self);
TORCH_API ::std::vector<at::Tensor> foreach_tensor_asin_slow(at::TensorList tensors);
TORCH_API ::std::vector<at::Tensor> foreach_tensor_asin_cuda(at::TensorList tensors);
TORCH_API void foreach_tensor_asin_slow_(at::TensorList self);
TORCH_API void foreach_tensor_asin_cuda_(at::TensorList self);
TORCH_API ::std::vector<at::Tensor> foreach_tensor_atan_slow(at::TensorList tensors);
TORCH_API ::std::vector<at::Tensor> foreach_tensor_atan_cuda(at::TensorList tensors);
TORCH_API void foreach_tensor_atan_slow_(at::TensorList self);
TORCH_API void foreach_tensor_atan_cuda_(at::TensorList self);
TORCH_API ::std::vector<at::Tensor> foreach_tensor_ceil_slow(at::TensorList tensors);
TORCH_API ::std::vector<at::Tensor> foreach_tensor_ceil_cuda(at::TensorList tensors);
TORCH_API void foreach_tensor_ceil_slow_(at::TensorList self);
TORCH_API void foreach_tensor_ceil_cuda_(at::TensorList self);
TORCH_API ::std::vector<at::Tensor> foreach_tensor_cos_slow(at::TensorList tensors);
TORCH_API ::std::vector<at::Tensor> foreach_tensor_cos_cuda(at::TensorList tensors);
TORCH_API void foreach_tensor_cos_slow_(at::TensorList self);
TORCH_API void foreach_tensor_cos_cuda_(at::TensorList self);
TORCH_API ::std::vector<at::Tensor> foreach_tensor_cosh_slow(at::TensorList tensors);
TORCH_API ::std::vector<at::Tensor> foreach_tensor_cosh_cuda(at::TensorList tensors);
TORCH_API void foreach_tensor_cosh_slow_(at::TensorList self);
TORCH_API void foreach_tensor_cosh_cuda_(at::TensorList self);
TORCH_API ::std::vector<at::Tensor> foreach_tensor_erf_slow(at::TensorList tensors);
TORCH_API ::std::vector<at::Tensor> foreach_tensor_erf_cuda(at::TensorList tensors);
TORCH_API void foreach_tensor_erf_slow_(at::TensorList self);
TORCH_API void foreach_tensor_erf_cuda_(at::TensorList self);
TORCH_API ::std::vector<at::Tensor> foreach_tensor_erfc_slow(at::TensorList tensors);
TORCH_API ::std::vector<at::Tensor> foreach_tensor_erfc_cuda(at::TensorList tensors);
TORCH_API void foreach_tensor_erfc_slow_(at::TensorList self);
TORCH_API void foreach_tensor_erfc_cuda_(at::TensorList self);
TORCH_API ::std::vector<at::Tensor> foreach_tensor_expm1_slow(at::TensorList tensors);
TORCH_API ::std::vector<at::Tensor> foreach_tensor_expm1_cuda(at::TensorList tensors);
TORCH_API void foreach_tensor_expm1_slow_(at::TensorList self);
TORCH_API void foreach_tensor_expm1_cuda_(at::TensorList self);
TORCH_API ::std::vector<at::Tensor> foreach_tensor_floor_slow(at::TensorList tensors);
TORCH_API ::std::vector<at::Tensor> foreach_tensor_floor_cuda(at::TensorList tensors);
TORCH_API void foreach_tensor_floor_slow_(at::TensorList self);
TORCH_API void foreach_tensor_floor_cuda_(at::TensorList self);
TORCH_API ::std::vector<at::Tensor> foreach_tensor_log_slow(at::TensorList tensors);
TORCH_API ::std::vector<at::Tensor> foreach_tensor_log_cuda(at::TensorList tensors);
TORCH_API void foreach_tensor_log_slow_(at::TensorList self);
TORCH_API void foreach_tensor_log_cuda_(at::TensorList self);
TORCH_API ::std::vector<at::Tensor> foreach_tensor_log10_slow(at::TensorList tensors);
TORCH_API ::std::vector<at::Tensor> foreach_tensor_log10_cuda(at::TensorList tensors);
TORCH_API void foreach_tensor_log10_slow_(at::TensorList self);
TORCH_API void foreach_tensor_log10_cuda_(at::TensorList self);
TORCH_API ::std::vector<at::Tensor> foreach_tensor_log1p_slow(at::TensorList tensors);
TORCH_API ::std::vector<at::Tensor> foreach_tensor_log1p_cuda(at::TensorList tensors);
TORCH_API void foreach_tensor_log1p_slow_(at::TensorList self);
TORCH_API void foreach_tensor_log1p_cuda_(at::TensorList self);
TORCH_API ::std::vector<at::Tensor> foreach_tensor_log2_slow(at::TensorList tensors);
TORCH_API ::std::vector<at::Tensor> foreach_tensor_log2_cuda(at::TensorList tensors);
TORCH_API void foreach_tensor_log2_slow_(at::TensorList self);
TORCH_API void foreach_tensor_log2_cuda_(at::TensorList self);
TORCH_API ::std::vector<at::Tensor> foreach_tensor_neg_slow(at::TensorList tensors);
TORCH_API ::std::vector<at::Tensor> foreach_tensor_neg_cuda(at::TensorList tensors);
TORCH_API void foreach_tensor_neg_slow_(at::TensorList self);
TORCH_API void foreach_tensor_neg_cuda_(at::TensorList self);
TORCH_API ::std::vector<at::Tensor> foreach_tensor_tan_slow(at::TensorList tensors);
TORCH_API ::std::vector<at::Tensor> foreach_tensor_tan_cuda(at::TensorList tensors);
TORCH_API void foreach_tensor_tan_slow_(at::TensorList self);
TORCH_API void foreach_tensor_tan_cuda_(at::TensorList self);
TORCH_API ::std::vector<at::Tensor> foreach_tensor_tanh_slow(at::TensorList tensors);
TORCH_API ::std::vector<at::Tensor> foreach_tensor_tanh_cuda(at::TensorList tensors);
TORCH_API void foreach_tensor_tanh_slow_(at::TensorList self);
TORCH_API void foreach_tensor_tanh_cuda_(at::TensorList self);
TORCH_API ::std::vector<at::Tensor> foreach_tensor_sin_slow(at::TensorList tensors);
TORCH_API ::std::vector<at::Tensor> foreach_tensor_sin_cuda(at::TensorList tensors);
TORCH_API void foreach_tensor_sin_slow_(at::TensorList self);
TORCH_API void foreach_tensor_sin_cuda_(at::TensorList self);
TORCH_API ::std::vector<at::Tensor> foreach_tensor_sinh_slow(at::TensorList tensors);
TORCH_API ::std::vector<at::Tensor> foreach_tensor_sinh_cuda(at::TensorList tensors);
TORCH_API void foreach_tensor_sinh_slow_(at::TensorList self);
TORCH_API void foreach_tensor_sinh_cuda_(at::TensorList self);
TORCH_API ::std::vector<at::Tensor> foreach_tensor_round_slow(at::TensorList tensors);
TORCH_API ::std::vector<at::Tensor> foreach_tensor_round_cuda(at::TensorList tensors);
TORCH_API void foreach_tensor_round_slow_(at::TensorList self);
TORCH_API void foreach_tensor_round_cuda_(at::TensorList self);
TORCH_API ::std::vector<at::Tensor> foreach_tensor_lgamma_slow(at::TensorList tensors);
TORCH_API ::std::vector<at::Tensor> foreach_tensor_lgamma_cuda(at::TensorList tensors);
TORCH_API void foreach_tensor_lgamma_slow_(at::TensorList self);
TORCH_API void foreach_tensor_lgamma_cuda_(at::TensorList self);
TORCH_API ::std::vector<at::Tensor> foreach_tensor_frac_slow(at::TensorList tensors);
TORCH_API ::std::vector<at::Tensor> foreach_tensor_frac_cuda(at::TensorList tensors);
TORCH_API void foreach_tensor_frac_slow_(at::TensorList self);
TORCH_API void foreach_tensor_frac_cuda_(at::TensorList self);
TORCH_API ::std::vector<at::Tensor> foreach_tensor_reciprocal_slow(at::TensorList tensors);
TORCH_API ::std::vector<at::Tensor> foreach_tensor_reciprocal_cuda(at::TensorList tensors);
TORCH_API void foreach_tensor_reciprocal_slow_(at::TensorList self);
TORCH_API void foreach_tensor_reciprocal_cuda_(at::TensorList self);
TORCH_API ::std::vector<at::Tensor> foreach_tensor_sigmoid_slow(at::TensorList tensors);
TORCH_API ::std::vector<at::Tensor> foreach_tensor_sigmoid_cuda(at::TensorList tensors);
TORCH_API void foreach_tensor_sigmoid_slow_(at::TensorList self);
TORCH_API void foreach_tensor_sigmoid_cuda_(at::TensorList self);
TORCH_API ::std::vector<at::Tensor> foreach_tensor_trunc_slow(at::TensorList tensors);
TORCH_API ::std::vector<at::Tensor> foreach_tensor_trunc_cuda(at::TensorList tensors);
TORCH_API void foreach_tensor_trunc_slow_(at::TensorList self);
TORCH_API void foreach_tensor_trunc_cuda_(at::TensorList self);
TORCH_API void foreach_tensor_addcdiv_scalar_slow_(at::TensorList self, at::TensorList tensor1, at::TensorList tensor2, const at::Scalar & value=1);
TORCH_API void foreach_tensor_addcdiv_scalar_cuda_(at::TensorList self, at::TensorList tensor1, at::TensorList tensor2, const at::Scalar & value=1);
TORCH_API void foreach_tensor_addcmul_scalar_slow_(at::TensorList self, at::TensorList tensor1, at::TensorList tensor2, const at::Scalar & value=1);
TORCH_API void foreach_tensor_addcmul_scalar_cuda_(at::TensorList self, at::TensorList tensor1, at::TensorList tensor2, const at::Scalar & value=1);
TORCH_API void foreach_tensor_addcdiv_scalarlist_slow_(at::TensorList self, at::TensorList tensor1, at::TensorList tensor2, at::ArrayRef<at::Scalar> scalars);
TORCH_API void foreach_tensor_addcdiv_scalarlist_cuda_(at::TensorList self, at::TensorList tensor1, at::TensorList tensor2, at::ArrayRef<at::Scalar> scalars);
TORCH_API void foreach_tensor_addcmul_scalarlist_slow_(at::TensorList self, at::TensorList tensor1, at::TensorList tensor2, at::ArrayRef<at::Scalar> scalars);
TORCH_API void foreach_tensor_addcmul_scalarlist_cuda_(at::TensorList self, at::TensorList tensor1, at::TensorList tensor2, at::ArrayRef<at::Scalar> scalars);
TORCH_API ::std::vector<at::Tensor> foreach_tensor_addcdiv_scalar_slow(at::TensorList input, at::TensorList tensor1, at::TensorList tensor2, const at::Scalar & value=1);
TORCH_API ::std::vector<at::Tensor> foreach_tensor_addcdiv_scalar_cuda(at::TensorList input, at::TensorList tensor1, at::TensorList tensor2, const at::Scalar & value=1);
TORCH_API ::std::vector<at::Tensor> foreach_tensor_addcmul_scalar_slow(at::TensorList input, at::TensorList tensor1, at::TensorList tensor2, const at::Scalar & value=1);
TORCH_API ::std::vector<at::Tensor> foreach_tensor_addcmul_scalar_cuda(at::TensorList input, at::TensorList tensor1, at::TensorList tensor2, const at::Scalar & value=1);
TORCH_API ::std::vector<at::Tensor> foreach_tensor_addcdiv_scalarlist_slow(at::TensorList input, at::TensorList tensor1, at::TensorList tensor2, at::ArrayRef<at::Scalar> scalars);
TORCH_API ::std::vector<at::Tensor> foreach_tensor_addcdiv_scalarlist_cuda(at::TensorList input, at::TensorList tensor1, at::TensorList tensor2, at::ArrayRef<at::Scalar> scalars);
TORCH_API ::std::vector<at::Tensor> foreach_tensor_addcmul_scalarlist_slow(at::TensorList input, at::TensorList tensor1, at::TensorList tensor2, at::ArrayRef<at::Scalar> scalars);
TORCH_API ::std::vector<at::Tensor> foreach_tensor_addcmul_scalarlist_cuda(at::TensorList input, at::TensorList tensor1, at::TensorList tensor2, at::ArrayRef<at::Scalar> scalars);
TORCH_API ::std::vector<at::Tensor> foreach_tensor_maximum_slow(at::TensorList tensors1, at::TensorList tensors2);
TORCH_API ::std::vector<at::Tensor> foreach_tensor_maximum_cuda(at::TensorList tensors1, at::TensorList tensors2);
TORCH_API ::std::vector<at::Tensor> foreach_tensor_minimum_slow(at::TensorList tensors1, at::TensorList tensors2);
TORCH_API ::std::vector<at::Tensor> foreach_tensor_minimum_cuda(at::TensorList tensors1, at::TensorList tensors2);
TORCH_API at::Tensor bucketize_cpu(const at::Tensor & self, const at::Tensor & boundaries, bool out_int32=false, bool right=false);
TORCH_API at::Tensor & bucketize_out_cpu(const at::Tensor & self, const at::Tensor & boundaries, bool out_int32, bool right, at::Tensor & out);
TORCH_API at::Tensor bucketize_cuda(const at::Tensor & self, const at::Tensor & boundaries, bool out_int32=false, bool right=false);
TORCH_API at::Tensor & bucketize_out_cuda(const at::Tensor & self, const at::Tensor & boundaries, bool out_int32, bool right, at::Tensor & out);
TORCH_API at::Tensor bucketize_cpu(const at::Scalar & self, const at::Tensor & boundaries, bool out_int32=false, bool right=false);
TORCH_API at::Tensor bucketize_cuda(const at::Scalar & self, const at::Tensor & boundaries, bool out_int32=false, bool right=false);
TORCH_API at::Tensor searchsorted_cpu(const at::Tensor & sorted_sequence, const at::Tensor & self, bool out_int32=false, bool right=false);
TORCH_API at::Tensor & searchsorted_out_cpu(const at::Tensor & sorted_sequence, const at::Tensor & self, bool out_int32, bool right, at::Tensor & out);
TORCH_API at::Tensor searchsorted_cuda(const at::Tensor & sorted_sequence, const at::Tensor & self, bool out_int32=false, bool right=false);
TORCH_API at::Tensor & searchsorted_out_cuda(const at::Tensor & sorted_sequence, const at::Tensor & self, bool out_int32, bool right, at::Tensor & out);
TORCH_API at::Tensor searchsorted_cpu(const at::Tensor & sorted_sequence, const at::Scalar & self, bool out_int32=false, bool right=false);
TORCH_API at::Tensor searchsorted_cuda(const at::Tensor & sorted_sequence, const at::Scalar & self, bool out_int32=false, bool right=false);
struct TORCH_API structured__convert_indices_from_coo_to_csr_structured_cpu : public at::meta::structured__convert_indices_from_coo_to_csr {
void impl(const at::Tensor & self, int64_t size, bool out_int32, const at::Tensor & out);
};
struct TORCH_API structured__convert_indices_from_coo_to_csr_structured_cuda : public at::meta::structured__convert_indices_from_coo_to_csr {
void impl(const at::Tensor & self, int64_t size, bool out_int32, const at::Tensor & out);
};
TORCH_API at::Tensor mse_loss(const at::Tensor & self, const at::Tensor & target, int64_t reduction=at::Reduction::Mean);
TORCH_API at::Tensor & mse_loss_out(const at::Tensor & self, const at::Tensor & target, int64_t reduction, at::Tensor & out);
TORCH_API at::Tensor mse_loss_backward(const at::Tensor & grad_output, const at::Tensor & self, const at::Tensor & target, int64_t reduction);
TORCH_API at::Tensor & mse_loss_backward_out(const at::Tensor & grad_output, const at::Tensor & self, const at::Tensor & target, int64_t reduction, at::Tensor & grad_input);
TORCH_API at::Tensor l1_loss(const at::Tensor & self, const at::Tensor & target, int64_t reduction=at::Reduction::Mean);
TORCH_API at::Tensor & l1_loss_out(const at::Tensor & self, const at::Tensor & target, int64_t reduction, at::Tensor & out);
TORCH_API at::Tensor l1_loss_backward(const at::Tensor & grad_output, const at::Tensor & self, const at::Tensor & target, int64_t reduction);
TORCH_API at::Tensor & l1_loss_backward_out(const at::Tensor & grad_output, const at::Tensor & self, const at::Tensor & target, int64_t reduction, at::Tensor & grad_input);
TORCH_API at::Tensor multi_margin_loss_cpu(const at::Tensor & self, const at::Tensor & target, const at::Scalar & p=1, const at::Scalar & margin=1, const c10::optional<at::Tensor> & weight={}, int64_t reduction=at::Reduction::Mean);
TORCH_API at::Tensor & multi_margin_loss_cpu_out(const at::Tensor & self, const at::Tensor & target, const at::Scalar & p, const at::Scalar & margin, const c10::optional<at::Tensor> & weight, int64_t reduction, at::Tensor & out);
TORCH_API at::Tensor multi_margin_loss_cuda(const at::Tensor & self, const at::Tensor & target, const at::Scalar & p=1, const at::Scalar & margin=1, const c10::optional<at::Tensor> & weight={}, int64_t reduction=at::Reduction::Mean);
TORCH_API at::Tensor & multi_margin_loss_cuda_out(const at::Tensor & self, const at::Tensor & target, const at::Scalar & p, const at::Scalar & margin, const c10::optional<at::Tensor> & weight, int64_t reduction, at::Tensor & out);
TORCH_API at::Tensor multi_margin_loss_cpu_backward(const at::Tensor & grad_output, const at::Tensor & self, const at::Tensor & target, const at::Scalar & p, const at::Scalar & margin, const c10::optional<at::Tensor> & weight={}, int64_t reduction=at::Reduction::Mean);
TORCH_API at::Tensor & multi_margin_loss_cpu_backward_out(const at::Tensor & grad_output, const at::Tensor & self, const at::Tensor & target, const at::Scalar & p, const at::Scalar & margin, const c10::optional<at::Tensor> & weight, int64_t reduction, at::Tensor & grad_input);
TORCH_API at::Tensor multi_margin_loss_cuda_backward(const at::Tensor & grad_output, const at::Tensor & self, const at::Tensor & target, const at::Scalar & p, const at::Scalar & margin, const c10::optional<at::Tensor> & weight={}, int64_t reduction=at::Reduction::Mean);
TORCH_API at::Tensor & multi_margin_loss_cuda_backward_out(const at::Tensor & grad_output, const at::Tensor & self, const at::Tensor & target, const at::Scalar & p, const at::Scalar & margin, const c10::optional<at::Tensor> & weight, int64_t reduction, at::Tensor & grad_input);
TORCH_API at::Tensor multilabel_margin_loss(const at::Tensor & self, const at::Tensor & target, int64_t reduction=at::Reduction::Mean);
TORCH_API at::Tensor & multilabel_margin_loss_out(const at::Tensor & self, const at::Tensor & target, int64_t reduction, at::Tensor & out);
TORCH_API ::std::tuple<at::Tensor,at::Tensor> multilabel_margin_loss_forward_cpu(const at::Tensor & self, const at::Tensor & target, int64_t reduction);
TORCH_API ::std::tuple<at::Tensor &,at::Tensor &> multilabel_margin_loss_forward_out_cpu(const at::Tensor & self, const at::Tensor & target, int64_t reduction, at::Tensor & output, at::Tensor & is_target);
TORCH_API ::std::tuple<at::Tensor,at::Tensor> multilabel_margin_loss_forward_cuda(const at::Tensor & self, const at::Tensor & target, int64_t reduction);
TORCH_API ::std::tuple<at::Tensor &,at::Tensor &> multilabel_margin_loss_forward_out_cuda(const at::Tensor & self, const at::Tensor & target, int64_t reduction, at::Tensor & output, at::Tensor & is_target);
TORCH_API at::Tensor multilabel_margin_loss_backward_cpu(const at::Tensor & grad_output, const at::Tensor & self, const at::Tensor & target, int64_t reduction, const at::Tensor & is_target);
TORCH_API at::Tensor & multilabel_margin_loss_backward_cpu_out(const at::Tensor & grad_output, const at::Tensor & self, const at::Tensor & target, int64_t reduction, const at::Tensor & is_target, at::Tensor & grad_input);
TORCH_API at::Tensor multilabel_margin_loss_backward_cuda(const at::Tensor & grad_output, const at::Tensor & self, const at::Tensor & target, int64_t reduction, const at::Tensor & is_target);
TORCH_API at::Tensor & multilabel_margin_loss_backward_cuda_out(const at::Tensor & grad_output, const at::Tensor & self, const at::Tensor & target, int64_t reduction, const at::Tensor & is_target, at::Tensor & grad_input);
TORCH_API at::Tensor nll_loss(const at::Tensor & self, const at::Tensor & target, const c10::optional<at::Tensor> & weight={}, int64_t reduction=at::Reduction::Mean, int64_t ignore_index=-100);
TORCH_API at::Tensor & nll_loss_out(const at::Tensor & self, const at::Tensor & target, const c10::optional<at::Tensor> & weight, int64_t reduction, int64_t ignore_index, at::Tensor & out);
TORCH_API at::Tensor nll_loss_nd(const at::Tensor & self, const at::Tensor & target, const c10::optional<at::Tensor> & weight={}, int64_t reduction=at::Reduction::Mean, int64_t ignore_index=-100);
struct TORCH_API structured_nll_loss_forward_out_cpu : public at::meta::structured_nll_loss_forward {
void impl(const at::Tensor & self, const at::Tensor & target, at::OptionalTensorRef weight, int64_t reduction, int64_t ignore_index, const at::Tensor & output, const at::Tensor & total_weight);
};
struct TORCH_API structured_nll_loss_forward_out_cuda : public at::meta::structured_nll_loss_forward {
void impl(const at::Tensor & self, const at::Tensor & target, at::OptionalTensorRef weight, int64_t reduction, int64_t ignore_index, const at::Tensor & output, const at::Tensor & total_weight);
};
struct TORCH_API structured_nll_loss_backward_out_cpu : public at::meta::structured_nll_loss_backward {
void impl(const at::Tensor & grad_output, const at::Tensor & self, const at::Tensor & target, at::OptionalTensorRef weight, int64_t reduction, int64_t ignore_index, const at::Tensor & total_weight, const at::Tensor & grad_input);
};
struct TORCH_API structured_nll_loss_backward_out_cuda : public at::meta::structured_nll_loss_backward {
void impl(const at::Tensor & grad_output, const at::Tensor & self, const at::Tensor & target, at::OptionalTensorRef weight, int64_t reduction, int64_t ignore_index, const at::Tensor & total_weight, const at::Tensor & grad_input);
};
TORCH_API at::Tensor nll_loss2d(const at::Tensor & self, const at::Tensor & target, const c10::optional<at::Tensor> & weight={}, int64_t reduction=at::Reduction::Mean, int64_t ignore_index=-100);
TORCH_API at::Tensor & nll_loss2d_out(const at::Tensor & self, const at::Tensor & target, const c10::optional<at::Tensor> & weight, int64_t reduction, int64_t ignore_index, at::Tensor & out);
TORCH_API ::std::tuple<at::Tensor,at::Tensor> nll_loss2d_forward_cpu(const at::Tensor & self, const at::Tensor & target, const c10::optional<at::Tensor> & weight, int64_t reduction, int64_t ignore_index);
TORCH_API ::std::tuple<at::Tensor &,at::Tensor &> nll_loss2d_forward_out_cpu(const at::Tensor & self, const at::Tensor & target, const c10::optional<at::Tensor> & weight, int64_t reduction, int64_t ignore_index, at::Tensor & output, at::Tensor & total_weight);
TORCH_API ::std::tuple<at::Tensor,at::Tensor> nll_loss2d_forward_cuda(const at::Tensor & self, const at::Tensor & target, const c10::optional<at::Tensor> & weight, int64_t reduction, int64_t ignore_index);
TORCH_API ::std::tuple<at::Tensor &,at::Tensor &> nll_loss2d_forward_out_cuda(const at::Tensor & self, const at::Tensor & target, const c10::optional<at::Tensor> & weight, int64_t reduction, int64_t ignore_index, at::Tensor & output, at::Tensor & total_weight);
TORCH_API at::Tensor nll_loss2d_backward_cpu(const at::Tensor & grad_output, const at::Tensor & self, const at::Tensor & target, const c10::optional<at::Tensor> & weight, int64_t reduction, int64_t ignore_index, const at::Tensor & total_weight);
TORCH_API at::Tensor & nll_loss2d_backward_out_cpu(const at::Tensor & grad_output, const at::Tensor & self, const at::Tensor & target, const c10::optional<at::Tensor> & weight, int64_t reduction, int64_t ignore_index, const at::Tensor & total_weight, at::Tensor & grad_input);
TORCH_API at::Tensor nll_loss2d_backward_cuda(const at::Tensor & grad_output, const at::Tensor & self, const at::Tensor & target, const c10::optional<at::Tensor> & weight, int64_t reduction, int64_t ignore_index, const at::Tensor & total_weight);
TORCH_API at::Tensor & nll_loss2d_backward_out_cuda(const at::Tensor & grad_output, const at::Tensor & self, const at::Tensor & target, const c10::optional<at::Tensor> & weight, int64_t reduction, int64_t ignore_index, const at::Tensor & total_weight, at::Tensor & grad_input);
TORCH_API at::Tensor smooth_l1_loss(const at::Tensor & self, const at::Tensor & target, int64_t reduction=at::Reduction::Mean, double beta=1.0);
TORCH_API at::Tensor & smooth_l1_loss_out(const at::Tensor & self, const at::Tensor & target, int64_t reduction, double beta, at::Tensor & out);
TORCH_API at::Tensor smooth_l1_loss_backward(const at::Tensor & grad_output, const at::Tensor & self, const at::Tensor & target, int64_t reduction, double beta);
TORCH_API at::Tensor & smooth_l1_loss_backward_out(const at::Tensor & grad_output, const at::Tensor & self, const at::Tensor & target, int64_t reduction, double beta, at::Tensor & grad_input);
TORCH_API at::Tensor huber_loss(const at::Tensor & self, const at::Tensor & target, int64_t reduction=at::Reduction::Mean, double delta=1.0);
TORCH_API at::Tensor & huber_loss_out(const at::Tensor & self, const at::Tensor & target, int64_t reduction, double delta, at::Tensor & out);
TORCH_API at::Tensor huber_loss_backward(const at::Tensor & grad_output, const at::Tensor & self, const at::Tensor & target, int64_t reduction, double delta);
TORCH_API at::Tensor & huber_loss_backward_out(const at::Tensor & grad_output, const at::Tensor & self, const at::Tensor & target, int64_t reduction, double delta, at::Tensor & grad_input);
TORCH_API at::Tensor soft_margin_loss(const at::Tensor & self, const at::Tensor & target, int64_t reduction=at::Reduction::Mean);
TORCH_API at::Tensor & soft_margin_loss_out(const at::Tensor & self, const at::Tensor & target, int64_t reduction, at::Tensor & out);
TORCH_API at::Tensor soft_margin_loss_backward(const at::Tensor & grad_output, const at::Tensor & self, const at::Tensor & target, int64_t reduction);
TORCH_API at::Tensor & soft_margin_loss_backward_out(const at::Tensor & grad_output, const at::Tensor & self, const at::Tensor & target, int64_t reduction, at::Tensor & grad_input);
TORCH_API at::Tensor & elu_(at::Tensor & self, const at::Scalar & alpha=1, const at::Scalar & scale=1, const at::Scalar & input_scale=1);
struct TORCH_API structured_elu_out : public at::meta::structured_elu {
void impl(const at::Tensor & self, const at::Scalar & alpha, const at::Scalar & scale, const at::Scalar & input_scale, const at::Tensor & out);
};
struct TORCH_API structured_elu_backward_out : public at::meta::structured_elu_backward {
void impl(const at::Tensor & grad_output, const at::Scalar & alpha, const at::Scalar & scale, const at::Scalar & input_scale, bool is_result, const at::Tensor & self_or_result, const at::Tensor & grad_input);
};
struct TORCH_API structured_glu_out : public at::meta::structured_glu {
void impl(const at::Tensor & self, int64_t dim, const at::Tensor & out);
};
TORCH_API at::Tensor glu_backward_cpu(const at::Tensor & grad_output, const at::Tensor & self, int64_t dim);
TORCH_API at::Tensor & glu_backward_cpu_out(const at::Tensor & grad_output, const at::Tensor & self, int64_t dim, at::Tensor & grad_input);
TORCH_API at::Tensor glu_backward_cuda(const at::Tensor & grad_output, const at::Tensor & self, int64_t dim);
TORCH_API at::Tensor & glu_backward_cuda_out(const at::Tensor & grad_output, const at::Tensor & self, int64_t dim, at::Tensor & grad_input);
struct TORCH_API structured_hardsigmoid_out : public at::meta::structured_hardsigmoid {
void impl(const at::Tensor & self, const at::Tensor & out);
};
TORCH_API at::Tensor hardsigmoid_quantized_cpu(const at::Tensor & self);
struct TORCH_API structured_hardsigmoid_backward_out : public at::meta::structured_hardsigmoid_backward {
void impl(const at::Tensor & grad_output, const at::Tensor & self, const at::Tensor & grad_input);
};
TORCH_API at::Tensor hardtanh(const at::Tensor & self, const at::Scalar & min_val=-1, const at::Scalar & max_val=1);
TORCH_API at::Tensor & hardtanh_out(const at::Tensor & self, const at::Scalar & min_val, const at::Scalar & max_val, at::Tensor & out);
TORCH_API at::Tensor & hardtanh_(at::Tensor & self, const at::Scalar & min_val=-1, const at::Scalar & max_val=1);
TORCH_API at::Tensor hardtanh_quantized_cpu(const at::Tensor & self, const at::Scalar & min_val=-1, const at::Scalar & max_val=1);
TORCH_API at::Tensor & hardtanh_out_quantized_cpu(const at::Tensor & self, const at::Scalar & min_val, const at::Scalar & max_val, at::Tensor & out);
TORCH_API at::Tensor & hardtanh_quantized_cpu_(at::Tensor & self, const at::Scalar & min_val=-1, const at::Scalar & max_val=1);
TORCH_API at::Tensor hardtanh_backward(const at::Tensor & grad_output, const at::Tensor & self, const at::Scalar & min_val, const at::Scalar & max_val);
TORCH_API at::Tensor & hardtanh_backward_out(const at::Tensor & grad_output, const at::Tensor & self, const at::Scalar & min_val, const at::Scalar & max_val, at::Tensor & grad_input);
TORCH_API at::Tensor hardswish(const at::Tensor & self);
TORCH_API at::Tensor & hardswish_out(const at::Tensor & self, at::Tensor & out);
TORCH_API at::Tensor & hardswish_(at::Tensor & self);
TORCH_API at::Tensor hardswish_backward(const at::Tensor & grad_output, const at::Tensor & self);
struct TORCH_API structured_leaky_relu_out : public at::meta::structured_leaky_relu {
void impl(const at::Tensor & self, const at::Scalar & negative_slope, const at::Tensor & out);
};
TORCH_API at::Tensor leaky_relu_quantized_cpu(const at::Tensor & self, const at::Scalar & negative_slope=0.01);
TORCH_API at::Tensor & leaky_relu_out_quantized_cpu(const at::Tensor & self, const at::Scalar & negative_slope, at::Tensor & out);
TORCH_API at::Tensor & leaky_relu_quantized_cpu_(at::Tensor & self, const at::Scalar & negative_slope=0.01);
struct TORCH_API structured_leaky_relu_backward_out : public at::meta::structured_leaky_relu_backward {
void impl(const at::Tensor & grad_output, const at::Tensor & self, const at::Scalar & negative_slope, bool self_is_result, const at::Tensor & grad_input);
};
TORCH_API at::Tensor log_sigmoid(const at::Tensor & self);
TORCH_API at::Tensor & log_sigmoid_out(const at::Tensor & self, at::Tensor & out);
TORCH_API ::std::tuple<at::Tensor,at::Tensor> log_sigmoid_forward_cpu(const at::Tensor & self);
TORCH_API ::std::tuple<at::Tensor &,at::Tensor &> log_sigmoid_forward_out_cpu(const at::Tensor & self, at::Tensor & output, at::Tensor & buffer);
TORCH_API ::std::tuple<at::Tensor,at::Tensor> log_sigmoid_forward_cuda(const at::Tensor & self);
TORCH_API ::std::tuple<at::Tensor &,at::Tensor &> log_sigmoid_forward_out_cuda(const at::Tensor & self, at::Tensor & output, at::Tensor & buffer);
TORCH_API at::Tensor log_sigmoid_backward_cpu(const at::Tensor & grad_output, const at::Tensor & self, const at::Tensor & buffer);
TORCH_API at::Tensor & log_sigmoid_backward_cpu_out(const at::Tensor & grad_output, const at::Tensor & self, const at::Tensor & buffer, at::Tensor & grad_input);
TORCH_API at::Tensor log_sigmoid_backward_cuda(const at::Tensor & grad_output, const at::Tensor & self, const at::Tensor & buffer);
TORCH_API at::Tensor & log_sigmoid_backward_cuda_out(const at::Tensor & grad_output, const at::Tensor & self, const at::Tensor & buffer, at::Tensor & grad_input);
TORCH_API at::Tensor rrelu_with_noise_cpu(const at::Tensor & self, const at::Tensor & noise, const at::Scalar & lower=0.125, const at::Scalar & upper=0.3333333333333333, bool training=false, c10::optional<at::Generator> generator=c10::nullopt);
TORCH_API at::Tensor & rrelu_with_noise_out_cpu(const at::Tensor & self, const at::Tensor & noise, const at::Scalar & lower, const at::Scalar & upper, bool training, c10::optional<at::Generator> generator, at::Tensor & out);
TORCH_API at::Tensor & rrelu_with_noise_cpu_(at::Tensor & self, const at::Tensor & noise, const at::Scalar & lower=0.125, const at::Scalar & upper=0.3333333333333333, bool training=false, c10::optional<at::Generator> generator=c10::nullopt);
TORCH_API at::Tensor rrelu_with_noise_cuda(const at::Tensor & self, const at::Tensor & noise, const at::Scalar & lower=0.125, const at::Scalar & upper=0.3333333333333333, bool training=false, c10::optional<at::Generator> generator=c10::nullopt);
TORCH_API at::Tensor & rrelu_with_noise_out_cuda(const at::Tensor & self, const at::Tensor & noise, const at::Scalar & lower, const at::Scalar & upper, bool training, c10::optional<at::Generator> generator, at::Tensor & out);
TORCH_API at::Tensor & rrelu_with_noise_cuda_(at::Tensor & self, const at::Tensor & noise, const at::Scalar & lower=0.125, const at::Scalar & upper=0.3333333333333333, bool training=false, c10::optional<at::Generator> generator=c10::nullopt);
TORCH_API at::Tensor rrelu_with_noise_backward(const at::Tensor & grad_output, const at::Tensor & self, const at::Tensor & noise, const at::Scalar & lower, const at::Scalar & upper, bool training, bool self_is_result);
struct TORCH_API structured_softplus_out : public at::meta::structured_softplus {
void impl(const at::Tensor & self, const at::Scalar & beta, const at::Scalar & threshold, const at::Tensor & out);
};
struct TORCH_API structured_softplus_backward_out : public at::meta::structured_softplus_backward {
void impl(const at::Tensor & grad_output, const at::Tensor & self, const at::Scalar & beta, const at::Scalar & threshold, const at::Tensor & output, const at::Tensor & grad_input);
};
struct TORCH_API structured_softshrink_out : public at::meta::structured_softshrink {
void impl(const at::Tensor & self, const at::Scalar & lambd, const at::Tensor & out);
};
struct TORCH_API structured_softshrink_backward_out : public at::meta::structured_softshrink_backward {
void impl(const at::Tensor & grad_output, const at::Tensor & self, const at::Scalar & lambd, const at::Tensor & grad_input);
};
TORCH_API at::Tensor adaptive_avg_pool2d(const at::Tensor & self, at::IntArrayRef output_size);
TORCH_API at::Tensor & adaptive_avg_pool2d_out_cpu(const at::Tensor & self, at::IntArrayRef output_size, at::Tensor & out);
TORCH_API at::Tensor & adaptive_avg_pool2d_out_cuda(const at::Tensor & self, at::IntArrayRef output_size, at::Tensor & out);
TORCH_API at::Tensor & mkldnn_adaptive_avg_pool2d_out(const at::Tensor & self, at::IntArrayRef output_size, at::Tensor & out);
TORCH_API at::Tensor mkldnn_adaptive_avg_pool2d(const at::Tensor & self, at::IntArrayRef output_size);
TORCH_API at::Tensor mkldnn_adaptive_avg_pool2d_backward(const at::Tensor & grad_output, const at::Tensor & self);
TORCH_API at::Tensor adaptive_avg_pool2d_cpu(const at::Tensor & self, at::IntArrayRef output_size);
TORCH_API at::Tensor adaptive_avg_pool2d_cuda(const at::Tensor & self, at::IntArrayRef output_size);
TORCH_API at::Tensor adaptive_avg_pool2d_quantized_cpu(const at::Tensor & self, at::IntArrayRef output_size);
TORCH_API at::Tensor adaptive_avg_pool2d_backward_cpu(const at::Tensor & grad_output, const at::Tensor & self);
TORCH_API at::Tensor adaptive_avg_pool2d_backward_cuda(const at::Tensor & grad_output, const at::Tensor & self);
TORCH_API at::Tensor adaptive_avg_pool3d(const at::Tensor & self, at::IntArrayRef output_size);
TORCH_API at::Tensor & adaptive_avg_pool3d_out_cpu(const at::Tensor & self, at::IntArrayRef output_size, at::Tensor & out);
TORCH_API at::Tensor & adaptive_avg_pool3d_out_cuda(const at::Tensor & self, at::IntArrayRef output_size, at::Tensor & out);
TORCH_API at::Tensor & adaptive_avg_pool3d_out_quantized_cpu(const at::Tensor & self, at::IntArrayRef output_size, at::Tensor & out);
TORCH_API at::Tensor adaptive_avg_pool3d_cpu(const at::Tensor & self, at::IntArrayRef output_size);
TORCH_API at::Tensor adaptive_avg_pool3d_cuda(const at::Tensor & self, at::IntArrayRef output_size);
TORCH_API at::Tensor adaptive_avg_pool3d_quantized_cpu(const at::Tensor & self, at::IntArrayRef output_size);
TORCH_API at::Tensor & adaptive_avg_pool3d_backward_out_cpu(const at::Tensor & grad_output, const at::Tensor & self, at::Tensor & grad_input);
TORCH_API at::Tensor & adaptive_avg_pool3d_backward_out_cuda(const at::Tensor & grad_output, const at::Tensor & self, at::Tensor & grad_input);
TORCH_API at::Tensor adaptive_avg_pool3d_backward_cpu(const at::Tensor & grad_output, const at::Tensor & self);
TORCH_API at::Tensor adaptive_avg_pool3d_backward_cuda(const at::Tensor & grad_output, const at::Tensor & self);
struct TORCH_API structured_adaptive_max_pool2d_out_cpu : public at::meta::structured_adaptive_max_pool2d {
void impl(const at::Tensor & self, at::IntArrayRef output_size, const at::Tensor & out, const at::Tensor & indices);
};
struct TORCH_API structured_adaptive_max_pool2d_out_cuda : public at::meta::structured_adaptive_max_pool2d {
void impl(const at::Tensor & self, at::IntArrayRef output_size, const at::Tensor & out, const at::Tensor & indices);
};
struct TORCH_API structured_adaptive_max_pool2d_backward_out_cpu : public at::meta::structured_adaptive_max_pool2d_backward {
void impl(const at::Tensor & grad_output, const at::Tensor & self, const at::Tensor & indices, const at::Tensor & grad_input);
};
struct TORCH_API structured_adaptive_max_pool2d_backward_out_cuda : public at::meta::structured_adaptive_max_pool2d_backward {
void impl(const at::Tensor & grad_output, const at::Tensor & self, const at::Tensor & indices, const at::Tensor & grad_input);
};
struct TORCH_API structured_adaptive_max_pool3d_out_cpu : public at::meta::structured_adaptive_max_pool3d {
void impl(const at::Tensor & self, at::IntArrayRef output_size, const at::Tensor & out, const at::Tensor & indices);
};
struct TORCH_API structured_adaptive_max_pool3d_out_cuda : public at::meta::structured_adaptive_max_pool3d {
void impl(const at::Tensor & self, at::IntArrayRef output_size, const at::Tensor & out, const at::Tensor & indices);
};
struct TORCH_API structured_adaptive_max_pool3d_backward_out_cpu : public at::meta::structured_adaptive_max_pool3d_backward {
void impl(const at::Tensor & grad_output, const at::Tensor & self, const at::Tensor & indices, const at::Tensor & grad_input);
};
struct TORCH_API structured_adaptive_max_pool3d_backward_out_cuda : public at::meta::structured_adaptive_max_pool3d_backward {
void impl(const at::Tensor & grad_output, const at::Tensor & self, const at::Tensor & indices, const at::Tensor & grad_input);
};
struct TORCH_API structured_avg_pool2d_out_cpu : public at::meta::structured_avg_pool2d {
void impl(const at::Tensor & self, int64_t kH, int64_t kW, int64_t dH, int64_t dW, int64_t padH, int64_t padW, bool ceil_mode, bool count_include_pad, c10::optional<int64_t> divisor_override, const at::Tensor & out);
};
struct TORCH_API structured_avg_pool2d_out_cuda : public at::meta::structured_avg_pool2d {
void impl(const at::Tensor & self, int64_t kH, int64_t kW, int64_t dH, int64_t dW, int64_t padH, int64_t padW, bool ceil_mode, bool count_include_pad, c10::optional<int64_t> divisor_override, const at::Tensor & out);
};
TORCH_API at::Tensor mkldnn_avg_pool2d(const at::Tensor & self, at::IntArrayRef kernel_size, at::IntArrayRef stride={}, at::IntArrayRef padding=0, bool ceil_mode=false, bool count_include_pad=true, c10::optional<int64_t> divisor_override=c10::nullopt);
TORCH_API at::Tensor & mkldnn_avg_pool2d_out(const at::Tensor & self, at::IntArrayRef kernel_size, at::IntArrayRef stride, at::IntArrayRef padding, bool ceil_mode, bool count_include_pad, c10::optional<int64_t> divisor_override, at::Tensor & out);
TORCH_API at::Tensor avg_pool2d_quantized_cpu(const at::Tensor & self, at::IntArrayRef kernel_size, at::IntArrayRef stride={}, at::IntArrayRef padding=0, bool ceil_mode=false, bool count_include_pad=true, c10::optional<int64_t> divisor_override=c10::nullopt);
struct TORCH_API structured_avg_pool2d_backward_out_cpu : public at::meta::structured_avg_pool2d_backward {
void impl(const at::Tensor & grad_output, const at::Tensor & self, at::IntArrayRef kernel_size, at::IntArrayRef stride, at::IntArrayRef padding, bool ceil_mode, bool count_include_pad, c10::optional<int64_t> divisor_override, const at::Tensor & grad_input);
};
struct TORCH_API structured_avg_pool2d_backward_out_cuda : public at::meta::structured_avg_pool2d_backward {
void impl(const at::Tensor & grad_output, const at::Tensor & self, at::IntArrayRef kernel_size, at::IntArrayRef stride, at::IntArrayRef padding, bool ceil_mode, bool count_include_pad, c10::optional<int64_t> divisor_override, const at::Tensor & grad_input);
};
TORCH_API at::Tensor mkldnn_avg_pool2d_backward(const at::Tensor & grad_output, const at::Tensor & self, at::IntArrayRef kernel_size, at::IntArrayRef stride, at::IntArrayRef padding, bool ceil_mode, bool count_include_pad, c10::optional<int64_t> divisor_override);
TORCH_API at::Tensor & mkldnn_avg_pool2d_backward_out(const at::Tensor & grad_output, const at::Tensor & self, at::IntArrayRef kernel_size, at::IntArrayRef stride, at::IntArrayRef padding, bool ceil_mode, bool count_include_pad, c10::optional<int64_t> divisor_override, at::Tensor & grad_input);
struct TORCH_API structured_avg_pool3d_out_cpu : public at::meta::structured_avg_pool3d {
void impl(const at::Tensor & self, at::IntArrayRef kernel_size, at::IntArrayRef stride, at::IntArrayRef padding, bool ceil_mode, bool count_include_pad, c10::optional<int64_t> divisor_override, const at::Tensor & out);
};
struct TORCH_API structured_avg_pool3d_out_cuda : public at::meta::structured_avg_pool3d {
void impl(const at::Tensor & self, at::IntArrayRef kernel_size, at::IntArrayRef stride, at::IntArrayRef padding, bool ceil_mode, bool count_include_pad, c10::optional<int64_t> divisor_override, const at::Tensor & out);
};
TORCH_API at::Tensor mkldnn_avg_pool3d(const at::Tensor & self, at::IntArrayRef kernel_size, at::IntArrayRef stride={}, at::IntArrayRef padding=0, bool ceil_mode=false, bool count_include_pad=true, c10::optional<int64_t> divisor_override=c10::nullopt);
TORCH_API at::Tensor & mkldnn_avg_pool3d_out(const at::Tensor & self, at::IntArrayRef kernel_size, at::IntArrayRef stride, at::IntArrayRef padding, bool ceil_mode, bool count_include_pad, c10::optional<int64_t> divisor_override, at::Tensor & out);
TORCH_API at::Tensor avg_pool3d_quantized_cpu(const at::Tensor & self, at::IntArrayRef kernel_size, at::IntArrayRef stride={}, at::IntArrayRef padding=0, bool ceil_mode=false, bool count_include_pad=true, c10::optional<int64_t> divisor_override=c10::nullopt);
struct TORCH_API structured_avg_pool3d_backward_out_cpu : public at::meta::structured_avg_pool3d_backward {
void impl(const at::Tensor & grad_output, const at::Tensor & self, at::IntArrayRef kernel_size, at::IntArrayRef stride, at::IntArrayRef padding, bool ceil_mode, bool count_include_pad, c10::optional<int64_t> divisor_override, const at::Tensor & grad_input);
};
struct TORCH_API structured_avg_pool3d_backward_out_cuda : public at::meta::structured_avg_pool3d_backward {
void impl(const at::Tensor & grad_output, const at::Tensor & self, at::IntArrayRef kernel_size, at::IntArrayRef stride, at::IntArrayRef padding, bool ceil_mode, bool count_include_pad, c10::optional<int64_t> divisor_override, const at::Tensor & grad_input);
};
TORCH_API at::Tensor mkldnn_avg_pool3d_backward(const at::Tensor & grad_output, const at::Tensor & self, at::IntArrayRef kernel_size, at::IntArrayRef stride, at::IntArrayRef padding, bool ceil_mode, bool count_include_pad, c10::optional<int64_t> divisor_override);
TORCH_API at::Tensor & mkldnn_avg_pool3d_backward_out(const at::Tensor & grad_output, const at::Tensor & self, at::IntArrayRef kernel_size, at::IntArrayRef stride, at::IntArrayRef padding, bool ceil_mode, bool count_include_pad, c10::optional<int64_t> divisor_override, at::Tensor & grad_input);
struct TORCH_API structured_fractional_max_pool2d_out_cpu : public at::meta::structured_fractional_max_pool2d {
void impl(const at::Tensor & self, at::IntArrayRef kernel_size, at::IntArrayRef output_size, const at::Tensor & random_samples, const at::Tensor & output, const at::Tensor & indices);
};
struct TORCH_API structured_fractional_max_pool2d_out_cuda : public at::meta::structured_fractional_max_pool2d {
void impl(const at::Tensor & self, at::IntArrayRef kernel_size, at::IntArrayRef output_size, const at::Tensor & random_samples, const at::Tensor & output, const at::Tensor & indices);
};
TORCH_API at::Tensor fractional_max_pool2d_backward_cpu(const at::Tensor & grad_output, const at::Tensor & self, at::IntArrayRef kernel_size, at::IntArrayRef output_size, const at::Tensor & indices);
TORCH_API at::Tensor & fractional_max_pool2d_backward_out_cpu(const at::Tensor & grad_output, const at::Tensor & self, at::IntArrayRef kernel_size, at::IntArrayRef output_size, const at::Tensor & indices, at::Tensor & grad_input);
TORCH_API at::Tensor fractional_max_pool2d_backward_cuda(const at::Tensor & grad_output, const at::Tensor & self, at::IntArrayRef kernel_size, at::IntArrayRef output_size, const at::Tensor & indices);
TORCH_API at::Tensor & fractional_max_pool2d_backward_out_cuda(const at::Tensor & grad_output, const at::Tensor & self, at::IntArrayRef kernel_size, at::IntArrayRef output_size, const at::Tensor & indices, at::Tensor & grad_input);
TORCH_API ::std::tuple<at::Tensor,at::Tensor> fractional_max_pool3d_cpu(const at::Tensor & self, at::IntArrayRef kernel_size, at::IntArrayRef output_size, const at::Tensor & random_samples);
TORCH_API ::std::tuple<at::Tensor &,at::Tensor &> fractional_max_pool3d_out_cpu(const at::Tensor & self, at::IntArrayRef kernel_size, at::IntArrayRef output_size, const at::Tensor & random_samples, at::Tensor & output, at::Tensor & indices);
TORCH_API ::std::tuple<at::Tensor,at::Tensor> fractional_max_pool3d_cuda(const at::Tensor & self, at::IntArrayRef kernel_size, at::IntArrayRef output_size, const at::Tensor & random_samples);
TORCH_API ::std::tuple<at::Tensor &,at::Tensor &> fractional_max_pool3d_out_cuda(const at::Tensor & self, at::IntArrayRef kernel_size, at::IntArrayRef output_size, const at::Tensor & random_samples, at::Tensor & output, at::Tensor & indices);
TORCH_API at::Tensor fractional_max_pool3d_backward_cpu(const at::Tensor & grad_output, const at::Tensor & self, at::IntArrayRef kernel_size, at::IntArrayRef output_size, const at::Tensor & indices);
TORCH_API at::Tensor & fractional_max_pool3d_backward_out_cpu(const at::Tensor & grad_output, const at::Tensor & self, at::IntArrayRef kernel_size, at::IntArrayRef output_size, const at::Tensor & indices, at::Tensor & grad_input);
TORCH_API at::Tensor fractional_max_pool3d_backward_cuda(const at::Tensor & grad_output, const at::Tensor & self, at::IntArrayRef kernel_size, at::IntArrayRef output_size, const at::Tensor & indices);
TORCH_API at::Tensor & fractional_max_pool3d_backward_out_cuda(const at::Tensor & grad_output, const at::Tensor & self, at::IntArrayRef kernel_size, at::IntArrayRef output_size, const at::Tensor & indices, at::Tensor & grad_input);
struct TORCH_API structured_max_pool2d_with_indices_out_cpu : public at::meta::structured_max_pool2d_with_indices {
void impl(const at::Tensor & self, at::IntArrayRef kernel_size, at::IntArrayRef stride, at::IntArrayRef padding, at::IntArrayRef dilation, bool ceil_mode, const at::Tensor & out, const at::Tensor & indices);
};
struct TORCH_API structured_max_pool2d_with_indices_out_cuda : public at::meta::structured_max_pool2d_with_indices {
void impl(const at::Tensor & self, at::IntArrayRef kernel_size, at::IntArrayRef stride, at::IntArrayRef padding, at::IntArrayRef dilation, bool ceil_mode, const at::Tensor & out, const at::Tensor & indices);
};
struct TORCH_API structured_max_pool2d_with_indices_backward_out_cpu : public at::meta::structured_max_pool2d_with_indices_backward {
void impl(const at::Tensor & grad_output, const at::Tensor & self, at::IntArrayRef kernel_size, at::IntArrayRef stride, at::IntArrayRef padding, at::IntArrayRef dilation, bool ceil_mode, const at::Tensor & indices, const at::Tensor & grad_input);
};
struct TORCH_API structured_max_pool2d_with_indices_backward_out_cuda : public at::meta::structured_max_pool2d_with_indices_backward {
void impl(const at::Tensor & grad_output, const at::Tensor & self, at::IntArrayRef kernel_size, at::IntArrayRef stride, at::IntArrayRef padding, at::IntArrayRef dilation, bool ceil_mode, const at::Tensor & indices, const at::Tensor & grad_input);
};
TORCH_API ::std::tuple<at::Tensor,at::Tensor> max_pool3d_with_indices_cpu(const at::Tensor & self, at::IntArrayRef kernel_size, at::IntArrayRef stride={}, at::IntArrayRef padding=0, at::IntArrayRef dilation=1, bool ceil_mode=false);
TORCH_API ::std::tuple<at::Tensor &,at::Tensor &> max_pool3d_with_indices_out_cpu(const at::Tensor & self, at::IntArrayRef kernel_size, at::IntArrayRef stride, at::IntArrayRef padding, at::IntArrayRef dilation, bool ceil_mode, at::Tensor & out, at::Tensor & indices);
TORCH_API ::std::tuple<at::Tensor,at::Tensor> max_pool3d_with_indices_cuda(const at::Tensor & self, at::IntArrayRef kernel_size, at::IntArrayRef stride={}, at::IntArrayRef padding=0, at::IntArrayRef dilation=1, bool ceil_mode=false);
TORCH_API ::std::tuple<at::Tensor &,at::Tensor &> max_pool3d_with_indices_out_cuda(const at::Tensor & self, at::IntArrayRef kernel_size, at::IntArrayRef stride, at::IntArrayRef padding, at::IntArrayRef dilation, bool ceil_mode, at::Tensor & out, at::Tensor & indices);
TORCH_API at::Tensor max_pool3d_with_indices_backward_cpu(const at::Tensor & grad_output, const at::Tensor & self, at::IntArrayRef kernel_size, at::IntArrayRef stride, at::IntArrayRef padding, at::IntArrayRef dilation, bool ceil_mode, const at::Tensor & indices);
TORCH_API at::Tensor & max_pool3d_with_indices_backward_out_cpu(const at::Tensor & grad_output, const at::Tensor & self, at::IntArrayRef kernel_size, at::IntArrayRef stride, at::IntArrayRef padding, at::IntArrayRef dilation, bool ceil_mode, const at::Tensor & indices, at::Tensor & grad_input);
TORCH_API at::Tensor max_pool3d_with_indices_backward_cuda(const at::Tensor & grad_output, const at::Tensor & self, at::IntArrayRef kernel_size, at::IntArrayRef stride, at::IntArrayRef padding, at::IntArrayRef dilation, bool ceil_mode, const at::Tensor & indices);
TORCH_API at::Tensor & max_pool3d_with_indices_backward_out_cuda(const at::Tensor & grad_output, const at::Tensor & self, at::IntArrayRef kernel_size, at::IntArrayRef stride, at::IntArrayRef padding, at::IntArrayRef dilation, bool ceil_mode, const at::Tensor & indices, at::Tensor & grad_input);
TORCH_API at::Tensor max_unpooling2d_forward_cpu(const at::Tensor & self, const at::Tensor & indices, at::IntArrayRef output_size);
TORCH_API at::Tensor & max_unpooling2d_forward_out_cpu(const at::Tensor & self, const at::Tensor & indices, at::IntArrayRef output_size, at::Tensor & out);
TORCH_API at::Tensor max_unpooling2d_forward_cuda(const at::Tensor & self, const at::Tensor & indices, at::IntArrayRef output_size);
TORCH_API at::Tensor & max_unpooling2d_forward_out_cuda(const at::Tensor & self, const at::Tensor & indices, at::IntArrayRef output_size, at::Tensor & out);
TORCH_API at::Tensor max_unpooling2d_backward_cpu(const at::Tensor & grad_output, const at::Tensor & self, const at::Tensor & indices, at::IntArrayRef output_size);
TORCH_API at::Tensor & max_unpooling2d_backward_out_cpu(const at::Tensor & grad_output, const at::Tensor & self, const at::Tensor & indices, at::IntArrayRef output_size, at::Tensor & grad_input);
TORCH_API at::Tensor max_unpooling2d_backward_cuda(const at::Tensor & grad_output, const at::Tensor & self, const at::Tensor & indices, at::IntArrayRef output_size);
TORCH_API at::Tensor & max_unpooling2d_backward_out_cuda(const at::Tensor & grad_output, const at::Tensor & self, const at::Tensor & indices, at::IntArrayRef output_size, at::Tensor & grad_input);
TORCH_API at::Tensor max_unpooling3d_forward_cpu(const at::Tensor & self, const at::Tensor & indices, at::IntArrayRef output_size, at::IntArrayRef stride, at::IntArrayRef padding);
TORCH_API at::Tensor & max_unpooling3d_forward_out_cpu(const at::Tensor & self, const at::Tensor & indices, at::IntArrayRef output_size, at::IntArrayRef stride, at::IntArrayRef padding, at::Tensor & out);
TORCH_API at::Tensor max_unpooling3d_forward_cuda(const at::Tensor & self, const at::Tensor & indices, at::IntArrayRef output_size, at::IntArrayRef stride, at::IntArrayRef padding);
TORCH_API at::Tensor & max_unpooling3d_forward_out_cuda(const at::Tensor & self, const at::Tensor & indices, at::IntArrayRef output_size, at::IntArrayRef stride, at::IntArrayRef padding, at::Tensor & out);
TORCH_API at::Tensor max_unpooling3d_backward_cpu(const at::Tensor & grad_output, const at::Tensor & self, const at::Tensor & indices, at::IntArrayRef output_size, at::IntArrayRef stride, at::IntArrayRef padding);
TORCH_API at::Tensor & max_unpooling3d_backward_out_cpu(const at::Tensor & grad_output, const at::Tensor & self, const at::Tensor & indices, at::IntArrayRef output_size, at::IntArrayRef stride, at::IntArrayRef padding, at::Tensor & grad_input);
TORCH_API at::Tensor max_unpooling3d_backward_cuda(const at::Tensor & grad_output, const at::Tensor & self, const at::Tensor & indices, at::IntArrayRef output_size, at::IntArrayRef stride, at::IntArrayRef padding);
TORCH_API at::Tensor & max_unpooling3d_backward_out_cuda(const at::Tensor & grad_output, const at::Tensor & self, const at::Tensor & indices, at::IntArrayRef output_size, at::IntArrayRef stride, at::IntArrayRef padding, at::Tensor & grad_input);
struct TORCH_API structured_reflection_pad1d_out_cpu : public at::meta::structured_reflection_pad1d {
void impl(const at::Tensor & self, at::IntArrayRef padding, const at::Tensor & out);
};
struct TORCH_API structured_reflection_pad1d_out_cuda : public at::meta::structured_reflection_pad1d {
void impl(const at::Tensor & self, at::IntArrayRef padding, const at::Tensor & out);
};
TORCH_API at::Tensor reflection_pad1d_cpu(const at::Tensor & self, at::IntArrayRef padding);
TORCH_API at::Tensor & reflection_pad1d_out_cpu(const at::Tensor & self, at::IntArrayRef padding, at::Tensor & out);
struct TORCH_API structured_reflection_pad1d_backward_out_cpu : public at::meta::structured_reflection_pad1d_backward {
void impl(const at::Tensor & grad_output, const at::Tensor & self, at::IntArrayRef padding, const at::Tensor & grad_input);
};
struct TORCH_API structured_reflection_pad1d_backward_out_cuda : public at::meta::structured_reflection_pad1d_backward {
void impl(const at::Tensor & grad_output, const at::Tensor & self, at::IntArrayRef padding, const at::Tensor & grad_input);
};
TORCH_API at::Tensor reflection_pad2d_cpu(const at::Tensor & self, at::IntArrayRef padding);
TORCH_API at::Tensor & reflection_pad2d_out_cpu(const at::Tensor & self, at::IntArrayRef padding, at::Tensor & out);
TORCH_API at::Tensor reflection_pad2d_cuda(const at::Tensor & self, at::IntArrayRef padding);
TORCH_API at::Tensor & reflection_pad2d_out_cuda(const at::Tensor & self, at::IntArrayRef padding, at::Tensor & out);
TORCH_API at::Tensor reflection_pad2d_backward_cpu(const at::Tensor & grad_output, const at::Tensor & self, at::IntArrayRef padding);
TORCH_API at::Tensor & reflection_pad2d_backward_out_cpu(const at::Tensor & grad_output, const at::Tensor & self, at::IntArrayRef padding, at::Tensor & grad_input);
TORCH_API at::Tensor reflection_pad2d_backward_cuda(const at::Tensor & grad_output, const at::Tensor & self, at::IntArrayRef padding);
TORCH_API at::Tensor & reflection_pad2d_backward_out_cuda(const at::Tensor & grad_output, const at::Tensor & self, at::IntArrayRef padding, at::Tensor & grad_input);
struct TORCH_API structured_reflection_pad3d_out_cpu : public at::meta::structured_reflection_pad3d {
void impl(const at::Tensor & self, at::IntArrayRef padding, const at::Tensor & out);
};
struct TORCH_API structured_reflection_pad3d_out_cuda : public at::meta::structured_reflection_pad3d {
void impl(const at::Tensor & self, at::IntArrayRef padding, const at::Tensor & out);
};
struct TORCH_API structured_reflection_pad3d_backward_out_cpu : public at::meta::structured_reflection_pad3d_backward {
void impl(const at::Tensor & grad_output, const at::Tensor & self, at::IntArrayRef padding, const at::Tensor & grad_input);
};
struct TORCH_API structured_reflection_pad3d_backward_out_cuda : public at::meta::structured_reflection_pad3d_backward {
void impl(const at::Tensor & grad_output, const at::Tensor & self, at::IntArrayRef padding, const at::Tensor & grad_input);
};
struct TORCH_API structured_replication_pad1d_out_cpu : public at::meta::structured_replication_pad1d {
void impl(const at::Tensor & self, at::IntArrayRef padding, const at::Tensor & out);
};
struct TORCH_API structured_replication_pad1d_out_cuda : public at::meta::structured_replication_pad1d {
void impl(const at::Tensor & self, at::IntArrayRef padding, const at::Tensor & out);
};
struct TORCH_API structured_replication_pad1d_backward_out_cpu : public at::meta::structured_replication_pad1d_backward {
void impl(const at::Tensor & grad_output, const at::Tensor & self, at::IntArrayRef padding, const at::Tensor & grad_input);
};
struct TORCH_API structured_replication_pad1d_backward_out_cuda : public at::meta::structured_replication_pad1d_backward {
void impl(const at::Tensor & grad_output, const at::Tensor & self, at::IntArrayRef padding, const at::Tensor & grad_input);
};
struct TORCH_API structured_replication_pad2d_out_cpu : public at::meta::structured_replication_pad2d {
void impl(const at::Tensor & self, at::IntArrayRef padding, const at::Tensor & out);
};
struct TORCH_API structured_replication_pad2d_out_cuda : public at::meta::structured_replication_pad2d {
void impl(const at::Tensor & self, at::IntArrayRef padding, const at::Tensor & out);
};
TORCH_API at::Tensor replication_pad2d_backward_cpu(const at::Tensor & grad_output, const at::Tensor & self, at::IntArrayRef padding);
TORCH_API at::Tensor & replication_pad2d_backward_out_cpu(const at::Tensor & grad_output, const at::Tensor & self, at::IntArrayRef padding, at::Tensor & grad_input);
TORCH_API at::Tensor replication_pad2d_backward_cuda(const at::Tensor & grad_output, const at::Tensor & self, at::IntArrayRef padding);
TORCH_API at::Tensor & replication_pad2d_backward_out_cuda(const at::Tensor & grad_output, const at::Tensor & self, at::IntArrayRef padding, at::Tensor & grad_input);
struct TORCH_API structured_replication_pad3d_out_cpu : public at::meta::structured_replication_pad3d {
void impl(const at::Tensor & self, at::IntArrayRef padding, const at::Tensor & out);
};
struct TORCH_API structured_replication_pad3d_out_cuda : public at::meta::structured_replication_pad3d {
void impl(const at::Tensor & self, at::IntArrayRef padding, const at::Tensor & out);
};
TORCH_API at::Tensor replication_pad3d_backward_cpu(const at::Tensor & grad_output, const at::Tensor & self, at::IntArrayRef padding);
TORCH_API at::Tensor & replication_pad3d_backward_out_cpu(const at::Tensor & grad_output, const at::Tensor & self, at::IntArrayRef padding, at::Tensor & grad_input);
TORCH_API at::Tensor replication_pad3d_backward_cuda(const at::Tensor & grad_output, const at::Tensor & self, at::IntArrayRef padding);
TORCH_API at::Tensor & replication_pad3d_backward_out_cuda(const at::Tensor & grad_output, const at::Tensor & self, at::IntArrayRef padding, at::Tensor & grad_input);
TORCH_API at::Tensor upsample_linear1d(const at::Tensor & input, c10::optional<at::IntArrayRef> output_size, bool align_corners, c10::optional<at::ArrayRef<double>> scale_factors);
TORCH_API at::Tensor upsample_linear1d_backward(const at::Tensor & grad_output, c10::optional<at::IntArrayRef> output_size, at::IntArrayRef input_size, bool align_corners, c10::optional<at::ArrayRef<double>> scale_factors);
TORCH_API at::Tensor upsample_bilinear2d(const at::Tensor & input, c10::optional<at::IntArrayRef> output_size, bool align_corners, c10::optional<at::ArrayRef<double>> scale_factors);
TORCH_API at::Tensor upsample_bilinear2d_backward(const at::Tensor & grad_output, c10::optional<at::IntArrayRef> output_size, at::IntArrayRef input_size, bool align_corners, c10::optional<at::ArrayRef<double>> scale_factors);
TORCH_API at::Tensor upsample_trilinear3d(const at::Tensor & input, c10::optional<at::IntArrayRef> output_size, bool align_corners, c10::optional<at::ArrayRef<double>> scale_factors);
TORCH_API at::Tensor upsample_trilinear3d_backward(const at::Tensor & grad_output, c10::optional<at::IntArrayRef> output_size, at::IntArrayRef input_size, bool align_corners, c10::optional<at::ArrayRef<double>> scale_factors);
TORCH_API at::Tensor upsample_bicubic2d(const at::Tensor & input, c10::optional<at::IntArrayRef> output_size, bool align_corners, c10::optional<at::ArrayRef<double>> scale_factors);
TORCH_API at::Tensor upsample_bicubic2d_backward(const at::Tensor & grad_output, c10::optional<at::IntArrayRef> output_size, at::IntArrayRef input_size, bool align_corners, c10::optional<at::ArrayRef<double>> scale_factors);
TORCH_API at::Tensor upsample_nearest1d(const at::Tensor & input, c10::optional<at::IntArrayRef> output_size, c10::optional<at::ArrayRef<double>> scale_factors);
TORCH_API at::Tensor upsample_nearest1d_backward(const at::Tensor & grad_output, c10::optional<at::IntArrayRef> output_size, at::IntArrayRef input_size, c10::optional<at::ArrayRef<double>> scale_factors);
TORCH_API at::Tensor upsample_nearest2d(const at::Tensor & input, c10::optional<at::IntArrayRef> output_size, c10::optional<at::ArrayRef<double>> scale_factors);
TORCH_API at::Tensor upsample_nearest2d_backward(const at::Tensor & grad_output, c10::optional<at::IntArrayRef> output_size, at::IntArrayRef input_size, c10::optional<at::ArrayRef<double>> scale_factors);
TORCH_API at::Tensor upsample_nearest3d_cpu(const at::Tensor & input, c10::optional<at::IntArrayRef> output_size, c10::optional<at::ArrayRef<double>> scale_factors);
TORCH_API at::Tensor upsample_nearest3d_cuda(const at::Tensor & input, c10::optional<at::IntArrayRef> output_size, c10::optional<at::ArrayRef<double>> scale_factors);
TORCH_API at::Tensor upsample_nearest3d_quantized_cpu(const at::Tensor & input, c10::optional<at::IntArrayRef> output_size, c10::optional<at::ArrayRef<double>> scale_factors);
TORCH_API at::Tensor upsample_nearest3d_backward_cpu(const at::Tensor & grad_output, c10::optional<at::IntArrayRef> output_size, at::IntArrayRef input_size, c10::optional<at::ArrayRef<double>> scale_factors);
TORCH_API at::Tensor upsample_nearest3d_backward_cuda(const at::Tensor & grad_output, c10::optional<at::IntArrayRef> output_size, at::IntArrayRef input_size, c10::optional<at::ArrayRef<double>> scale_factors);
struct TORCH_API structured_upsample_linear1d_out_cpu : public at::meta::structured_upsample_linear1d {
void impl(const at::Tensor & self, at::IntArrayRef output_size, bool align_corners, c10::optional<double> scales, const at::Tensor & out);
};
struct TORCH_API structured_upsample_linear1d_out_cuda : public at::meta::structured_upsample_linear1d {
void impl(const at::Tensor & self, at::IntArrayRef output_size, bool align_corners, c10::optional<double> scales, const at::Tensor & out);
};
struct TORCH_API structured_upsample_linear1d_backward_out_cpu : public at::meta::structured_upsample_linear1d_backward {
void impl(const at::Tensor & grad_output, at::IntArrayRef output_size, at::IntArrayRef input_size, bool align_corners, c10::optional<double> scales, const at::Tensor & grad_input);
};
struct TORCH_API structured_upsample_linear1d_backward_out_cuda : public at::meta::structured_upsample_linear1d_backward {
void impl(const at::Tensor & grad_output, at::IntArrayRef output_size, at::IntArrayRef input_size, bool align_corners, c10::optional<double> scales, const at::Tensor & grad_input);
};
struct TORCH_API structured_upsample_bilinear2d_out_cpu : public at::meta::structured_upsample_bilinear2d {
void impl(const at::Tensor & self, at::IntArrayRef output_size, bool align_corners, c10::optional<double> scales_h, c10::optional<double> scales_w, const at::Tensor & out);
};
struct TORCH_API structured_upsample_bilinear2d_out_cuda : public at::meta::structured_upsample_bilinear2d {
void impl(const at::Tensor & self, at::IntArrayRef output_size, bool align_corners, c10::optional<double> scales_h, c10::optional<double> scales_w, const at::Tensor & out);
};
TORCH_API at::Tensor upsample_bilinear2d_quantized_cpu(const at::Tensor & self, at::IntArrayRef output_size, bool align_corners, c10::optional<double> scales_h=c10::nullopt, c10::optional<double> scales_w=c10::nullopt);
struct TORCH_API structured_upsample_bilinear2d_backward_out_cpu : public at::meta::structured_upsample_bilinear2d_backward {
void impl(const at::Tensor & grad_output, at::IntArrayRef output_size, at::IntArrayRef input_size, bool align_corners, c10::optional<double> scales_h, c10::optional<double> scales_w, const at::Tensor & grad_input);
};
struct TORCH_API structured_upsample_bilinear2d_backward_out_cuda : public at::meta::structured_upsample_bilinear2d_backward {
void impl(const at::Tensor & grad_output, at::IntArrayRef output_size, at::IntArrayRef input_size, bool align_corners, c10::optional<double> scales_h, c10::optional<double> scales_w, const at::Tensor & grad_input);
};
struct TORCH_API structured_upsample_bicubic2d_out_cpu : public at::meta::structured_upsample_bicubic2d {
void impl(const at::Tensor & self, at::IntArrayRef output_size, bool align_corners, c10::optional<double> scales_h, c10::optional<double> scales_w, const at::Tensor & out);
};
struct TORCH_API structured_upsample_bicubic2d_out_cuda : public at::meta::structured_upsample_bicubic2d {
void impl(const at::Tensor & self, at::IntArrayRef output_size, bool align_corners, c10::optional<double> scales_h, c10::optional<double> scales_w, const at::Tensor & out);
};
struct TORCH_API structured_upsample_bicubic2d_backward_out_cpu : public at::meta::structured_upsample_bicubic2d_backward {
void impl(const at::Tensor & grad_output, at::IntArrayRef output_size, at::IntArrayRef input_size, bool align_corners, c10::optional<double> scales_h, c10::optional<double> scales_w, const at::Tensor & grad_input);
};
struct TORCH_API structured_upsample_bicubic2d_backward_out_cuda : public at::meta::structured_upsample_bicubic2d_backward {
void impl(const at::Tensor & grad_output, at::IntArrayRef output_size, at::IntArrayRef input_size, bool align_corners, c10::optional<double> scales_h, c10::optional<double> scales_w, const at::Tensor & grad_input);
};
struct TORCH_API structured_upsample_trilinear3d_out_cpu : public at::meta::structured_upsample_trilinear3d {
void impl(const at::Tensor & self, at::IntArrayRef output_size, bool align_corners, c10::optional<double> scales_d, c10::optional<double> scales_h, c10::optional<double> scales_w, const at::Tensor & out);
};
struct TORCH_API structured_upsample_trilinear3d_out_cuda : public at::meta::structured_upsample_trilinear3d {
void impl(const at::Tensor & self, at::IntArrayRef output_size, bool align_corners, c10::optional<double> scales_d, c10::optional<double> scales_h, c10::optional<double> scales_w, const at::Tensor & out);
};
struct TORCH_API structured_upsample_trilinear3d_backward_out_cpu : public at::meta::structured_upsample_trilinear3d_backward {
void impl(const at::Tensor & grad_output, at::IntArrayRef output_size, at::IntArrayRef input_size, bool align_corners, c10::optional<double> scales_d, c10::optional<double> scales_h, c10::optional<double> scales_w, const at::Tensor & grad_input);
};
struct TORCH_API structured_upsample_trilinear3d_backward_out_cuda : public at::meta::structured_upsample_trilinear3d_backward {
void impl(const at::Tensor & grad_output, at::IntArrayRef output_size, at::IntArrayRef input_size, bool align_corners, c10::optional<double> scales_d, c10::optional<double> scales_h, c10::optional<double> scales_w, const at::Tensor & grad_input);
};
struct TORCH_API structured_upsample_nearest1d_out_cpu : public at::meta::structured_upsample_nearest1d {
void impl(const at::Tensor & self, at::IntArrayRef output_size, c10::optional<double> scales, const at::Tensor & out);
};
struct TORCH_API structured_upsample_nearest1d_out_cuda : public at::meta::structured_upsample_nearest1d {
void impl(const at::Tensor & self, at::IntArrayRef output_size, c10::optional<double> scales, const at::Tensor & out);
};
struct TORCH_API structured_upsample_nearest1d_backward_out_cpu : public at::meta::structured_upsample_nearest1d_backward {
void impl(const at::Tensor & grad_output, at::IntArrayRef output_size, at::IntArrayRef input_size, c10::optional<double> scales, const at::Tensor & grad_input);
};
struct TORCH_API structured_upsample_nearest1d_backward_out_cuda : public at::meta::structured_upsample_nearest1d_backward {
void impl(const at::Tensor & grad_output, at::IntArrayRef output_size, at::IntArrayRef input_size, c10::optional<double> scales, const at::Tensor & grad_input);
};
struct TORCH_API structured_upsample_nearest2d_out_cpu : public at::meta::structured_upsample_nearest2d {
void impl(const at::Tensor & self, at::IntArrayRef output_size, c10::optional<double> scales_h, c10::optional<double> scales_w, const at::Tensor & out);
};
struct TORCH_API structured_upsample_nearest2d_out_cuda : public at::meta::structured_upsample_nearest2d {
void impl(const at::Tensor & self, at::IntArrayRef output_size, c10::optional<double> scales_h, c10::optional<double> scales_w, const at::Tensor & out);
};
TORCH_API at::Tensor upsample_nearest2d_quantized_cpu(const at::Tensor & self, at::IntArrayRef output_size, c10::optional<double> scales_h=c10::nullopt, c10::optional<double> scales_w=c10::nullopt);
struct TORCH_API structured_upsample_nearest2d_backward_out_cpu : public at::meta::structured_upsample_nearest2d_backward {
void impl(const at::Tensor & grad_output, at::IntArrayRef output_size, at::IntArrayRef input_size, c10::optional<double> scales_h, c10::optional<double> scales_w, const at::Tensor & grad_input);
};
struct TORCH_API structured_upsample_nearest2d_backward_out_cuda : public at::meta::structured_upsample_nearest2d_backward {
void impl(const at::Tensor & grad_output, at::IntArrayRef output_size, at::IntArrayRef input_size, c10::optional<double> scales_h, c10::optional<double> scales_w, const at::Tensor & grad_input);
};
struct TORCH_API structured_upsample_nearest3d_out_cpu : public at::meta::structured_upsample_nearest3d {
void impl(const at::Tensor & self, at::IntArrayRef output_size, c10::optional<double> scales_d, c10::optional<double> scales_h, c10::optional<double> scales_w, const at::Tensor & out);
};
struct TORCH_API structured_upsample_nearest3d_out_cuda : public at::meta::structured_upsample_nearest3d {
void impl(const at::Tensor & self, at::IntArrayRef output_size, c10::optional<double> scales_d, c10::optional<double> scales_h, c10::optional<double> scales_w, const at::Tensor & out);
};
TORCH_API at::Tensor upsample_nearest3d_quantized_cpu(const at::Tensor & self, at::IntArrayRef output_size, c10::optional<double> scales_d=c10::nullopt, c10::optional<double> scales_h=c10::nullopt, c10::optional<double> scales_w=c10::nullopt);
struct TORCH_API structured_upsample_nearest3d_backward_out_cpu : public at::meta::structured_upsample_nearest3d_backward {
void impl(const at::Tensor & grad_output, at::IntArrayRef output_size, at::IntArrayRef input_size, c10::optional<double> scales_d, c10::optional<double> scales_h, c10::optional<double> scales_w, const at::Tensor & grad_input);
};
struct TORCH_API structured_upsample_nearest3d_backward_out_cuda : public at::meta::structured_upsample_nearest3d_backward {
void impl(const at::Tensor & grad_output, at::IntArrayRef output_size, at::IntArrayRef input_size, c10::optional<double> scales_d, c10::optional<double> scales_h, c10::optional<double> scales_w, const at::Tensor & grad_input);
};
struct TORCH_API structured_sigmoid_backward_out : public at::meta::structured_sigmoid_backward {
void impl(const at::Tensor & grad_output, const at::Tensor & output, const at::Tensor & grad_input);
};
struct TORCH_API structured_logit_backward_out : public at::meta::structured_logit_backward {
void impl(const at::Tensor & grad_output, const at::Tensor & self, c10::optional<double> eps, const at::Tensor & grad_input);
};
struct TORCH_API structured_tanh_backward_out : public at::meta::structured_tanh_backward {
void impl(const at::Tensor & grad_output, const at::Tensor & output, const at::Tensor & grad_input);
};
struct TORCH_API structured_slow_conv_transpose2d_structured_cpu : public at::meta::structured_slow_conv_transpose2d {
void impl(const at::Tensor & self, const at::Tensor & weight, at::IntArrayRef kernel_size, at::OptionalTensorRef bias, at::IntArrayRef stride, at::IntArrayRef padding, at::IntArrayRef output_padding, at::IntArrayRef dilation, const at::Tensor & out);
};
struct TORCH_API structured_slow_conv_transpose2d_structured_cuda : public at::meta::structured_slow_conv_transpose2d {
void impl(const at::Tensor & self, const at::Tensor & weight, at::IntArrayRef kernel_size, at::OptionalTensorRef bias, at::IntArrayRef stride, at::IntArrayRef padding, at::IntArrayRef output_padding, at::IntArrayRef dilation, const at::Tensor & out);
};
TORCH_API ::std::tuple<at::Tensor &,at::Tensor &,at::Tensor &> slow_conv_transpose2d_backward_out_cpu(const at::Tensor & grad_output, const at::Tensor & self, const at::Tensor & weight, at::IntArrayRef kernel_size, at::IntArrayRef stride, at::IntArrayRef padding, at::IntArrayRef output_padding, at::IntArrayRef dilation, const at::Tensor & columns, const at::Tensor & ones, at::Tensor & grad_input, at::Tensor & grad_weight, at::Tensor & grad_bias);
TORCH_API ::std::tuple<at::Tensor &,at::Tensor &,at::Tensor &> slow_conv_transpose2d_backward_out_cuda(const at::Tensor & grad_output, const at::Tensor & self, const at::Tensor & weight, at::IntArrayRef kernel_size, at::IntArrayRef stride, at::IntArrayRef padding, at::IntArrayRef output_padding, at::IntArrayRef dilation, const at::Tensor & columns, const at::Tensor & ones, at::Tensor & grad_input, at::Tensor & grad_weight, at::Tensor & grad_bias);
TORCH_API ::std::tuple<at::Tensor,at::Tensor,at::Tensor> slow_conv_transpose2d_backward_cpu(const at::Tensor & grad_output, const at::Tensor & self, const at::Tensor & weight, at::IntArrayRef kernel_size, at::IntArrayRef stride, at::IntArrayRef padding, at::IntArrayRef output_padding, at::IntArrayRef dilation, const at::Tensor & columns, const at::Tensor & ones, ::std::array<bool,3> output_mask);
TORCH_API ::std::tuple<at::Tensor,at::Tensor,at::Tensor> slow_conv_transpose2d_backward_cuda(const at::Tensor & grad_output, const at::Tensor & self, const at::Tensor & weight, at::IntArrayRef kernel_size, at::IntArrayRef stride, at::IntArrayRef padding, at::IntArrayRef output_padding, at::IntArrayRef dilation, const at::Tensor & columns, const at::Tensor & ones, ::std::array<bool,3> output_mask);
TORCH_API at::Tensor slow_conv_transpose3d_cpu(const at::Tensor & self, const at::Tensor & weight, at::IntArrayRef kernel_size, const c10::optional<at::Tensor> & bias={}, at::IntArrayRef stride=1, at::IntArrayRef padding=0, at::IntArrayRef output_padding=0, at::IntArrayRef dilation=1);
TORCH_API at::Tensor & slow_conv_transpose3d_out_cpu(const at::Tensor & self, const at::Tensor & weight, at::IntArrayRef kernel_size, const c10::optional<at::Tensor> & bias, at::IntArrayRef stride, at::IntArrayRef padding, at::IntArrayRef output_padding, at::IntArrayRef dilation, at::Tensor & out);
TORCH_API at::Tensor slow_conv_transpose3d_cuda(const at::Tensor & self, const at::Tensor & weight, at::IntArrayRef kernel_size, const c10::optional<at::Tensor> & bias={}, at::IntArrayRef stride=1, at::IntArrayRef padding=0, at::IntArrayRef output_padding=0, at::IntArrayRef dilation=1);
TORCH_API at::Tensor & slow_conv_transpose3d_out_cuda(const at::Tensor & self, const at::Tensor & weight, at::IntArrayRef kernel_size, const c10::optional<at::Tensor> & bias, at::IntArrayRef stride, at::IntArrayRef padding, at::IntArrayRef output_padding, at::IntArrayRef dilation, at::Tensor & out);
TORCH_API ::std::tuple<at::Tensor &,at::Tensor &,at::Tensor &> slow_conv_transpose3d_backward_out_cpu(const at::Tensor & grad_output, const at::Tensor & self, const at::Tensor & weight, at::IntArrayRef kernel_size, at::IntArrayRef stride, at::IntArrayRef padding, at::IntArrayRef output_padding, at::IntArrayRef dilation, const at::Tensor & finput, const at::Tensor & fgrad_input, at::Tensor & grad_input, at::Tensor & grad_weight, at::Tensor & grad_bias);
TORCH_API ::std::tuple<at::Tensor &,at::Tensor &,at::Tensor &> slow_conv_transpose3d_backward_out_cuda(const at::Tensor & grad_output, const at::Tensor & self, const at::Tensor & weight, at::IntArrayRef kernel_size, at::IntArrayRef stride, at::IntArrayRef padding, at::IntArrayRef output_padding, at::IntArrayRef dilation, const at::Tensor & finput, const at::Tensor & fgrad_input, at::Tensor & grad_input, at::Tensor & grad_weight, at::Tensor & grad_bias);
TORCH_API ::std::tuple<at::Tensor,at::Tensor,at::Tensor> slow_conv_transpose3d_backward_cpu(const at::Tensor & grad_output, const at::Tensor & self, const at::Tensor & weight, at::IntArrayRef kernel_size, at::IntArrayRef stride, at::IntArrayRef padding, at::IntArrayRef output_padding, at::IntArrayRef dilation, const at::Tensor & finput, const at::Tensor & fgrad_input, ::std::array<bool,3> output_mask);
TORCH_API ::std::tuple<at::Tensor,at::Tensor,at::Tensor> slow_conv_transpose3d_backward_cuda(const at::Tensor & grad_output, const at::Tensor & self, const at::Tensor & weight, at::IntArrayRef kernel_size, at::IntArrayRef stride, at::IntArrayRef padding, at::IntArrayRef output_padding, at::IntArrayRef dilation, const at::Tensor & finput, const at::Tensor & fgrad_input, ::std::array<bool,3> output_mask);
TORCH_API at::Tensor thnn_conv2d(const at::Tensor & self, const at::Tensor & weight, at::IntArrayRef kernel_size, const c10::optional<at::Tensor> & bias={}, at::IntArrayRef stride=1, at::IntArrayRef padding=0);
TORCH_API at::Tensor & thnn_conv2d_out(const at::Tensor & self, const at::Tensor & weight, at::IntArrayRef kernel_size, const c10::optional<at::Tensor> & bias, at::IntArrayRef stride, at::IntArrayRef padding, at::Tensor & out);
TORCH_API ::std::tuple<at::Tensor,at::Tensor,at::Tensor> slow_conv2d_forward_cpu(const at::Tensor & self, const at::Tensor & weight, at::IntArrayRef kernel_size, const c10::optional<at::Tensor> & bias, at::IntArrayRef stride, at::IntArrayRef padding);
TORCH_API ::std::tuple<at::Tensor &,at::Tensor &,at::Tensor &> slow_conv2d_forward_out_cpu(const at::Tensor & self, const at::Tensor & weight, at::IntArrayRef kernel_size, const c10::optional<at::Tensor> & bias, at::IntArrayRef stride, at::IntArrayRef padding, at::Tensor & output, at::Tensor & finput, at::Tensor & fgrad_input);
TORCH_API ::std::tuple<at::Tensor,at::Tensor,at::Tensor> slow_conv2d_forward_cuda(const at::Tensor & self, const at::Tensor & weight, at::IntArrayRef kernel_size, const c10::optional<at::Tensor> & bias, at::IntArrayRef stride, at::IntArrayRef padding);
TORCH_API ::std::tuple<at::Tensor &,at::Tensor &,at::Tensor &> slow_conv2d_forward_out_cuda(const at::Tensor & self, const at::Tensor & weight, at::IntArrayRef kernel_size, const c10::optional<at::Tensor> & bias, at::IntArrayRef stride, at::IntArrayRef padding, at::Tensor & output, at::Tensor & finput, at::Tensor & fgrad_input);
TORCH_API ::std::tuple<at::Tensor &,at::Tensor &,at::Tensor &> slow_conv2d_backward_out_cpu(const at::Tensor & grad_output, const at::Tensor & self, const at::Tensor & weight, at::IntArrayRef kernel_size, at::IntArrayRef stride, at::IntArrayRef padding, const at::Tensor & finput, const at::Tensor & fgrad_input, at::Tensor & grad_input, at::Tensor & grad_weight, at::Tensor & grad_bias);
TORCH_API ::std::tuple<at::Tensor &,at::Tensor &,at::Tensor &> slow_conv2d_backward_out_cuda(const at::Tensor & grad_output, const at::Tensor & self, const at::Tensor & weight, at::IntArrayRef kernel_size, at::IntArrayRef stride, at::IntArrayRef padding, const at::Tensor & finput, const at::Tensor & fgrad_input, at::Tensor & grad_input, at::Tensor & grad_weight, at::Tensor & grad_bias);
TORCH_API ::std::tuple<at::Tensor,at::Tensor,at::Tensor> slow_conv2d_backward_cpu(const at::Tensor & grad_output, const at::Tensor & self, const at::Tensor & weight, at::IntArrayRef kernel_size, at::IntArrayRef stride, at::IntArrayRef padding, const at::Tensor & finput, const at::Tensor & fgrad_input, ::std::array<bool,3> output_mask);
TORCH_API ::std::tuple<at::Tensor,at::Tensor,at::Tensor> slow_conv2d_backward_cuda(const at::Tensor & grad_output, const at::Tensor & self, const at::Tensor & weight, at::IntArrayRef kernel_size, at::IntArrayRef stride, at::IntArrayRef padding, const at::Tensor & finput, const at::Tensor & fgrad_input, ::std::array<bool,3> output_mask);
TORCH_API at::Tensor conv_depthwise2d_cuda(const at::Tensor & self, const at::Tensor & weight, at::IntArrayRef kernel_size, const c10::optional<at::Tensor> & bias, at::IntArrayRef stride, at::IntArrayRef padding, at::IntArrayRef dilation);
TORCH_API const at::Tensor & conv_depthwise2d_cuda_out(const at::Tensor & self, const at::Tensor & weight, at::IntArrayRef kernel_size, const c10::optional<at::Tensor> & bias, at::IntArrayRef stride, at::IntArrayRef padding, at::IntArrayRef dilation, const at::Tensor & out);
TORCH_API ::std::tuple<at::Tensor &,at::Tensor &> conv_depthwise2d_backward_cuda_out(const at::Tensor & grad_output, const at::Tensor & self, const at::Tensor & weight, at::IntArrayRef kernel_size, at::IntArrayRef stride, at::IntArrayRef padding, at::IntArrayRef dilation, at::Tensor & grad_input, at::Tensor & grad_weight);
TORCH_API ::std::tuple<at::Tensor,at::Tensor> conv_depthwise2d_backward_cuda(const at::Tensor & grad_output, const at::Tensor & self, const at::Tensor & weight, at::IntArrayRef kernel_size, at::IntArrayRef stride, at::IntArrayRef padding, at::IntArrayRef dilation, ::std::array<bool,2> output_mask);
TORCH_API at::Tensor conv_depthwise3d_cuda(const at::Tensor & self, const at::Tensor & weight, at::IntArrayRef kernel_size, const c10::optional<at::Tensor> & bias, at::IntArrayRef stride, at::IntArrayRef padding, at::IntArrayRef dilation);
TORCH_API ::std::tuple<at::Tensor &,at::Tensor &,at::Tensor &> conv_depthwise3d_backward_cuda_out(const at::Tensor & grad_output, const at::Tensor & self, const at::Tensor & weight, at::IntArrayRef kernel_size, at::IntArrayRef stride, at::IntArrayRef padding, at::IntArrayRef dilation, at::Tensor & grad_input, at::Tensor & grad_weight, at::Tensor & grad_bias);
TORCH_API ::std::tuple<at::Tensor,at::Tensor,at::Tensor> conv_depthwise3d_backward_cuda(const at::Tensor & grad_output, const at::Tensor & self, const at::Tensor & weight, at::IntArrayRef kernel_size, at::IntArrayRef stride, at::IntArrayRef padding, at::IntArrayRef dilation, ::std::array<bool,3> output_mask);
TORCH_API at::Tensor slow_conv3d(const at::Tensor & self, const at::Tensor & weight, at::IntArrayRef kernel_size, const c10::optional<at::Tensor> & bias={}, at::IntArrayRef stride=1, at::IntArrayRef padding=0);
TORCH_API at::Tensor & slow_conv3d_out(const at::Tensor & self, const at::Tensor & weight, at::IntArrayRef kernel_size, const c10::optional<at::Tensor> & bias, at::IntArrayRef stride, at::IntArrayRef padding, at::Tensor & out);
TORCH_API ::std::tuple<at::Tensor,at::Tensor,at::Tensor> slow_conv3d_forward_cpu(const at::Tensor & self, const at::Tensor & weight, at::IntArrayRef kernel_size, const c10::optional<at::Tensor> & bias, at::IntArrayRef stride, at::IntArrayRef padding);
TORCH_API ::std::tuple<at::Tensor &,at::Tensor &,at::Tensor &> slow_conv3d_forward_out_cpu(const at::Tensor & self, const at::Tensor & weight, at::IntArrayRef kernel_size, const c10::optional<at::Tensor> & bias, at::IntArrayRef stride, at::IntArrayRef padding, at::Tensor & output, at::Tensor & finput, at::Tensor & fgrad_input);
TORCH_API ::std::tuple<at::Tensor &,at::Tensor &,at::Tensor &> slow_conv3d_backward_out_cpu(const at::Tensor & grad_output, const at::Tensor & self, const at::Tensor & weight, at::IntArrayRef kernel_size, at::IntArrayRef stride, at::IntArrayRef padding, const at::Tensor & finput, const at::Tensor & fgrad_input, at::Tensor & grad_input, at::Tensor & grad_weight, at::Tensor & grad_bias);
TORCH_API ::std::tuple<at::Tensor,at::Tensor,at::Tensor> slow_conv3d_backward_cpu(const at::Tensor & grad_output, const at::Tensor & self, const at::Tensor & weight, at::IntArrayRef kernel_size, at::IntArrayRef stride, at::IntArrayRef padding, const at::Tensor & finput, const at::Tensor & fgrad_input, ::std::array<bool,3> output_mask);
TORCH_API at::Tensor slow_conv_dilated2d_cpu(const at::Tensor & self, const at::Tensor & weight, at::IntArrayRef kernel_size, const c10::optional<at::Tensor> & bias={}, at::IntArrayRef stride=1, at::IntArrayRef padding=0, at::IntArrayRef dilation=1);
TORCH_API at::Tensor slow_conv_dilated2d_cuda(const at::Tensor & self, const at::Tensor & weight, at::IntArrayRef kernel_size, const c10::optional<at::Tensor> & bias={}, at::IntArrayRef stride=1, at::IntArrayRef padding=0, at::IntArrayRef dilation=1);
TORCH_API ::std::tuple<at::Tensor,at::Tensor,at::Tensor> slow_conv_dilated2d_backward_cpu(const at::Tensor & grad_output, const at::Tensor & self, const at::Tensor & weight, at::IntArrayRef kernel_size, at::IntArrayRef stride, at::IntArrayRef padding, at::IntArrayRef dilation, ::std::array<bool,3> output_mask);
TORCH_API ::std::tuple<at::Tensor,at::Tensor,at::Tensor> slow_conv_dilated2d_backward_cuda(const at::Tensor & grad_output, const at::Tensor & self, const at::Tensor & weight, at::IntArrayRef kernel_size, at::IntArrayRef stride, at::IntArrayRef padding, at::IntArrayRef dilation, ::std::array<bool,3> output_mask);
TORCH_API at::Tensor slow_conv_dilated3d_cpu(const at::Tensor & self, const at::Tensor & weight, at::IntArrayRef kernel_size, const c10::optional<at::Tensor> & bias={}, at::IntArrayRef stride=1, at::IntArrayRef padding=0, at::IntArrayRef dilation=1);
TORCH_API at::Tensor slow_conv_dilated3d_cuda(const at::Tensor & self, const at::Tensor & weight, at::IntArrayRef kernel_size, const c10::optional<at::Tensor> & bias={}, at::IntArrayRef stride=1, at::IntArrayRef padding=0, at::IntArrayRef dilation=1);
TORCH_API ::std::tuple<at::Tensor,at::Tensor,at::Tensor> slow_conv_dilated3d_backward_cpu(const at::Tensor & grad_output, const at::Tensor & self, const at::Tensor & weight, at::IntArrayRef kernel_size, at::IntArrayRef stride, at::IntArrayRef padding, at::IntArrayRef dilation, ::std::array<bool,3> output_mask);
TORCH_API ::std::tuple<at::Tensor,at::Tensor,at::Tensor> slow_conv_dilated3d_backward_cuda(const at::Tensor & grad_output, const at::Tensor & self, const at::Tensor & weight, at::IntArrayRef kernel_size, at::IntArrayRef stride, at::IntArrayRef padding, at::IntArrayRef dilation, ::std::array<bool,3> output_mask);
TORCH_API at::Tensor col2im_cpu(const at::Tensor & self, at::IntArrayRef output_size, at::IntArrayRef kernel_size, at::IntArrayRef dilation, at::IntArrayRef padding, at::IntArrayRef stride);
TORCH_API at::Tensor & col2im_out_cpu(const at::Tensor & self, at::IntArrayRef output_size, at::IntArrayRef kernel_size, at::IntArrayRef dilation, at::IntArrayRef padding, at::IntArrayRef stride, at::Tensor & out);
TORCH_API at::Tensor col2im_cuda(const at::Tensor & self, at::IntArrayRef output_size, at::IntArrayRef kernel_size, at::IntArrayRef dilation, at::IntArrayRef padding, at::IntArrayRef stride);
TORCH_API at::Tensor & col2im_out_cuda(const at::Tensor & self, at::IntArrayRef output_size, at::IntArrayRef kernel_size, at::IntArrayRef dilation, at::IntArrayRef padding, at::IntArrayRef stride, at::Tensor & out);
TORCH_API at::Tensor col2im_backward_cpu(const at::Tensor & grad_output, at::IntArrayRef kernel_size, at::IntArrayRef dilation, at::IntArrayRef padding, at::IntArrayRef stride);
TORCH_API at::Tensor & col2im_backward_out_cpu(const at::Tensor & grad_output, at::IntArrayRef kernel_size, at::IntArrayRef dilation, at::IntArrayRef padding, at::IntArrayRef stride, at::Tensor & grad_input);
TORCH_API at::Tensor col2im_backward_cuda(const at::Tensor & grad_output, at::IntArrayRef kernel_size, at::IntArrayRef dilation, at::IntArrayRef padding, at::IntArrayRef stride);
TORCH_API at::Tensor & col2im_backward_out_cuda(const at::Tensor & grad_output, at::IntArrayRef kernel_size, at::IntArrayRef dilation, at::IntArrayRef padding, at::IntArrayRef stride, at::Tensor & grad_input);
TORCH_API at::Tensor column_stack(at::TensorList tensors);
TORCH_API at::Tensor & column_stack_out(at::TensorList tensors, at::Tensor & out);
TORCH_API at::Tensor im2col_cpu(const at::Tensor & self, at::IntArrayRef kernel_size, at::IntArrayRef dilation, at::IntArrayRef padding, at::IntArrayRef stride);
TORCH_API at::Tensor & im2col_out_cpu(const at::Tensor & self, at::IntArrayRef kernel_size, at::IntArrayRef dilation, at::IntArrayRef padding, at::IntArrayRef stride, at::Tensor & out);
TORCH_API at::Tensor im2col_cuda(const at::Tensor & self, at::IntArrayRef kernel_size, at::IntArrayRef dilation, at::IntArrayRef padding, at::IntArrayRef stride);
TORCH_API at::Tensor & im2col_out_cuda(const at::Tensor & self, at::IntArrayRef kernel_size, at::IntArrayRef dilation, at::IntArrayRef padding, at::IntArrayRef stride, at::Tensor & out);
TORCH_API at::Tensor im2col_backward_cpu(const at::Tensor & grad_output, at::IntArrayRef input_size, at::IntArrayRef kernel_size, at::IntArrayRef dilation, at::IntArrayRef padding, at::IntArrayRef stride);
TORCH_API at::Tensor & im2col_backward_out_cpu(const at::Tensor & grad_output, at::IntArrayRef input_size, at::IntArrayRef kernel_size, at::IntArrayRef dilation, at::IntArrayRef padding, at::IntArrayRef stride, at::Tensor & grad_input);
TORCH_API at::Tensor im2col_backward_cuda(const at::Tensor & grad_output, at::IntArrayRef input_size, at::IntArrayRef kernel_size, at::IntArrayRef dilation, at::IntArrayRef padding, at::IntArrayRef stride);
TORCH_API at::Tensor & im2col_backward_out_cuda(const at::Tensor & grad_output, at::IntArrayRef input_size, at::IntArrayRef kernel_size, at::IntArrayRef dilation, at::IntArrayRef padding, at::IntArrayRef stride, at::Tensor & grad_input);
TORCH_API at::Tensor isfinite(const at::Tensor & self);
TORCH_API at::Tensor isinf(const at::Tensor & self);
TORCH_API void record_stream_cuda(at::Tensor & self, at::Stream s);
struct TORCH_API structured_isposinf_out : public at::meta::structured_isposinf {
void impl(const at::Tensor & self, const at::Tensor & out);
};
struct TORCH_API structured_isneginf_out : public at::meta::structured_isneginf {
void impl(const at::Tensor & self, const at::Tensor & out);
};
TORCH_API at::Tensor _add_batch_dim(const at::Tensor & self, int64_t batch_dim, int64_t level);
TORCH_API at::Tensor _remove_batch_dim(const at::Tensor & self, int64_t level, int64_t batch_size, int64_t out_dim);
struct TORCH_API structured_special_entr_out : public at::meta::structured_special_entr {
void impl(const at::Tensor & self, const at::Tensor & out);
};
struct TORCH_API structured_special_ndtri_out : public at::meta::structured_special_ndtri {
void impl(const at::Tensor & self, const at::Tensor & out);
};
TORCH_API at::Tensor special_expm1(const at::Tensor & self);
TORCH_API at::Tensor & special_expm1_out(const at::Tensor & self, at::Tensor & out);
TORCH_API at::Tensor special_exp2(const at::Tensor & self);
TORCH_API at::Tensor & special_exp2_out(const at::Tensor & self, at::Tensor & out);
TORCH_API at::Tensor special_psi(const at::Tensor & self);
TORCH_API at::Tensor & special_psi_out(const at::Tensor & self, at::Tensor & out);
TORCH_API at::Tensor special_digamma(const at::Tensor & self);
TORCH_API at::Tensor & special_digamma_out(const at::Tensor & self, at::Tensor & out);
TORCH_API at::Tensor special_gammaln(const at::Tensor & self);
TORCH_API at::Tensor & special_gammaln_out(const at::Tensor & self, at::Tensor & out);
TORCH_API at::Tensor special_erf(const at::Tensor & self);
TORCH_API at::Tensor & special_erf_out(const at::Tensor & self, at::Tensor & out);
TORCH_API at::Tensor special_erfc(const at::Tensor & self);
TORCH_API at::Tensor & special_erfc_out(const at::Tensor & self, at::Tensor & out);
struct TORCH_API structured_special_erfcx_out : public at::meta::structured_special_erfcx {
void impl(const at::Tensor & self, const at::Tensor & out);
};
TORCH_API at::Tensor special_erfinv(const at::Tensor & self);
TORCH_API at::Tensor & special_erfinv_out(const at::Tensor & self, at::Tensor & out);
TORCH_API at::Tensor special_ndtr(const at::Tensor & self);
TORCH_API at::Tensor & special_ndtr_out(const at::Tensor & self, at::Tensor & out);
struct TORCH_API structured_special_xlog1py_out : public at::meta::structured_special_xlog1py {
void impl(const at::Tensor & self, const at::Tensor & other, const at::Tensor & out);
};
TORCH_API at::Tensor special_xlog1py(const at::Scalar & self, const at::Tensor & other);
TORCH_API at::Tensor & special_xlog1py_out(const at::Scalar & self, const at::Tensor & other, at::Tensor & out);
TORCH_API at::Tensor special_xlog1py(const at::Tensor & self, const at::Scalar & other);
TORCH_API at::Tensor & special_xlog1py_out(const at::Tensor & self, const at::Scalar & other, at::Tensor & out);
TORCH_API at::Tensor special_xlogy(const at::Tensor & self, const at::Tensor & other);
TORCH_API at::Tensor & special_xlogy_out(const at::Tensor & self, const at::Tensor & other, at::Tensor & out);
TORCH_API at::Tensor special_xlogy(const at::Scalar & self, const at::Tensor & other);
TORCH_API at::Tensor & special_xlogy_out(const at::Scalar & self, const at::Tensor & other, at::Tensor & out);
TORCH_API at::Tensor special_xlogy(const at::Tensor & self, const at::Scalar & other);
TORCH_API at::Tensor & special_xlogy_out(const at::Tensor & self, const at::Scalar & other, at::Tensor & out);
TORCH_API at::Tensor special_zeta(const at::Tensor & self, const at::Tensor & other);
struct TORCH_API structured_special_zeta_out : public at::meta::structured_special_zeta {
void impl(const at::Tensor & self, const at::Tensor & other, const at::Tensor & out);
};
TORCH_API at::Tensor special_zeta(const at::Scalar & self, const at::Tensor & other);
TORCH_API at::Tensor & special_zeta_out(const at::Scalar & self, const at::Tensor & other, at::Tensor & out);
TORCH_API at::Tensor special_zeta(const at::Tensor & self, const at::Scalar & other);
TORCH_API at::Tensor & special_zeta_out(const at::Tensor & self, const at::Scalar & other, at::Tensor & out);
TORCH_API at::Tensor special_i0(const at::Tensor & self);
TORCH_API at::Tensor & special_i0_out(const at::Tensor & self, at::Tensor & out);
struct TORCH_API structured_special_i0e_out : public at::meta::structured_special_i0e {
void impl(const at::Tensor & self, const at::Tensor & out);
};
struct TORCH_API structured_special_i1_out : public at::meta::structured_special_i1 {
void impl(const at::Tensor & self, const at::Tensor & out);
};
struct TORCH_API structured_special_i1e_out : public at::meta::structured_special_i1e {
void impl(const at::Tensor & self, const at::Tensor & out);
};
TORCH_API at::Tensor special_logit(const at::Tensor & self, c10::optional<double> eps=c10::nullopt);
TORCH_API at::Tensor & special_logit_out(const at::Tensor & self, c10::optional<double> eps, at::Tensor & out);
TORCH_API at::Tensor special_polygamma(int64_t n, const at::Tensor & self);
TORCH_API at::Tensor & special_polygamma_out(int64_t n, const at::Tensor & self, at::Tensor & out);
TORCH_API at::Tensor special_logsumexp(const at::Tensor & self, at::IntArrayRef dim, bool keepdim=false);
TORCH_API at::Tensor & special_logsumexp_out(const at::Tensor & self, at::IntArrayRef dim, bool keepdim, at::Tensor & out);
TORCH_API at::Tensor special_expit(const at::Tensor & self);
TORCH_API at::Tensor & special_expit_out(const at::Tensor & self, at::Tensor & out);
TORCH_API at::Tensor special_sinc(const at::Tensor & self);
TORCH_API at::Tensor & special_sinc_out(const at::Tensor & self, at::Tensor & out);
TORCH_API at::Tensor special_round(const at::Tensor & self);
TORCH_API at::Tensor & special_round_out(const at::Tensor & self, at::Tensor & out);
TORCH_API at::Tensor special_log1p(const at::Tensor & self);
TORCH_API at::Tensor & special_log1p_out(const at::Tensor & self, at::Tensor & out);
TORCH_API at::Tensor special_log_softmax(const at::Tensor & self, int64_t dim, c10::optional<at::ScalarType> dtype=c10::nullopt);
TORCH_API at::Tensor special_gammainc(const at::Tensor & self, const at::Tensor & other);
TORCH_API at::Tensor & special_gammainc_out(const at::Tensor & self, const at::Tensor & other, at::Tensor & out);
TORCH_API at::Tensor special_gammaincc(const at::Tensor & self, const at::Tensor & other);
TORCH_API at::Tensor & special_gammaincc_out(const at::Tensor & self, const at::Tensor & other, at::Tensor & out);
TORCH_API at::Tensor special_multigammaln(const at::Tensor & self, int64_t p);
TORCH_API at::Tensor & special_multigammaln_out(const at::Tensor & self, int64_t p, at::Tensor & out);
TORCH_API at::Tensor fft_fft(const at::Tensor & self, c10::optional<int64_t> n=c10::nullopt, int64_t dim=-1, c10::optional<c10::string_view> norm=c10::nullopt);
TORCH_API at::Tensor & fft_fft_out(const at::Tensor & self, c10::optional<int64_t> n, int64_t dim, c10::optional<c10::string_view> norm, at::Tensor & out);
TORCH_API at::Tensor fft_ifft(const at::Tensor & self, c10::optional<int64_t> n=c10::nullopt, int64_t dim=-1, c10::optional<c10::string_view> norm=c10::nullopt);
TORCH_API at::Tensor & fft_ifft_out(const at::Tensor & self, c10::optional<int64_t> n, int64_t dim, c10::optional<c10::string_view> norm, at::Tensor & out);
TORCH_API at::Tensor fft_rfft(const at::Tensor & self, c10::optional<int64_t> n=c10::nullopt, int64_t dim=-1, c10::optional<c10::string_view> norm=c10::nullopt);
TORCH_API at::Tensor & fft_rfft_out(const at::Tensor & self, c10::optional<int64_t> n, int64_t dim, c10::optional<c10::string_view> norm, at::Tensor & out);
TORCH_API at::Tensor fft_irfft(const at::Tensor & self, c10::optional<int64_t> n=c10::nullopt, int64_t dim=-1, c10::optional<c10::string_view> norm=c10::nullopt);
TORCH_API at::Tensor & fft_irfft_out(const at::Tensor & self, c10::optional<int64_t> n, int64_t dim, c10::optional<c10::string_view> norm, at::Tensor & out);
TORCH_API at::Tensor fft_hfft(const at::Tensor & self, c10::optional<int64_t> n=c10::nullopt, int64_t dim=-1, c10::optional<c10::string_view> norm=c10::nullopt);
TORCH_API at::Tensor & fft_hfft_out(const at::Tensor & self, c10::optional<int64_t> n, int64_t dim, c10::optional<c10::string_view> norm, at::Tensor & out);
TORCH_API at::Tensor fft_ihfft(const at::Tensor & self, c10::optional<int64_t> n=c10::nullopt, int64_t dim=-1, c10::optional<c10::string_view> norm=c10::nullopt);
TORCH_API at::Tensor & fft_ihfft_out(const at::Tensor & self, c10::optional<int64_t> n, int64_t dim, c10::optional<c10::string_view> norm, at::Tensor & out);
TORCH_API at::Tensor fft_fft2(const at::Tensor & self, c10::optional<at::IntArrayRef> s=c10::nullopt, at::IntArrayRef dim={-2,-1}, c10::optional<c10::string_view> norm=c10::nullopt);
TORCH_API at::Tensor & fft_fft2_out(const at::Tensor & self, c10::optional<at::IntArrayRef> s, at::IntArrayRef dim, c10::optional<c10::string_view> norm, at::Tensor & out);
TORCH_API at::Tensor fft_ifft2(const at::Tensor & self, c10::optional<at::IntArrayRef> s=c10::nullopt, at::IntArrayRef dim={-2,-1}, c10::optional<c10::string_view> norm=c10::nullopt);
TORCH_API at::Tensor & fft_ifft2_out(const at::Tensor & self, c10::optional<at::IntArrayRef> s, at::IntArrayRef dim, c10::optional<c10::string_view> norm, at::Tensor & out);
TORCH_API at::Tensor fft_rfft2(const at::Tensor & self, c10::optional<at::IntArrayRef> s=c10::nullopt, at::IntArrayRef dim={-2,-1}, c10::optional<c10::string_view> norm=c10::nullopt);
TORCH_API at::Tensor & fft_rfft2_out(const at::Tensor & self, c10::optional<at::IntArrayRef> s, at::IntArrayRef dim, c10::optional<c10::string_view> norm, at::Tensor & out);
TORCH_API at::Tensor fft_irfft2(const at::Tensor & self, c10::optional<at::IntArrayRef> s=c10::nullopt, at::IntArrayRef dim={-2,-1}, c10::optional<c10::string_view> norm=c10::nullopt);
TORCH_API at::Tensor & fft_irfft2_out(const at::Tensor & self, c10::optional<at::IntArrayRef> s, at::IntArrayRef dim, c10::optional<c10::string_view> norm, at::Tensor & out);
TORCH_API at::Tensor fft_fftn(const at::Tensor & self, c10::optional<at::IntArrayRef> s=c10::nullopt, c10::optional<at::IntArrayRef> dim=c10::nullopt, c10::optional<c10::string_view> norm=c10::nullopt);
TORCH_API at::Tensor & fft_fftn_out(const at::Tensor & self, c10::optional<at::IntArrayRef> s, c10::optional<at::IntArrayRef> dim, c10::optional<c10::string_view> norm, at::Tensor & out);
TORCH_API at::Tensor fft_ifftn(const at::Tensor & self, c10::optional<at::IntArrayRef> s=c10::nullopt, c10::optional<at::IntArrayRef> dim=c10::nullopt, c10::optional<c10::string_view> norm=c10::nullopt);
TORCH_API at::Tensor & fft_ifftn_out(const at::Tensor & self, c10::optional<at::IntArrayRef> s, c10::optional<at::IntArrayRef> dim, c10::optional<c10::string_view> norm, at::Tensor & out);
TORCH_API at::Tensor fft_rfftn(const at::Tensor & self, c10::optional<at::IntArrayRef> s=c10::nullopt, c10::optional<at::IntArrayRef> dim=c10::nullopt, c10::optional<c10::string_view> norm=c10::nullopt);
TORCH_API at::Tensor & fft_rfftn_out(const at::Tensor & self, c10::optional<at::IntArrayRef> s, c10::optional<at::IntArrayRef> dim, c10::optional<c10::string_view> norm, at::Tensor & out);
TORCH_API at::Tensor fft_irfftn(const at::Tensor & self, c10::optional<at::IntArrayRef> s=c10::nullopt, c10::optional<at::IntArrayRef> dim=c10::nullopt, c10::optional<c10::string_view> norm=c10::nullopt);
TORCH_API at::Tensor & fft_irfftn_out(const at::Tensor & self, c10::optional<at::IntArrayRef> s, c10::optional<at::IntArrayRef> dim, c10::optional<c10::string_view> norm, at::Tensor & out);
TORCH_API at::Tensor fft_fftfreq(int64_t n, double d=1.0, c10::optional<at::ScalarType> dtype={}, c10::optional<at::Layout> layout={}, c10::optional<at::Device> device={}, c10::optional<bool> pin_memory={});
TORCH_API at::Tensor & fft_fftfreq_out(int64_t n, double d, at::Tensor & out);
TORCH_API at::Tensor fft_rfftfreq(int64_t n, double d=1.0, c10::optional<at::ScalarType> dtype={}, c10::optional<at::Layout> layout={}, c10::optional<at::Device> device={}, c10::optional<bool> pin_memory={});
TORCH_API at::Tensor & fft_rfftfreq_out(int64_t n, double d, at::Tensor & out);
TORCH_API at::Tensor fft_fftshift(const at::Tensor & self, c10::optional<at::IntArrayRef> dim=c10::nullopt);
TORCH_API at::Tensor fft_ifftshift(const at::Tensor & self, c10::optional<at::IntArrayRef> dim=c10::nullopt);
TORCH_API ::std::tuple<at::Tensor,at::Tensor> linalg_cholesky_ex(const at::Tensor & self, bool upper=false, bool check_errors=false);
TORCH_API ::std::tuple<at::Tensor &,at::Tensor &> linalg_cholesky_ex_out(const at::Tensor & self, bool upper, bool check_errors, at::Tensor & L, at::Tensor & info);
TORCH_API at::Tensor linalg_cholesky(const at::Tensor & self, bool upper=false);
TORCH_API at::Tensor & linalg_cholesky_out(const at::Tensor & self, bool upper, at::Tensor & out);
TORCH_API at::Tensor linalg_det(const at::Tensor & self);
TORCH_API at::Tensor & linalg_det_out(const at::Tensor & self, at::Tensor & out);
TORCH_API at::Tensor det(const at::Tensor & self);
TORCH_API ::std::tuple<at::Tensor,at::Tensor,at::Tensor> _det_lu_based_helper(const at::Tensor & self);
TORCH_API at::Tensor _det_lu_based_helper_backward_helper(const at::Tensor & det_grad, const at::Tensor & det, const at::Tensor & self, const at::Tensor & lu, const at::Tensor & pivs);
TORCH_API ::std::tuple<at::Tensor,at::Tensor,at::Tensor,at::Tensor> linalg_lstsq(const at::Tensor & self, const at::Tensor & b, c10::optional<double> rcond=c10::nullopt, c10::optional<c10::string_view> driver=c10::nullopt);
TORCH_API ::std::tuple<at::Tensor &,at::Tensor &,at::Tensor &,at::Tensor &> linalg_lstsq_out(const at::Tensor & self, const at::Tensor & b, c10::optional<double> rcond, c10::optional<c10::string_view> driver, at::Tensor & solution, at::Tensor & residuals, at::Tensor & rank, at::Tensor & singular_values);
TORCH_API at::Tensor linalg_matmul(const at::Tensor & self, const at::Tensor & other);
TORCH_API at::Tensor & linalg_matmul_out(const at::Tensor & self, const at::Tensor & other, at::Tensor & out);
TORCH_API ::std::tuple<at::Tensor,at::Tensor> linalg_slogdet(const at::Tensor & self);
TORCH_API ::std::tuple<at::Tensor &,at::Tensor &> linalg_slogdet_out(const at::Tensor & self, at::Tensor & sign, at::Tensor & logabsdet);
TORCH_API ::std::tuple<at::Tensor,at::Tensor> linalg_eig(const at::Tensor & self);
TORCH_API ::std::tuple<at::Tensor &,at::Tensor &> linalg_eig_out(const at::Tensor & self, at::Tensor & eigenvalues, at::Tensor & eigenvectors);
TORCH_API at::Tensor linalg_eigvals(const at::Tensor & self);
TORCH_API at::Tensor & linalg_eigvals_out(const at::Tensor & self, at::Tensor & out);
TORCH_API ::std::tuple<at::Tensor,at::Tensor> linalg_eigh(const at::Tensor & self, c10::string_view UPLO="L");
TORCH_API ::std::tuple<at::Tensor &,at::Tensor &> linalg_eigh_out(const at::Tensor & self, c10::string_view UPLO, at::Tensor & eigvals, at::Tensor & eigvecs);
TORCH_API at::Tensor linalg_eigvalsh(const at::Tensor & self, c10::string_view UPLO="L");
TORCH_API at::Tensor & linalg_eigvalsh_out(const at::Tensor & self, c10::string_view UPLO, at::Tensor & out);
TORCH_API at::Tensor linalg_householder_product(const at::Tensor & input, const at::Tensor & tau);
TORCH_API at::Tensor & linalg_householder_product_out(const at::Tensor & input, const at::Tensor & tau, at::Tensor & out);
TORCH_API at::Tensor & _linalg_inv_out_helper_cpu(at::Tensor & self, at::Tensor & infos_lu, at::Tensor & infos_getri);
TORCH_API at::Tensor & _linalg_inv_out_helper_cuda(at::Tensor & self, at::Tensor & infos_lu, at::Tensor & infos_getri);
TORCH_API ::std::tuple<at::Tensor,at::Tensor> linalg_inv_ex(const at::Tensor & self, bool check_errors=false);
TORCH_API ::std::tuple<at::Tensor &,at::Tensor &> linalg_inv_ex_out(const at::Tensor & self, bool check_errors, at::Tensor & inverse, at::Tensor & info);
TORCH_API at::Tensor linalg_inv(const at::Tensor & self);
TORCH_API at::Tensor & linalg_inv_out(const at::Tensor & self, at::Tensor & out);
TORCH_API at::Tensor inner(const at::Tensor & self, const at::Tensor & other);
TORCH_API at::Tensor & inner_out(const at::Tensor & self, const at::Tensor & other, at::Tensor & out);
TORCH_API at::Tensor outer(const at::Tensor & self, const at::Tensor & vec2);
TORCH_API at::Tensor & outer_out(const at::Tensor & self, const at::Tensor & vec2, at::Tensor & out);
TORCH_API at::Tensor ger(const at::Tensor & self, const at::Tensor & vec2);
TORCH_API at::Tensor & ger_out(const at::Tensor & self, const at::Tensor & vec2, at::Tensor & out);
TORCH_API at::Tensor linalg_norm(const at::Tensor & self, const c10::optional<at::Scalar> & ord=c10::nullopt, c10::optional<at::IntArrayRef> dim=c10::nullopt, bool keepdim=false, c10::optional<at::ScalarType> dtype=c10::nullopt);
TORCH_API at::Tensor & linalg_norm_out(const at::Tensor & self, const c10::optional<at::Scalar> & ord, c10::optional<at::IntArrayRef> dim, bool keepdim, c10::optional<at::ScalarType> dtype, at::Tensor & out);
TORCH_API at::Tensor linalg_norm(const at::Tensor & self, c10::string_view ord, c10::optional<at::IntArrayRef> dim=c10::nullopt, bool keepdim=false, c10::optional<at::ScalarType> dtype=c10::nullopt);
TORCH_API at::Tensor & linalg_norm_out(const at::Tensor & self, c10::string_view ord, c10::optional<at::IntArrayRef> dim, bool keepdim, c10::optional<at::ScalarType> dtype, at::Tensor & out);
TORCH_API at::Tensor linalg_vector_norm(const at::Tensor & self, const at::Scalar & ord=2, c10::optional<at::IntArrayRef> dim=c10::nullopt, bool keepdim=false, c10::optional<at::ScalarType> dtype=c10::nullopt);
TORCH_API at::Tensor & linalg_vector_norm_out(const at::Tensor & self, const at::Scalar & ord, c10::optional<at::IntArrayRef> dim, bool keepdim, c10::optional<at::ScalarType> dtype, at::Tensor & out);
TORCH_API at::Tensor linalg_matrix_norm(const at::Tensor & self, const at::Scalar & ord, at::IntArrayRef dim={-2,-1}, bool keepdim=false, c10::optional<at::ScalarType> dtype=c10::nullopt);
TORCH_API at::Tensor & linalg_matrix_norm_out(const at::Tensor & self, const at::Scalar & ord, at::IntArrayRef dim, bool keepdim, c10::optional<at::ScalarType> dtype, at::Tensor & out);
TORCH_API at::Tensor linalg_matrix_norm(const at::Tensor & self, c10::string_view ord="fro", at::IntArrayRef dim={-2,-1}, bool keepdim=false, c10::optional<at::ScalarType> dtype=c10::nullopt);
TORCH_API at::Tensor & linalg_matrix_norm_out(const at::Tensor & self, c10::string_view ord, at::IntArrayRef dim, bool keepdim, c10::optional<at::ScalarType> dtype, at::Tensor & out);
TORCH_API ::std::tuple<at::Tensor,at::Tensor,at::Tensor> linalg_svd(const at::Tensor & self, bool full_matrices=true);
TORCH_API ::std::tuple<at::Tensor &,at::Tensor &,at::Tensor &> linalg_svd_out(const at::Tensor & self, bool full_matrices, at::Tensor & U, at::Tensor & S, at::Tensor & Vh);
TORCH_API at::Tensor linalg_svdvals(const at::Tensor & input);
TORCH_API at::Tensor & linalg_svdvals_out(const at::Tensor & input, at::Tensor & out);
TORCH_API at::Tensor linalg_cond(const at::Tensor & self, const c10::optional<at::Scalar> & p=c10::nullopt);
TORCH_API at::Tensor & linalg_cond_out(const at::Tensor & self, const c10::optional<at::Scalar> & p, at::Tensor & out);
TORCH_API at::Tensor linalg_cond(const at::Tensor & self, c10::string_view p);
TORCH_API at::Tensor & linalg_cond_out(const at::Tensor & self, c10::string_view p, at::Tensor & out);
TORCH_API at::Tensor linalg_pinv(const at::Tensor & self, double rcond=1e-15, bool hermitian=false);
TORCH_API at::Tensor & linalg_pinv_out(const at::Tensor & self, double rcond, bool hermitian, at::Tensor & out);
TORCH_API at::Tensor linalg_pinv(const at::Tensor & self, const at::Tensor & rcond, bool hermitian=false);
TORCH_API at::Tensor & linalg_pinv_out(const at::Tensor & self, const at::Tensor & rcond, bool hermitian, at::Tensor & out);
TORCH_API at::Tensor linalg_solve(const at::Tensor & input, const at::Tensor & other);
TORCH_API at::Tensor & linalg_solve_out(const at::Tensor & input, const at::Tensor & other, at::Tensor & out);
TORCH_API at::Tensor linalg_tensorinv(const at::Tensor & self, int64_t ind=2);
TORCH_API at::Tensor & linalg_tensorinv_out(const at::Tensor & self, int64_t ind, at::Tensor & out);
TORCH_API at::Tensor linalg_tensorsolve(const at::Tensor & self, const at::Tensor & other, c10::optional<at::IntArrayRef> dims=c10::nullopt);
TORCH_API at::Tensor & linalg_tensorsolve_out(const at::Tensor & self, const at::Tensor & other, c10::optional<at::IntArrayRef> dims, at::Tensor & out);
TORCH_API ::std::tuple<at::Tensor,at::Tensor> linalg_qr(const at::Tensor & self, c10::string_view mode="reduced");
TORCH_API ::std::tuple<at::Tensor &,at::Tensor &> linalg_qr_out(const at::Tensor & self, c10::string_view mode, at::Tensor & Q, at::Tensor & R);
TORCH_API ::std::tuple<at::Tensor,at::Tensor> _linalg_qr_helper_default(const at::Tensor & self, c10::string_view mode);
TORCH_API ::std::tuple<at::Tensor,at::Tensor> _linalg_qr_helper_cuda(const at::Tensor & self, c10::string_view mode);
TORCH_API at::Tensor linalg_matrix_power(const at::Tensor & self, int64_t n);
TORCH_API at::Tensor & linalg_matrix_power_out(const at::Tensor & self, int64_t n, at::Tensor & out);
TORCH_API at::Tensor linalg_matrix_rank(const at::Tensor & self, c10::optional<double> tol=c10::nullopt, bool hermitian=false);
TORCH_API at::Tensor & linalg_matrix_rank_out(const at::Tensor & self, c10::optional<double> tol, bool hermitian, at::Tensor & out);
TORCH_API at::Tensor linalg_matrix_rank(const at::Tensor & input, const at::Tensor & tol, bool hermitian=false);
TORCH_API at::Tensor & linalg_matrix_rank_out(const at::Tensor & input, const at::Tensor & tol, bool hermitian, at::Tensor & out);
TORCH_API at::Tensor linalg_multi_dot(at::TensorList tensors);
TORCH_API at::Tensor & linalg_multi_dot_out(at::TensorList tensors, at::Tensor & out);
TORCH_API at::Tensor _test_serialization_subcmul(const at::Tensor & self, const at::Tensor & other, const at::Scalar & alpha=1);
TORCH_API at::Tensor _test_optional_intlist(const at::Tensor & values, c10::optional<at::IntArrayRef> addends);
TORCH_API at::Tensor _test_optional_intlist(const at::Tensor & values, c10::optional<at::IntArrayRef> addends);
TORCH_API at::Tensor _test_optional_floatlist(const at::Tensor & values, c10::optional<at::ArrayRef<double>> addends);
TORCH_API at::Tensor _test_string_default(const at::Tensor & dummy, c10::string_view a="\"'\\", c10::string_view b="\"'\\");
TORCH_API at::Tensor _test_ambiguous_defaults(const at::Tensor & dummy, int64_t a=1, int64_t b=1);
TORCH_API at::Tensor _test_ambiguous_defaults(const at::Tensor & dummy, int64_t a=2, c10::string_view b="2");
TORCH_API at::Tensor segment_reduce_kernel(const at::Tensor & data, c10::string_view reduce, const c10::optional<at::Tensor> & lengths={}, const c10::optional<at::Tensor> & indices={}, int64_t axis=0, bool unsafe=false, const c10::optional<at::Scalar> & initial=c10::nullopt);
TORCH_API at::Tensor _segment_reduce_backward_kernel(const at::Tensor & grad, const at::Tensor & output, const at::Tensor & data, c10::string_view reduce, const c10::optional<at::Tensor> & lengths={}, int64_t axis=0);
TORCH_API at::Tensor pad_sequence(at::TensorList sequences, bool batch_first=false, double padding_value=0.0);
TORCH_API at::Tensor flatten_dense_tensors(at::TensorList tensors);
TORCH_API ::std::vector<at::Tensor> unflatten_dense_tensors(const at::Tensor & flat, at::TensorList tensors);

} // namespace native
} // namespace at
