# Overview


# Architecture
![alt text](docs/image.png)

# Debug

## 压测工具
```bash
./client/asr_client_main --h=127.0.0.1 --p=10086 --c=1000 --t=100

--h: asr server host. default 127.0.0.1  
--p: asr server port. default 10086  
--c: concurrency numbers. default: 3  
--t: run duration-time. default: 10s. 实际运行会比10ms多出1分钟-要做收尾操作. 当--d生效的时候，这个参数失效。
--f: audio wav's file.  
--d: audio wav's path. 当指定--f参数的时候，这个参数就失效。  
--m: 是否用多通道。0, 否； 1，用。 default: 1. 多连接通道，规避使用多路(流通道)复用单连接通道  
--v: 推送版本。default: v1
    v1： 1个线程, 循环使用单连接单音频流通道循环推送1个音频文件。 
    v2：1个线程,循环推送音频文件，每个音频文件单独创建音频流通道推送.—— asr 处理时，会有一段时间的空闲。

```


音频处理参数: client/conf/config.ini

```bash
# cuishou or dianxiao
scene_type: 租户信息, cuishou or dianxiao
enable_sent_volume: xxxx
enable_sent_speech_rate: ??
# the unit is ms
max_sentence_silence = 600


```

example
```bash
## stage cycle the sample
./client/asr_client_main  --h=127.0.0.1  --c=10 --t=120 --m=1 --d=/data/oceanus_share/cron-asr/wav_sample_pool

## stress 1 sample
./client/asr_client_main --f=/data/oceanus_share/data/audio/wav-file/4014.wav --h=127.0.0.1  --c=10 --t=120 --m=1 


## stress 1 sample in version V2
./client/asr_client_main  --h=127.0.0.1  --c=10 --t=120 --m=1 --d=/data/oceanus_share/cron-asr/wav_sample_pool --v=v2

./client/asr_client_main --f=/data/oceanus_share/data/audio/wav-file/4014.wav --h=127.0.0.1  --c=1 --t=120 --m=1  --v=v2

```




# FAQ
__1、 多线程单通道问题？__<br/>
创建通道的时候，采用不用的参数。若参数一样，通道就会一样。参照代码样例 client/asr_client_test.cc 中的 CreateAndUseChannel(int keepalive_ms)方法


# References

[ASR流量压测工具说明](https://docs.qq.com/doc/DZlBEdkFRQmJJSGl6)

## FS SDK Client

![fs-sdk_from_zhenhua](docs/fs_sdk.png)

![alt text](docs/fs-sdk_client_2.png)

## Grpc connect state

![alt text](docs/grpc_connect_status.png)

## Client & Server tcp info
![alt text](docs/client_server_tcp_info.png)


## Latency Sample info

![alt text](docs/audio_tag.png)