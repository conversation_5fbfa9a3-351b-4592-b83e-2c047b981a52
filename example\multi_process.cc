#include <iostream>
#include <unistd.h>
#include <sys/wait.h>

void doWork(int id)
{
    std::cout << "Process " << id << " started, PID: " << getpid() << std::endl;
    // 模拟计算密集型任务
    for (volatile int i = 0; i < 100000000; ++i)
        ;
    std::cout << "Process " << id << " finished" << std::endl;
}

int main()
{
    const int numProcesses = 10;
    pid_t pids[numProcesses];

    for (int i = 0; i < numProcesses; ++i)
    {
        pid_t pid = fork();
        if (pid == -1)
        {
            perror("fork");
            return 1;
        }
        else if (pid == 0)
        {
            doWork(i + 1);
            return 0;
        }
        else
        {
            pids[i] = pid;
        }
    }

    // 父进程等待所有子进程结束
    for (int i = 0; i < numProcesses; ++i)
    {
        int status;
        pid_t wpid = waitpid(pids[i], &status, 0);
        if (wpid == -1)
        {
            perror("waitpid");
            return 1;
        }
        if (WIFEXITED(status))
        {
            std::cout << "Process " << wpid << " exited with status " << WEXITSTATUS(status) << std::endl;
        }
        else
        {
            std::cout << "Process " << wpid << " did not exit normally" << std::endl;
        }
    }

    std::cout << "All processes have finished" << std::endl;
    return 0;
}