#ifndef GRPC_HANDLER_H_
#define GRPC_HANDLER_H_

#include <condition_variable>
#include "grpc/triton_grpc_client.h"
#include "grpc/grpc_session_id_map.h"
#include "utils/fifo.h"
#include "utils/timer.h"
#include "utils/utils.h"
#include "utils/value.h"
#include "utils/decibel_analyzer.h"

#define MIN_LENGTH_PAUSE 600
#define MAX_LENGTH_PAUSE 1200
#define MAX_LENGTH_SESSION_ID 300
#define CHUNK_LENGTH_STREAM_PCM 320

namespace _360sk {

enum DecodeState {
  kEndBatch = 0x00,  // End of current decoding batch, normal case
  kEndpoint = 0x01,  // Endpoint is detected
  kEndFeats = 0x02   // All feature is decoded
};

class GrpcConnectionHandler {
 public:
  GrpcConnectionHandler(
      grpc::ServerReaderWriter<com::qihoo::shuke::aibot::asr::proto::Response,
                               com::qihoo::shuke::aibot::asr::proto::Request>*
          stream,
      std::shared_ptr<com::qihoo::shuke::aibot::asr::proto::Request> request,
      std::shared_ptr<com::qihoo::shuke::aibot::asr::proto::Response> response,
      std::shared_ptr<GRPCSessionMap> sessionID2InitRequest,
      std::shared_ptr<GRPCSessionIdMap> sessionIdMapping,
      std::string triton_grpc_server_url_, bool triton_grpc_client_verbose_,
      std::shared_ptr<Fifo<char>> session_id_ptr);

  void operator()();

 private:
  // int NumRequriedFrames(bool start);

  DecodeState ChunkDecoding(bool new_sentence_start_or_not);
  bool InvalidStreamRequestCheck();
  void OnSpeechEnd();
  void OnSpeechData();
  void OnPartialResult();
  void OnFinalResult();
  void WordRecognitionResult();
  void DecodeThreadFunc();
  void CheckDecibelAndPush();
  //   void TritonGrpcClientStart();
  void SerializeResult(bool finish);
  //   void Postprocess(const std::shared_ptr<tc::InferResult> result);
  void OnErrorMessage(int error_code, std::string& error_message);
  bool AsrStreamSend(tc::InferOptions& options, int begin, int data_len,
                     int require_chunk_len);
  grpc::ServerReaderWriter<com::qihoo::shuke::aibot::asr::proto::Response,
                           com::qihoo::shuke::aibot::asr::proto::Request>*
      stream_;
  std::shared_ptr<com::qihoo::shuke::aibot::asr::proto::Request> request_;
  std::shared_ptr<com::qihoo::shuke::aibot::asr::proto::Response> response_;

  AsrResult parseResult_;
  bool compute_ser_ = false;
  bool signal_end_ = false;
  bool triton_grpc_client_verbose_;
  bool blank_endpoint_ = false;
  bool session_start_or_not_ = true;
  bool continuous_decoding_ = true;

  int nbest_ = 1;
  int num_invalid_request_ = 0;
  int sentence_begin_time_;
  int sentence_end_time_;
  int chunk_req_id_ = 0;
  int vad_downsampline_rate_ = 5;
  int nframes_decoded_ = 0;
  int num_res_recieved_ = 0;
  int num_vad_req_ = 0;
  int num_vad_res_ = 0;
  int vad_idx_ = 0;
  int num_req_sending_ = 0;
  int num_pcm_combing_ = 0;
  int ctc_endpoint_threshold_ = 10;
  int current_decibel_value_ = 0;
  int64_t last_decibel_push_time_ = 0;  // 上次分贝推送时间戳
  int decibel_push_interval_ms_ = 1000;  // 分贝推送间隔（毫秒）

  size_t OnFinalResult_timer_;
  size_t OnSpeechData_timer_;
  size_t SerializeResult_timer_;

  std::string session_id_;
  std::string asr_session_id_;
  std::string asr_endpoint_result_;
  std::string streaming_vad_model_name_ = "streaming_vad";
  std::string streaming_asr_model_name_ = "streaming_wenet";
  std::string triton_grpc_server_url_;

  std::vector<float> accumulation_pcm_data_;

  std::shared_ptr<TritonGrpcClient> triton_grpc_client_;
  std::shared_ptr<AsrChunkReqToResMap> asr_chunk_req_to_res_map_;
  std::shared_ptr<GRPCSessionMap> sessionID2InitRequest_;
  std::shared_ptr<GRPCSessionIdMap> sessionIdMapping_;
  std::unique_ptr<RequestConf> request_conf_;
  std::shared_ptr<Fifo<char>> session_id_ptr_;
  std::shared_ptr<std::thread> decode_thread_;
  mutable std::mutex mutex_;
};

}  // namespace _360sk
#endif
