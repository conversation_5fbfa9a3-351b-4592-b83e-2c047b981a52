#include <queue>
#include <memory>
#include "client/asr_client_pool.h"

namespace _360sk {

AsrClientPool::AsrClientPool(const std::string host, int port, int pool_size, int keepalive_ms)
    :host_(host), port_(port), pool_size_(pool_size), keepalive_ms_(keepalive_ms){
    if (pool_size_< 1) {
        std::cout << "param exception! pool_size:" << pool_size_ << std::endl;
        return;
    }
    for (int i = 0; i < pool_size_ ; i++) {
        keepalive_ms_ += i + 100;
        std::shared_ptr<_360sk::AsrClient> asrClient(new AsrClient(host_, port_, keepalive_ms_, 1));
        if (0 == asrClient->Connect()) {
            asrClient->is_used = true;
            asrClient->pool_seq = i;
            client_pool.push(asrClient);
        }
    }
}

std::shared_ptr<_360sk::AsrClient> AsrClientPool::Acquire() {
    std::unique_lock<std::mutex> lock(pool_mutex_);

    cond_var_.wait(lock, [this] { 
        return client_pool.size() > 1; 
    });

    try {
        if (!client_pool.empty()) {
            std::shared_ptr<_360sk::AsrClient> asrClient = client_pool.front();
            asrClient->is_used = true;
            client_pool.pop();
            return asrClient;
        }
    } catch (const std::exception& e) {
        std::cerr << "Exception caught: " << e.what() << std::endl;
        return nullptr;
    } catch (...) {
        std::cerr << "Unknown exception caught!" << std::endl;
        return nullptr;
    }

    return nullptr;
}
bool AsrClientPool::Release(std::shared_ptr<_360sk::AsrClient> client) {
    std::lock_guard<std::mutex> lock(pool_mutex_);

    client->is_used = false;
    client_pool.push(client);

    cond_var_.notify_one(); // 唤醒一个等待的线程
    return false;
}

};