# 设置编译类型为调试模式, 打出coredump
set(CMAKE_BUILD_TYPE Debug)

# Specify the C++ standard
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED True)


add_library(asr_client STATIC
    asr_client.h
    asr_client.cc
    asr_client_pool.h
    asr_client_pool.cc
    audio_helper.h
    audio_helper.cc
    tools_util.h
    tools_util.cc
    config_parser.h
    config_parser.cc
)


# Add Google Test ========================================================================
find_package(GTest REQUIRED)

# Create an executable from the source files
add_executable(
    asr_client_test
    asr_client_test.cc
)

# Link test executable against gtest & gtest_main
target_link_libraries(asr_client_test PUBLIC ${GTEST_LIBRARIES} asr_client pthread asr_grpc grpcclient utils uuid mybase)

# ConfigParse ========================================================================
# Create an executable from the source files
add_executable(
    config_parser_test
    config_parser_test.cc
)

# 使用 configure_file 将配置文件复制到构建目录
set(CONF_FILE "${CMAKE_SOURCE_DIR}/client/conf/config.ini")
set(DEST_FILE "${CMAKE_BINARY_DIR}/client/conf/config.ini")
configure_file(${CONF_FILE} ${DEST_FILE} COPYONLY)

# Link test executable against gtest & gtest_main
target_link_libraries(config_parser_test PUBLIC ${GTEST_LIBRARIES} asr_client pthread asr_grpc grpcclient utils uuid mybase)



# Asr Client Main========================================================================
add_executable(
    asr_client_main
    asr_client_main.cc
    config_parser.cc
)

# Link test executable against gtest & gtest_main
target_link_libraries(asr_client_main PUBLIC asr_client pthread asr_grpc grpcclient utils uuid mybase)


add_executable(
    asr_client_latency_test_main
    asr_client_latency_test_main.cc
)

target_link_libraries(asr_client_latency_test_main PUBLIC asr_grpc signal utils uuid asr_client)
# 添加源文件和头文件
#add_library(tools_util STATIC tools_util.cc)
#add_library(audio_helper STATIC helpers/audio_helper.cc)
# Link test executable against gtest & gtest_main
add_executable(
  tools_util_test 
  tools_util_test.cc
)
add_executable(
    asr_client_pool_test 
    asr_client_pool_test.cc
)
target_link_libraries(asr_client_pool_test PUBLIC ${GTEST_LIBRARIES} asr_client pthread asr_grpc grpcclient)
target_link_libraries(tools_util_test PRIVATE asr_client ${GTEST_LIBRARIES} ${GTEST_MAIN_LIBRARIES})

add_executable(
  audio_helper_test 
  audio_helper_test.cc
)
#target_link_libraries(audio_helper PUBLIC asr_client signal)
target_link_libraries(audio_helper_test PRIVATE GTest::GTest GTest::Main signal asr_client signal)
add_test(NAME AudioSenderTest COMMAND audio_sender_test)

# Link Google Test libraries to the test executable
# target_link_libraries(
  # client_grpc_test
  # gtest
  # gtest_main
# )

# Add tests
# add_test(
  # NAME client_test
  # COMMAND client_test
# )

# Optionally, link libraries if needed
# target_link_libraries(MyExecutable SomeLibrary)