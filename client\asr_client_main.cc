#include <chrono>
#include <filesystem>
#include <iostream>
#include <thread>
#include <vector>

#include "client/asr_client.h"
#include "client/audio_helper.h"
#include "client/config_parser.h"
#include "utils/utils.h"
// #include "client/audio_helper.h"
// #include "utils/utils.h"

using namespace _360sk;
using namespace std;
ConfigParser config;

// 提取文件名中的指定部分（不包括文件扩展名）
std::string extractFileNamePart(const std::string& file_path) {
    // 找到最后一个 '/' 符号的位置
    size_t last_slash = file_path.find_last_of("/\\");
    // 找到最后一个 '.' 符号的位置
    size_t last_dot = file_path.find_last_of('.');

    // 检查是否找到了 '/' 和 '.'，并且 '.' 位于 '/' 之后
    if (last_slash == std::string::npos) last_slash = -1; // 没找到就从头开始
    if (last_dot == std::string::npos || last_dot < last_slash) last_dot = file_path.size(); // 没找到或在 '/' 之前就不管

    // 提取文件名的部分，不包括扩展名
    std::string file_name = file_path.substr(last_slash + 1, last_dot - last_slash - 1);

    return file_name;
}

int InitClient(AsrClient* asrClient, string session_id) {
    int res_code;

    bool enable_sent_volume =
        std::stoi(config.get("audio.enable_sent_volume", "0")) == 0 ? false
                                                                    : true;
    bool enable_sent_speech_rate =
        std::stoi(config.get("audio.enable_sent_speech_rate", "0")) == 0 ? false
                                                                         : true;
    int max_sentence_silence =
        std::stoi(config.get("audio.max_sentence_silence", "600"));
    string scene_type = config.get("audio.scene_type", "cuishou");

    // asrClient = new AsrClient(host, port, keepalive_ms);
    if (0 != asrClient->Connect()) {
        SHUKE_WARN << "SendData:Connect failure!";
        return 2;
    }

    res_code =
        asrClient->Initialize(session_id, scene_type, max_sentence_silence,
                              enable_sent_volume, enable_sent_speech_rate);
    if (0 != res_code) {
        SHUKE_WARN << "SendData:Initialize failure! session_id:" << session_id;
        return 3;
    }
    res_code = asrClient->SuspendedResponse();
    if (0 != res_code) {
        SHUKE_WARN << "SendData:SuspenedResponse failure! session_id:"
                   << session_id;
        return 4;
    }
    return 0;
}

int SendDataV1(int threadno, string host, int port, string wav_file,
               int duration_time_s, int more_channel, int concurrency,int uuid_session) {
    string session_id = "";
    if(uuid_session==1){
        session_id = "AsrClientMain_" + generate_unique_id();
    }else{
        session_id = extractFileNamePart(wav_file);
        session_id =  "AsrClientMain_" + session_id;
    }
    SHUKE_LOG << "SendDataV1,********************session_id:"<< session_id;

    int keepalive_ms = more_channel == 1 ? (threadno + 1) * 1000 : 1000;
    AsrClient asrClient(host, port, keepalive_ms, concurrency);
    if (0 != InitClient(&asrClient, session_id)) {
        SHUKE_WARN << "Init Client failure. host = " << host << ",port" << port;
        return 1;
    }

    // sampling
    float audio_interval =
        std::stof(config.get("audio.audio_interval", "0.02"));
    int audio_sample_rate =
        std::stoi(config.get("audio.audio_sample_rate", "8000"));
    AudioSender audio_sender(audio_interval, audio_sample_rate);
    SHUKE_LOG << "audio sender wav_file:" << wav_file;
    int s_code = audio_sender.setAudioData(wav_file);
    if (0 != s_code) {
        SHUKE_WARN << "audio sender set audio data failure. wav_file"
                   << wav_file;
        return 2;
    }
    SHUKE_LOG << ".audio sender wav_file:" << wav_file;
    const auto& stored_data = audio_sender.getAudioData();
    const auto& stored_left_data = audio_sender.getAudioLeftData();
    const auto& stored_right_data = audio_sender.getAudioRightData();

    // send data
    auto start_time = std::chrono::steady_clock::now();
    auto end_time =
        start_time + std::chrono::milliseconds(duration_time_s * 1000);
    do {
        if (stored_data.size() > 0) {
            int start = 0;
            for (const auto& chunk : stored_data) {
                asrClient.SendData(chunk.data(),
                                   chunk.size() * sizeof(int16_t));
                asrClient.ResetStartTime((start + 160) / 8);
                std::this_thread::sleep_for(
                    std::chrono::milliseconds(static_cast<int>(20)));
                start += audio_interval * audio_sample_rate;
            }
        }
        if (stored_left_data.size() > 0) {
            int start = 0;
            for (const auto& chunk : stored_left_data) {
                asrClient.SendData(chunk.data(),
                                   chunk.size() * sizeof(int16_t));
                asrClient.ResetStartTime((start + 160) / 8);
                std::this_thread::sleep_for(
                    std::chrono::milliseconds(static_cast<int>(20)));
                start += audio_interval * audio_sample_rate;
            }
        }
        if (stored_right_data.size() > 0) {
            int start = 0;
            for (const auto& chunk : stored_right_data) {
                asrClient.SendData(chunk.data(),
                                   chunk.size() * sizeof(int16_t));
                asrClient.ResetStartTime((start + 160) / 8);
                std::this_thread::sleep_for(
                    std::chrono::milliseconds(static_cast<int>(20)));
                start += audio_interval * audio_sample_rate;
            }
        }

    } while (chrono::steady_clock::now() < end_time);
    std::this_thread::sleep_for(std::chrono::seconds(60));
    asrClient.Join();
    return 0;
}

// ======================================== V2: new stream connect
int SendDataASR(const vector<std::vector<int16_t>>& stored_data,
                int keepalive_ms, string host, int port, float audio_interval,
                int audio_sample_rate, int concurrency, int retry) {
    string session_id = "AsrClientMain_" + generate_unique_id();
    AsrClient asrClient(host, port, keepalive_ms, concurrency);
    if (0 != InitClient(&asrClient, session_id)) {
        int retry_t = retry + 1;
        if (retry_t < 100) {
            return SendDataASR(stored_data, keepalive_ms, host, port, audio_interval, audio_sample_rate, concurrency, retry_t);
        }
        SHUKE_WARN << "Init Client failure. host = " << host << ",port=" << port;
        return 1;
    }
    if (stored_data.size() == 0) {
        SHUKE_WARN << "stored data is empty!";
        return 2;
    }
    // sampling
    int start = 0;
    for (const auto& chunk : stored_data) {
        asrClient.SendData(chunk.data(), chunk.size() * sizeof(int16_t));
        asrClient.ResetStartTime((start + 160) / 8);
        std::this_thread::sleep_for(
            std::chrono::milliseconds(static_cast<int>(20)));
        start += audio_interval * audio_sample_rate;
    }
    asrClient.Join();

    return 0;
}

int SendDataV2(int threadno, string host, int port, string wav_file,
               int duration_time_s, int more_channel, int concurrency, int retry) {
    SHUKE_LOG << "SendDataV2";
    string session_id = "AsrClientMain_" + generate_unique_id();

    int keepalive_ms = more_channel == 1 ? (threadno + 1) * 2: 10000;
    // std::cout << "keepalive_ms:" << keepalive_ms << "" << std::endl;
    // return 0;
    AsrClient asrClient(host, port, keepalive_ms, concurrency);
    if (0 != InitClient(&asrClient, session_id)) {
        int retry_t = retry + 1;
        if (retry_t < 100) {
            return SendDataV2(threadno, host, port, wav_file, duration_time_s, more_channel, concurrency, retry_t);
        }
        SHUKE_WARN << "Init Client failure. host = " << host << ",port" << port;
        return 1;
    }

    // sampling
    float audio_interval =
        std::stof(config.get("audio.audio_interval", "0.02"));
    int audio_sample_rate =
        std::stoi(config.get("audio.audio_sample_rate", "8000"));
    AudioSender audio_sender(audio_interval, audio_sample_rate);
    SHUKE_LOG << "audio sender wav_file:" << wav_file;
    int s_code = audio_sender.setAudioData(wav_file);
    if (0 != s_code) {
        SHUKE_WARN << "audio sender set audio data failure. wav_file"
                   << wav_file;
        return 2;
    }
    SHUKE_LOG << ".audio sender wav_file:" << wav_file;
    const auto& stored_data = audio_sender.getAudioData();
    const auto& stored_left_data = audio_sender.getAudioLeftData();
    const auto& stored_right_data = audio_sender.getAudioRightData();

    // send data
    auto start_time = std::chrono::steady_clock::now();
    auto end_time =
        start_time + std::chrono::milliseconds(duration_time_s * 1000);
    do {
        if (stored_data.size() > 0) {
            SendDataASR(stored_data, keepalive_ms, host, port, audio_interval,
                        audio_sample_rate, concurrency, 1);
        }
        if (stored_left_data.size() > 0) {
            SendDataASR(stored_left_data, keepalive_ms, host, port,
                        audio_interval, audio_sample_rate, concurrency, 1);
        }
        if (stored_right_data.size() > 0) {
            SendDataASR(stored_right_data, keepalive_ms, host, port,
                        audio_interval, audio_sample_rate, concurrency, 1);
        }
        // std::this_thread::sleep_for(std::chrono::milliseconds(300));
    } while (chrono::steady_clock::now() < end_time);
    std::this_thread::sleep_for(std::chrono::seconds(60));
    asrClient.Join();
    return 0;
}
// ======================================== V2: new stream connect

void Execute(string host, int port, string wav_file, string wav_dir_path,
             int concurrency, int duration_time_s, int more_channel,
             string version) {
    SHUKE_LOG << "Execute, host:" << host << ",port:" << port << ",wav_file:"
              << ",wav_dir_path:" << wav_dir_path
              << ",concurrency:" << concurrency
              << ",duration_time_ms:" << duration_time_s
              << ", more_channel:" << more_channel << ",version:" << version;
    if (!wav_file.empty()) {  // file logic
        vector<thread> threads;
        for (int i = 0; i < concurrency; i++) {
            if (version == "v2") {
                threads.push_back(std::thread(SendDataV2, i, host, port,
                                              wav_file, duration_time_s,
                                              more_channel, concurrency, 1));
            } else {
                threads.push_back(std::thread(SendDataV1, i, host, port,
                                              wav_file, duration_time_s,
                                              more_channel, concurrency,1));
            }
        }
        SHUKE_LOG << "cocurrency:" << concurrency << ", actual threads:" << threads.size();

        for (auto& t : threads) {
            if (t.joinable()) {
                t.join();
            }
        }
        threads.clear();
    } else if (!wav_dir_path.empty()) {  // dir logic
        vector<string> files;
        for (const auto& entry : filesystem::directory_iterator(wav_dir_path)) {
            if (filesystem::is_regular_file(entry.status())) {
                files.push_back(wav_dir_path + "/" +
                                entry.path().filename().string());
            }
        }
        SHUKE_LOG << "dir<" << wav_dir_path << ">'s file total:" << files.size()
                  << std::endl;

        if (files.size() > 0) {
            vector<string> cc_files;
            for (string file_path : files) {
                cc_files.push_back(file_path);
                if (cc_files.size() >= concurrency) {
                    vector<thread> threads = vector<thread>();
                    for (int i = 0; i < cc_files.size(); i++) {
                        if (version == "v2") {
                            threads.push_back(std::thread(SendDataV2, i, host,
                                                          port, cc_files[i], 1,
                                                          more_channel, concurrency, 1));
                        } else {
                            threads.push_back(std::thread(SendDataV1, i, host,
                                                          port, cc_files[i], 1,
                                                          more_channel, concurrency,0));
                        }
                    }
                    SHUKE_LOG << "multi threads size:" << threads.size()
                              << ", cc_files size:" << cc_files.size();
                    for (auto& t : threads) {
                        if (t.joinable()) {
                            t.join();  // 确保线程可以 join
                        }
                    }
                    std::cout << "dir threads size:" << threads.size()
                              << ", cc_files size:" << cc_files.size()
                              << std::endl;
                    threads.clear();
                    cc_files.clear();
                    cc_files = vector<string>();
                    std::cout << ".dir threads size:" << threads.size()
                              << ", cc_files size:" << cc_files.size()
                              << std::endl;
                }
            }

            // Tie up loose ends
            if (cc_files.size() > 0) {
                if (cc_files.size() > 0) {
                    vector<thread> threads;
                    for (int i = 0; i < cc_files.size(); i++) {
                        if (version == "v2") {
                            threads.push_back(std::thread(SendDataV2, i, host,
                                                          port, cc_files[i], 1,
                                                          more_channel, concurrency, 1));
                        } else {
                            threads.push_back(std::thread(SendDataV1, i, host,
                                                          port, cc_files[i], 1,
                                                          more_channel, concurrency,0));
                        }
                    }
                    for (auto& t : threads) {
                        if (t.joinable()) {
                            t.join();
                        }
                    }
                    threads.clear();
                }
            }
        }
    }
}

int main(int argc, char* argv[]) {
    std::string host = "127.0.0.1", wav_file = "", wav_dir_path = "",
                version = "v1";
    int port = 10086, concurrency = 3, duration_time_s = 10, more_channel = 1;

    const char* usage = "asr grpc client conf";
    ParseOptions po(usage);
    po.Register("h", &host, "asr server host");
    po.Register("p", &port, "asr server host's port");
    po.Register("c", &concurrency, "concurrency numbers. defalut: 3");
    po.Register("t", &duration_time_s, "run duration-time. defalut 3s");
    po.Register("f", &wav_file, "audio wav's file.");
    po.Register("d", &wav_dir_path, "audio wav's path.");
    po.Register("m", &more_channel, "yes or no more tcp channel");
    po.Register("v", &version,
                "push version. v1: cycle send data. v2: new stream connect.");
    po.Read(argc, argv);

    SHUKE_LOG << "input params. host = " << host << ",port = " << port
              << ",concurrency = " << concurrency
              << ",duration_time_s = " << duration_time_s
              << ", wav_dir_path = " << wav_dir_path
              << ",wav_file = " << wav_file;
    if (wav_file.empty() && wav_dir_path.empty()) {
        SHUKE_WARN << "input params error. --f(wav_file) and --d(wav_dir_path) "
                      "is empty! ";
        return 1;
    } else if (!wav_file.empty() && !filesystem::exists(wav_file)) {
        SHUKE_WARN << "input params error. --f(" << wav_file
                   << ")  does not exists.";
        return 2;
    } else if (!wav_dir_path.empty() &&
               (!filesystem::exists(wav_dir_path) ||
                !filesystem::is_directory(wav_dir_path))) {
        SHUKE_WARN << "input params error. --d(" << wav_dir_path
                   << ")  does not exists.";
        return 3;
    }

    if (!config.load("client/conf/config.ini")) {
        SHUKE_WARN << "config lack! config file=client/conf/config.ini";
        return 4;
    }

    Execute(host, port, wav_file, wav_dir_path, concurrency, duration_time_s,
            more_channel, version);
    return 0;
}