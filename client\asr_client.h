#pragma once

#include <grpc/grpc.h>
#include <grpcpp/channel.h>
#include <grpcpp/client_context.h>
#include <grpcpp/create_channel.h>

#include <memory>
#include <string>
#include <thread>

#include "grpc/asr.grpc.pb.h"
#include "utils/timer.h"
#include "mybase/base.h"

using com::qihoo::shuke::aibot::asr::proto::SpeechRecognizer;
using com::qihoo::shuke::aibot::asr::proto::EventType;
using com::qihoo::shuke::aibot::asr::proto::InitializeRequest;
using com::qihoo::shuke::aibot::asr::proto::InitializeResponse;
using com::qihoo::shuke::aibot::asr::proto::Request;
using com::qihoo::shuke::aibot::asr::proto::Response;
using com::qihoo::shuke::aibot::asr::proto::SeceneType;
using com::qihoo::shuke::aibot::asr::proto::SpeechRecognizer;



namespace _360sk {
class AsrClient final {
public:
    // for the client pool
    bool is_used = false;
    int pool_seq = 0;

    AsrClient(std::string host, int port);
    AsrClient(std::string host, int port, int keepalive_ms, int concurrency);
    ~AsrClient();
    bool Test();
    int Connect();
    int Initialize(const std::string& session_id, const std::string& scene_type, 
        int max_sentence_silence, bool enable_sent_volume, bool enable_sent_speech_rate);
    int SendData(const void* data, size_t size);
    bool IsConnected();
    int SuspendedResponse();

    void Join();
    void Break();
    // listen remote server' response
    int ServerCallback();

    void ResetStartTime(int time);
private:
    // remote grpc server info
    std::string host_;
    int port_;
    int keepalive_ms_ = 3000;

    std::shared_ptr<grpc::Channel> channel_{nullptr};
    std::unique_ptr<SpeechRecognizer::Stub> stub_{nullptr};
    std::unique_ptr<grpc::ClientReaderWriter<Request, Response>> stream_{nullptr};
    std::shared_ptr<Request> request_{nullptr};
    std::shared_ptr<Response> response_{nullptr};
    std::string session_id_;
    std::unique_ptr<std::thread> t_{nullptr};
    std::shared_ptr<grpc::ClientContext> context_;

    // business's scene timer. for example, FS 20ms interval time
    Timer bz_timer_;
    int concurrency_;

};

}; // namespace _360sk
