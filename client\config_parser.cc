#include "client/config_parser.h"

namespace _360sk {

ConfigParser::ConfigParser() {}

// 解析配置文件的类
bool ConfigParser::load(const std::string filename) {
    std::ifstream file(filename);
    if (!file) {
        std::cerr << "Could not open file: " << filename << std::endl;
        return false;
    }

    std::string line, current_section;
    while (std::getline(file, line)) {
        // 去除空白字符
        line = trim(line);

        // 忽略空行和注释
        if (line.empty() || line[0] == '#') {
            continue;
        }

        // 处理节 (section) 标记
        if (line[0] == '[' && line.back() == ']') {
            current_section = line.substr(1, line.size() - 2);
        } else {
            // 解析键值对
            auto pos = line.find('=');
            if (pos != std::string::npos) {
                std::string key = trim(line.substr(0, pos));
                std::string value = trim(line.substr(pos + 1));
                config_data[current_section + "." + key] = value;
            }
        }
    }

    file.close();
    return true;
}

std::string ConfigParser::get(const std::string key,
                              const std::string default_value){
    auto it = config_data.find(key);
    if (it != config_data.end()) {
        return it->second;
    }
    return default_value;
}

std::string ConfigParser::trim(const std::string str) {
    size_t first = str.find_first_not_of(" \t");
    size_t last = str.find_last_not_of(" \t");
    if (first == std::string::npos || last == std::string::npos) {
        return "";
    }
    return str.substr(first, last - first + 1);
}
};  // namespace _360sk