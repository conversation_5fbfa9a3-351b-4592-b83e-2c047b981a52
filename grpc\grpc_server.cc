#include "grpc/grpc_server.h"
#include "utils/string_to_md_five_sum.h"
#include "utils/utils.h"

namespace _360sk {

// using namespace wenet;
using namespace com::qihoo::shuke::aibot::asr::proto;
using grpc::ServerContext;
using grpc::ServerReaderWriter;
using grpc::Status;

void GrpcServer::OnErrorMessage(InitializeResponse* init_response,
                                std::string& session_id, int& error_code,
                                std::string& error_message) {
  init_response->set_session(session_id);
  init_response->mutable_error_message()->set_error_code(error_code);
  init_response->mutable_error_message()->set_error_message(error_message);
}

Status GrpcServer::initialize(ServerContext* context,
                              const InitializeRequest* init_request,
                              InitializeResponse* init_response) {
  try {
    int init_error_code = static_cast<int>(ERROR_TYPE::kInitError);
    std::string session_id = init_request->session();
    if (session_id.empty()) {
      std::string error_message = "error session_id in the initRequest";
      SHUKE_WARN << session_id << " " << error_message;
      OnErrorMessage(init_response, session_id, init_error_code, error_message);
      return Status::CANCELLED;
    }
    if (sessionID2InitRequest_->Size() >= max_num_request_ &&
        num_threads_ > max_num_request_) {
      std::string warning_message =
          "session2map'size is " +
          std::to_string(sessionID2InitRequest_->Size()) +
          ", and num_threads is " + std::to_string(num_threads_);
      SHUKE_WARN << session_id << " " << warning_message;
      OnErrorMessage(init_response, session_id, init_error_code,
                     warning_message);
      return Status::CANCELLED;
    }
    std::unique_ptr<RequestConf> request_conf =
        std::make_unique<RequestConf>(init_request);
    request_conf->session_id = session_id;

    request_conf->nframes_first_chunk = nframes_first_chunk_;
    request_conf->nframes_other_chunk = nframes_other_chunk_;
    // {
    //   // SHUKE_LOG <<session_id << " "<< generateMdSumToInt(session_id);
    //   int sequence_id = GenerateMdSumToInt(session_id);
    //   if (sequence_id <= 0 || sequence_id > std::numeric_limits<int>::max()) {
    //     sequence_id = RandInt(1, 100000);
    //   }
    //   request_conf->sequence_id = sequence_id;
    // }
    {
      std::lock_guard<std::mutex> lock(mutex_);
      sequence_id_loop_++;
      if(sequence_id_loop_>=sequence_id_loop_max_){
        sequence_id_loop_ = sequence_id_loop_min_;
      }
      request_conf->sequence_id = sequence_id_loop_;
    }

    // request_conf->punctuation =
    //   std::make_shared<PunctServer>(punctuation_config_);
    request_conf->post_processor_ = post_processor_;
    request_conf->feature_pipeline =
        std::make_shared<FeaturePipeline>(*feature_config_);

    request_conf->vad_config.reset(new VadConfig(vad_config_.get()));
    // 覆盖默认的断句阀值，有效值在200-1200之间
    uint32_t req_max_sentence_silence = init_request->session_request().asr_service_param().max_sentence_silence();
    if (req_max_sentence_silence >= 200 && req_max_sentence_silence <= 1200)
    {
      request_conf->vad_config->max_speech_pause_length = (int)req_max_sentence_silence;
    }
    else
    {
      SHUKE_WARN << "max_sentence_silence: " << req_max_sentence_silence << " not validate, range [200,1200].";
    }

    request_conf->word_recognition = checkWordRecognition(init_request->reserved_field().field2());

    // 从vad_config中读取分贝相关配置
    request_conf->enable_decibel_push = request_conf->vad_config->enable_decibel_push;
    request_conf->decibel_threshold = request_conf->vad_config->decibel_threshold;

    request_conf->vad = std::make_shared<VAD>(request_conf->feature_pipeline,
                                              *(request_conf->vad_config));
    // call_connected 接通前后标识参数,用于区分在接通前、后语音助手识别
    // AudioSpec.reverses_bytes=false 接通前
    // AudioSpec.reverses_bytes=true  接通后
    request_conf->call_connected = init_request->audio_info().reverses_bytes();

    std::string asr_session_id = session_id + "_" + generate_unique_id();
    SHUKE_LOG << "save session mapping: <" << session_id << ", " << asr_session_id << ">";
    SHUKE_LOG << "session: " << asr_session_id << ", call connected: " << request_conf->call_connected;
    if (request_conf->call_connected) { // 接通后将接通前的 asr_session_id 删除（如果有的话）
      sessionIdMapping_->Delete(session_id);
    }
    sessionIdMapping_->Insert(session_id, asr_session_id);
    request_conf->asr_session_id = asr_session_id;

    if (!sessionID2InitRequest_->Insert(asr_session_id, request_conf)) {
      std::string warning_message = "  coming more than one";
      SHUKE_WARN << session_id << " " << warning_message;
      OnErrorMessage(init_response, session_id, init_error_code,
                     warning_message);
      return Status::CANCELLED;
    }

    SHUKE_LOG << session_id << " num_threads "
              << sessionID2InitRequest_->Size();
    init_response->set_session(session_id);
    return Status::OK;
  } catch (std::exception const& e) {
    SHUKE_ERR << e.what();
    return Status::CANCELLED;
  }
}

Status GrpcServer::recognize_stream(
    ServerContext* context, ServerReaderWriter<Response, Request>* stream) {
  try {
    SHUKE_LOG << "new session coming";
    num_threads_++;
    auto request = std::make_shared<Request>();
    auto response = std::make_shared<Response>();
    //
    std::shared_ptr<Fifo<char>> session_id_fifo =
        std::make_shared<FifoAmplify<char>>();
    // GrpcConnectionHandler handler(stream, request, response,
    //                               sessionID2InitRequest_, session_id_fifo);
    // std::thread t(std::move(handler));

    auto handler = std::make_shared<GrpcConnectionHandler>(
        stream, request, response, sessionID2InitRequest_, sessionIdMapping_,
        triton_grpc_server_url_, triton_grpc_client_verbose_, session_id_fifo);
    // 创建线程，使用 lambda 表达式调用 GrpcConnectionHandler 的 operator()
    std::thread t([handler] {
      (*handler)();  // 调用 GrpcConnectionHandler 的 operator()
    });

    t.join();
    {
      if (session_id_fifo.get() != nullptr && session_id_fifo->Size() > 0) {
        std::string asr_session_id = session_id_fifo->Data();
        if (asr_session_id.size() > 0 && asr_session_id.size() <= MAX_LENGTH_SESSION_ID) {
          // SHUKE_LOG << session_id << " threads: " << num_threads_;
          SHUKE_LOG << "free " << asr_session_id;

          // 通话结束后，清除 mapping 关系
          std::unique_ptr<RequestConf> request_conf_tmp = std::make_unique<RequestConf>();
          if (sessionID2InitRequest_->Find(asr_session_id, request_conf_tmp)) {
            if(request_conf_tmp->call_connected) {
              if(!sessionIdMapping_->Delete(asr_session_id)) {
                SHUKE_WARN << asr_session_id << " failed to free session id mapping";
              }
            }
          }

          // 清除线程上下文
          if (!sessionID2InitRequest_->Delete(asr_session_id)) {
            SHUKE_WARN << asr_session_id << " failed to free";
          }
        }
      }
    }
    num_threads_--;
    return Status::OK;
  } catch (std::exception const& e) {
    SHUKE_ERR << e.what();
    num_threads_--;
    return Status::CANCELLED;
  }
}

int GrpcServer::NumRequriedFrames(bool start) {
  int num_requried_frames = 0;
  int chunk_size = 18, right_context_ = 6, subsampling_rate_ = 4;
  // it is v5 and v6 deferent
  if (asr_version_=="v6"){
    chunk_size = 16;
  }
  // SHUKE_LOG << "asr_version:" << asr_version_ << " chunk_size:"<< chunk_size;
  int frame_length_ms = 25, frame_shift_ms = 10;
  if (chunk_size > 0) {
    if (!start) {                        // First batch
      int context = right_context_ + 1;  // Add current frame  //7
      num_requried_frames =
          (chunk_size - 1) * subsampling_rate_ + context;  // 15*4+7=67
      int add_frames =
          std::ceil((frame_length_ms - frame_shift_ms) / frame_shift_ms);
      num_requried_frames += 2;
    } else {
      num_requried_frames = chunk_size * subsampling_rate_;  // 16*4
    }
  } else {
    num_requried_frames = std::numeric_limits<int>::max();
    num_requried_frames /= 2;
  }
  return num_requried_frames;
}

GrpcServer::GrpcServer(std::shared_ptr<FeaturePipelineConfig> feature_config,
                       std::shared_ptr<VadConfig> vad_config,
                       std::shared_ptr<PunctuationConfig> punctuation_config,
                       int max_num_request, 
                       int sequence_id_loop_level,
                       std::string triton_grpc_server_url,
                       bool triton_grpc_client_verbose,
                       std::string asr_version)
    : feature_config_(std::move(feature_config)),
      max_num_request_(max_num_request),
      sequence_id_loop_level_(sequence_id_loop_level),
      triton_grpc_server_url_(triton_grpc_server_url),
      triton_grpc_client_verbose_(triton_grpc_client_verbose),
      vad_config_(std::move(vad_config)),
      asr_version_(asr_version) {
  SHUKE_LOG << "hello! asr:" << asr_version;

  sessionID2InitRequest_ = std::make_shared<GRPCSessionMap>();
  sessionIdMapping_ = std::make_shared<GRPCSessionIdMap>();
  nframes_first_chunk_ = NumRequriedFrames(false);
  nframes_other_chunk_ = NumRequriedFrames(true);

  sequence_id_loop_ = sequence_id_loop_level_ * sequence_id_loop_step_ + 1;
  sequence_id_loop_min_ = sequence_id_loop_;
  sequence_id_loop_max_ = sequence_id_loop_min_ + sequence_id_loop_step_;

  int language_type = 0;
  bool lowercase = true;
  PostProcessOptions post_process_opts;
  post_process_opts.language_type =
      language_type == 0 ? kMandarinEnglish : kIndoEuropean;
  post_process_opts.lowercase = lowercase;
  post_process_opts.punctuation =
      std::make_shared<PunctServer>(punctuation_config);
  post_processor_ =
      std::make_shared<PostProcessor>(std::move(post_process_opts));
}

GrpcServer::~GrpcServer() { SHUKE_LOG << "say goodbye!"; }
}  // namespace _360sk
