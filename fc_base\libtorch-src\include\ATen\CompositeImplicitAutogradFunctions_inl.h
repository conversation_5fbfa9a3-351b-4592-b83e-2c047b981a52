// @generated by tools/codegen/gen.py from DispatchKeyFunctions_inl.h

// NB: The implementing C++ file is RegisterDispatchKey.cpp

// The only #includes we need are for custom classes that have defaults in the C++ API
#include <c10/core/MemoryFormat.h>
#include <c10/core/Scalar.h>
#include <ATen/core/Reduction.h>

namespace at {
namespace compositeimplicitautograd {

TORCH_API at::Tensor _cast_Byte(const at::Tensor & self, bool non_blocking=false);
TORCH_API at::Tensor _cast_Char(const at::Tensor & self, bool non_blocking=false);
TORCH_API at::Tensor _cast_Double(const at::Tensor & self, bool non_blocking=false);
TORCH_API at::Tensor _cast_Float(const at::Tensor & self, bool non_blocking=false);
TORCH_API at::Tensor _cast_Int(const at::Tensor & self, bool non_blocking=false);
TORCH_API at::Tensor _cast_Long(const at::Tensor & self, bool non_blocking=false);
TORCH_API at::Tensor _cast_Short(const at::Tensor & self, bool non_blocking=false);
TORCH_API at::Tensor _cast_Half(const at::Tensor & self, bool non_blocking=false);
TORCH_API void _backward(const at::Tensor & self, at::TensorList inputs, const c10::optional<at::Tensor> & gradient={}, c10::optional<bool> retain_graph=c10::nullopt, bool create_graph=false);
TORCH_API void set_data(at::Tensor & self, const at::Tensor & new_data);
TORCH_API at::Tensor data(const at::Tensor & self);
TORCH_API bool is_leaf(const at::Tensor & self);
TORCH_API int64_t output_nr(const at::Tensor & self);
TORCH_API int64_t _version(const at::Tensor & self);
TORCH_API at::Tensor & requires_grad_(at::Tensor & self, bool requires_grad=true);
TORCH_API void retain_grad(at::Tensor & self);
TORCH_API bool retains_grad(const at::Tensor & self);
TORCH_API at::Tensor _make_dual(const at::Tensor & primal, const at::Tensor & tangent, int64_t level);
TORCH_API ::std::tuple<at::Tensor,at::Tensor> _unpack_dual(const at::Tensor & dual, int64_t level);
TORCH_API at::Tensor & rename_(at::Tensor & self, c10::optional<at::DimnameList> names);
TORCH_API at::Tensor rename(const at::Tensor & self, c10::optional<at::DimnameList> names);
TORCH_API at::Tensor align_to(const at::Tensor & self, at::DimnameList names);
TORCH_API at::Tensor align_to(const at::Tensor & self, at::DimnameList order, int64_t ellipsis_idx);
TORCH_API at::Tensor align_as(const at::Tensor & self, const at::Tensor & other);
TORCH_API ::std::vector<at::Tensor> align_tensors(at::TensorList tensors);
TORCH_API at::Tensor refine_names(const at::Tensor & self, at::DimnameList names);
TORCH_API bool _use_cudnn_rnn_flatten_weight();
TORCH_API int64_t _debug_has_internal_overlap(const at::Tensor & self);
TORCH_API ::std::tuple<at::Tensor,at::Tensor> _sobol_engine_draw(const at::Tensor & quasi, int64_t n, const at::Tensor & sobolstate, int64_t dimension, int64_t num_generated, c10::optional<at::ScalarType> dtype);
TORCH_API at::Tensor & _sobol_engine_ff_(at::Tensor & self, int64_t n, const at::Tensor & sobolstate, int64_t dimension, int64_t num_generated);
TORCH_API at::Tensor & _sobol_engine_scramble_(at::Tensor & self, const at::Tensor & ltm, int64_t dimension);
TORCH_API at::Tensor & _sobol_engine_initialize_state_(at::Tensor & self, int64_t dimension);
TORCH_API at::Tensor _reshape_from_tensor(const at::Tensor & self, const at::Tensor & shape);
TORCH_API at::Tensor _shape_as_tensor(const at::Tensor & self);
TORCH_API at::Tensor dropout(const at::Tensor & input, double p, bool train);
TORCH_API at::Tensor & dropout_(at::Tensor & self, double p, bool train);
TORCH_API at::Tensor feature_dropout(const at::Tensor & input, double p, bool train);
TORCH_API at::Tensor & feature_dropout_(at::Tensor & self, double p, bool train);
TORCH_API at::Tensor alpha_dropout(const at::Tensor & input, double p, bool train);
TORCH_API at::Tensor & alpha_dropout_(at::Tensor & self, double p, bool train);
TORCH_API at::Tensor feature_alpha_dropout(const at::Tensor & input, double p, bool train);
TORCH_API at::Tensor & feature_alpha_dropout_(at::Tensor & self, double p, bool train);
TORCH_API at::Tensor absolute(const at::Tensor & self);
TORCH_API at::Tensor & absolute_out(at::Tensor & out, const at::Tensor & self);
TORCH_API at::Tensor & absolute_outf(const at::Tensor & self, at::Tensor & out);
TORCH_API at::Tensor & absolute_(at::Tensor & self);
TORCH_API at::Tensor real(const at::Tensor & self);
TORCH_API at::Tensor imag(const at::Tensor & self);
TORCH_API at::Tensor conj(const at::Tensor & self);
TORCH_API at::Tensor conj_physical(const at::Tensor & self);
TORCH_API at::Tensor resolve_conj(const at::Tensor & self);
TORCH_API at::Tensor resolve_neg(const at::Tensor & self);
TORCH_API at::Tensor arccos(const at::Tensor & self);
TORCH_API at::Tensor & arccos_out(at::Tensor & out, const at::Tensor & self);
TORCH_API at::Tensor & arccos_outf(const at::Tensor & self, at::Tensor & out);
TORCH_API at::Tensor & arccos_(at::Tensor & self);
TORCH_API at::Tensor avg_pool1d(const at::Tensor & self, at::IntArrayRef kernel_size, at::IntArrayRef stride={}, at::IntArrayRef padding=0, bool ceil_mode=false, bool count_include_pad=true);
TORCH_API at::Tensor adaptive_avg_pool1d(const at::Tensor & self, at::IntArrayRef output_size);
TORCH_API ::std::tuple<at::Tensor,at::Tensor> adaptive_max_pool1d(const at::Tensor & self, at::IntArrayRef output_size);
TORCH_API at::Tensor addr(const at::Tensor & self, const at::Tensor & vec1, const at::Tensor & vec2, const at::Scalar & beta=1, const at::Scalar & alpha=1);
TORCH_API at::Tensor & addr_out(at::Tensor & out, const at::Tensor & self, const at::Tensor & vec1, const at::Tensor & vec2, const at::Scalar & beta=1, const at::Scalar & alpha=1);
TORCH_API at::Tensor & addr_outf(const at::Tensor & self, const at::Tensor & vec1, const at::Tensor & vec2, const at::Scalar & beta, const at::Scalar & alpha, at::Tensor & out);
TORCH_API at::Tensor affine_grid_generator_backward(const at::Tensor & grad, at::IntArrayRef size, bool align_corners);
TORCH_API at::Tensor all(const at::Tensor & self, at::Dimname dim, bool keepdim=false);
TORCH_API at::Tensor & all_out(at::Tensor & out, const at::Tensor & self, at::Dimname dim, bool keepdim=false);
TORCH_API at::Tensor & all_outf(const at::Tensor & self, at::Dimname dim, bool keepdim, at::Tensor & out);
TORCH_API bool allclose(const at::Tensor & self, const at::Tensor & other, double rtol=1e-05, double atol=1e-08, bool equal_nan=false);
TORCH_API at::Tensor any(const at::Tensor & self, at::Dimname dim, bool keepdim=false);
TORCH_API at::Tensor & any_out(at::Tensor & out, const at::Tensor & self, at::Dimname dim, bool keepdim=false);
TORCH_API at::Tensor & any_outf(const at::Tensor & self, at::Dimname dim, bool keepdim, at::Tensor & out);
TORCH_API at::Tensor arange(const at::Scalar & end, at::TensorOptions options={});
TORCH_API at::Tensor arange(const at::Scalar & end, c10::optional<at::ScalarType> dtype, c10::optional<at::Layout> layout, c10::optional<at::Device> device, c10::optional<bool> pin_memory);
TORCH_API at::Tensor arange(const at::Scalar & start, const at::Scalar & end, at::TensorOptions options={});
TORCH_API at::Tensor arange(const at::Scalar & start, const at::Scalar & end, c10::optional<at::ScalarType> dtype, c10::optional<at::Layout> layout, c10::optional<at::Device> device, c10::optional<bool> pin_memory);
TORCH_API at::Tensor arange(const at::Scalar & start, const at::Scalar & end, const at::Scalar & step, at::TensorOptions options={});
TORCH_API at::Tensor arange(const at::Scalar & start, const at::Scalar & end, const at::Scalar & step, c10::optional<at::ScalarType> dtype, c10::optional<at::Layout> layout, c10::optional<at::Device> device, c10::optional<bool> pin_memory);
TORCH_API at::Tensor & arange_out(at::Tensor & out, const at::Scalar & end);
TORCH_API at::Tensor & arange_outf(const at::Scalar & end, at::Tensor & out);
TORCH_API at::Tensor _dim_arange(const at::Tensor & like, int64_t dim);
TORCH_API at::Tensor arccosh(const at::Tensor & self);
TORCH_API at::Tensor & arccosh_out(at::Tensor & out, const at::Tensor & self);
TORCH_API at::Tensor & arccosh_outf(const at::Tensor & self, at::Tensor & out);
TORCH_API at::Tensor & arccosh_(at::Tensor & self);
TORCH_API at::Tensor arcsinh(const at::Tensor & self);
TORCH_API at::Tensor & arcsinh_out(at::Tensor & out, const at::Tensor & self);
TORCH_API at::Tensor & arcsinh_outf(const at::Tensor & self, at::Tensor & out);
TORCH_API at::Tensor & arcsinh_(at::Tensor & self);
TORCH_API at::Tensor arctanh(const at::Tensor & self);
TORCH_API at::Tensor & arctanh_out(at::Tensor & out, const at::Tensor & self);
TORCH_API at::Tensor & arctanh_outf(const at::Tensor & self, at::Tensor & out);
TORCH_API at::Tensor & arctanh_(at::Tensor & self);
TORCH_API at::Tensor arcsin(const at::Tensor & self);
TORCH_API at::Tensor & arcsin_out(at::Tensor & out, const at::Tensor & self);
TORCH_API at::Tensor & arcsin_outf(const at::Tensor & self, at::Tensor & out);
TORCH_API at::Tensor & arcsin_(at::Tensor & self);
TORCH_API at::Tensor arctan(const at::Tensor & self);
TORCH_API at::Tensor & arctan_out(at::Tensor & out, const at::Tensor & self);
TORCH_API at::Tensor & arctan_outf(const at::Tensor & self, at::Tensor & out);
TORCH_API at::Tensor & arctan_(at::Tensor & self);
TORCH_API at::Tensor atleast_1d(const at::Tensor & self);
TORCH_API ::std::vector<at::Tensor> atleast_1d(at::TensorList tensors);
TORCH_API at::Tensor atleast_2d(const at::Tensor & self);
TORCH_API ::std::vector<at::Tensor> atleast_2d(at::TensorList tensors);
TORCH_API at::Tensor atleast_3d(const at::Tensor & self);
TORCH_API ::std::vector<at::Tensor> atleast_3d(at::TensorList tensors);
TORCH_API at::Tensor & _baddbmm_mkl_(at::Tensor & self, const at::Tensor & batch1, const at::Tensor & batch2, const at::Scalar & beta=1, const at::Scalar & alpha=1);
TORCH_API at::Tensor bartlett_window(int64_t window_length, at::TensorOptions options={});
TORCH_API at::Tensor bartlett_window(int64_t window_length, c10::optional<at::ScalarType> dtype, c10::optional<at::Layout> layout, c10::optional<at::Device> device, c10::optional<bool> pin_memory);
TORCH_API at::Tensor bartlett_window(int64_t window_length, bool periodic, at::TensorOptions options={});
TORCH_API at::Tensor bartlett_window(int64_t window_length, bool periodic, c10::optional<at::ScalarType> dtype, c10::optional<at::Layout> layout, c10::optional<at::Device> device, c10::optional<bool> pin_memory);
TORCH_API at::Tensor batch_norm(const at::Tensor & input, const c10::optional<at::Tensor> & weight, const c10::optional<at::Tensor> & bias, const c10::optional<at::Tensor> & running_mean, const c10::optional<at::Tensor> & running_var, bool training, double momentum, double eps, bool cudnn_enabled);
TORCH_API ::std::tuple<at::Tensor,at::Tensor,at::Tensor,at::Tensor,int64_t> _batch_norm_impl_index(const at::Tensor & input, const c10::optional<at::Tensor> & weight, const c10::optional<at::Tensor> & bias, const c10::optional<at::Tensor> & running_mean, const c10::optional<at::Tensor> & running_var, bool training, double momentum, double eps, bool cudnn_enabled);
TORCH_API ::std::tuple<at::Tensor,at::Tensor,at::Tensor> _batch_norm_impl_index_backward(int64_t impl_index, const at::Tensor & input, const at::Tensor & grad_output, const c10::optional<at::Tensor> & weight, const c10::optional<at::Tensor> & running_mean, const c10::optional<at::Tensor> & running_var, const c10::optional<at::Tensor> & save_mean, const c10::optional<at::Tensor> & save_var_transform, bool train, double eps, ::std::array<bool,3> output_mask, const at::Tensor & reservedSpace);
TORCH_API at::Tensor bernoulli(const at::Tensor & self, double p, c10::optional<at::Generator> generator=c10::nullopt);
TORCH_API at::Tensor bilinear(const at::Tensor & input1, const at::Tensor & input2, const at::Tensor & weight, const c10::optional<at::Tensor> & bias);
TORCH_API at::Tensor binary_cross_entropy_with_logits_backward(const at::Tensor & grad_output, const at::Tensor & self, const at::Tensor & target, const c10::optional<at::Tensor> & weight={}, const c10::optional<at::Tensor> & pos_weight={}, int64_t reduction=at::Reduction::Mean);
TORCH_API at::Tensor logical_not(const at::Tensor & self);
TORCH_API at::Tensor & logical_not_(at::Tensor & self);
TORCH_API at::Tensor logical_xor(const at::Tensor & self, const at::Tensor & other);
TORCH_API at::Tensor & logical_xor_(at::Tensor & self, const at::Tensor & other);
TORCH_API at::Tensor logical_and(const at::Tensor & self, const at::Tensor & other);
TORCH_API at::Tensor & logical_and_(at::Tensor & self, const at::Tensor & other);
TORCH_API at::Tensor logical_or(const at::Tensor & self, const at::Tensor & other);
TORCH_API at::Tensor & logical_or_(at::Tensor & self, const at::Tensor & other);
TORCH_API at::Tensor blackman_window(int64_t window_length, at::TensorOptions options={});
TORCH_API at::Tensor blackman_window(int64_t window_length, c10::optional<at::ScalarType> dtype, c10::optional<at::Layout> layout, c10::optional<at::Device> device, c10::optional<bool> pin_memory);
TORCH_API at::Tensor blackman_window(int64_t window_length, bool periodic, at::TensorOptions options={});
TORCH_API at::Tensor blackman_window(int64_t window_length, bool periodic, c10::optional<at::ScalarType> dtype, c10::optional<at::Layout> layout, c10::optional<at::Device> device, c10::optional<bool> pin_memory);
TORCH_API ::std::vector<at::Tensor> broadcast_tensors(at::TensorList tensors);
TORCH_API at::Tensor broadcast_to(const at::Tensor & self, at::IntArrayRef size);
TORCH_API at::Tensor cat(at::TensorList tensors, at::Dimname dim);
TORCH_API at::Tensor & cat_out(at::Tensor & out, at::TensorList tensors, at::Dimname dim);
TORCH_API at::Tensor & cat_outf(at::TensorList tensors, at::Dimname dim, at::Tensor & out);
TORCH_API at::Tensor concat(at::TensorList tensors, int64_t dim=0);
TORCH_API at::Tensor & concat_out(at::Tensor & out, at::TensorList tensors, int64_t dim=0);
TORCH_API at::Tensor & concat_outf(at::TensorList tensors, int64_t dim, at::Tensor & out);
TORCH_API at::Tensor concat(at::TensorList tensors, at::Dimname dim);
TORCH_API at::Tensor & concat_out(at::Tensor & out, at::TensorList tensors, at::Dimname dim);
TORCH_API at::Tensor & concat_outf(at::TensorList tensors, at::Dimname dim, at::Tensor & out);
TORCH_API at::Tensor block_diag(at::TensorList tensors);
TORCH_API at::Tensor chain_matmul(at::TensorList matrices);
TORCH_API at::Tensor & chain_matmul_out(at::Tensor & out, at::TensorList matrices);
TORCH_API at::Tensor & chain_matmul_outf(at::TensorList matrices, at::Tensor & out);
TORCH_API ::std::vector<at::Tensor> unsafe_chunk(const at::Tensor & self, int64_t chunks, int64_t dim=0);
TORCH_API ::std::vector<at::Tensor> chunk(const at::Tensor & self, int64_t chunks, int64_t dim=0);
TORCH_API ::std::vector<at::Tensor> tensor_split(const at::Tensor & self, int64_t sections, int64_t dim=0);
TORCH_API ::std::vector<at::Tensor> tensor_split(const at::Tensor & self, at::IntArrayRef indices, int64_t dim=0);
TORCH_API ::std::vector<at::Tensor> tensor_split(const at::Tensor & self, const at::Tensor & tensor_indices_or_sections, int64_t dim=0);
TORCH_API at::Tensor clip(const at::Tensor & self, const c10::optional<at::Scalar> & min, const c10::optional<at::Scalar> & max=c10::nullopt);
TORCH_API at::Tensor & clip_out(at::Tensor & out, const at::Tensor & self, const c10::optional<at::Scalar> & min, const c10::optional<at::Scalar> & max=c10::nullopt);
TORCH_API at::Tensor & clip_outf(const at::Tensor & self, const c10::optional<at::Scalar> & min, const c10::optional<at::Scalar> & max, at::Tensor & out);
TORCH_API at::Tensor & clip_(at::Tensor & self, const c10::optional<at::Scalar> & min, const c10::optional<at::Scalar> & max=c10::nullopt);
TORCH_API at::Tensor clip(const at::Tensor & self, const c10::optional<at::Tensor> & min={}, const c10::optional<at::Tensor> & max={});
TORCH_API at::Tensor & clip_out(at::Tensor & out, const at::Tensor & self, const c10::optional<at::Tensor> & min={}, const c10::optional<at::Tensor> & max={});
TORCH_API at::Tensor & clip_outf(const at::Tensor & self, const c10::optional<at::Tensor> & min, const c10::optional<at::Tensor> & max, at::Tensor & out);
TORCH_API at::Tensor & clip_(at::Tensor & self, const c10::optional<at::Tensor> & min={}, const c10::optional<at::Tensor> & max={});
TORCH_API bool cudnn_is_acceptable(const at::Tensor & self);
TORCH_API at::Tensor contiguous(const at::Tensor & self, at::MemoryFormat memory_format=MemoryFormat::Contiguous);
TORCH_API at::Tensor convolution(const at::Tensor & input, const at::Tensor & weight, const c10::optional<at::Tensor> & bias, at::IntArrayRef stride, at::IntArrayRef padding, at::IntArrayRef dilation, bool transposed, at::IntArrayRef output_padding, int64_t groups);
TORCH_API at::Tensor _convolution(const at::Tensor & input, const at::Tensor & weight, const c10::optional<at::Tensor> & bias, at::IntArrayRef stride, at::IntArrayRef padding, at::IntArrayRef dilation, bool transposed, at::IntArrayRef output_padding, int64_t groups, bool benchmark, bool deterministic, bool cudnn_enabled, bool allow_tf32);
TORCH_API at::Tensor _convolution(const at::Tensor & input, const at::Tensor & weight, const c10::optional<at::Tensor> & bias, at::IntArrayRef stride, at::IntArrayRef padding, at::IntArrayRef dilation, bool transposed, at::IntArrayRef output_padding, int64_t groups, bool benchmark, bool deterministic, bool cudnn_enabled);
TORCH_API at::Tensor _convolution_mode(const at::Tensor & input, const at::Tensor & weight, const c10::optional<at::Tensor> & bias, at::IntArrayRef stride, c10::string_view padding, at::IntArrayRef dilation, int64_t groups);
TORCH_API at::Tensor _convolution_nogroup(const at::Tensor & input, const at::Tensor & weight, const c10::optional<at::Tensor> & bias, at::IntArrayRef stride, at::IntArrayRef padding, at::IntArrayRef dilation, bool transposed, at::IntArrayRef output_padding);
TORCH_API ::std::tuple<at::Tensor,at::Tensor,at::Tensor> _convolution_double_backward(const c10::optional<at::Tensor> & ggI, const c10::optional<at::Tensor> & ggW, const c10::optional<at::Tensor> & ggb, const at::Tensor & gO, const at::Tensor & weight, const at::Tensor & self, at::IntArrayRef stride, at::IntArrayRef padding, at::IntArrayRef dilation, bool transposed, at::IntArrayRef output_padding, int64_t groups, bool benchmark, bool deterministic, bool cudnn_enabled, bool allow_tf32, ::std::array<bool,3> output_mask);
TORCH_API at::Tensor conv1d(const at::Tensor & input, const at::Tensor & weight, const c10::optional<at::Tensor> & bias={}, at::IntArrayRef stride=1, at::IntArrayRef padding=0, at::IntArrayRef dilation=1, int64_t groups=1);
TORCH_API at::Tensor conv2d(const at::Tensor & input, const at::Tensor & weight, const c10::optional<at::Tensor> & bias={}, at::IntArrayRef stride=1, at::IntArrayRef padding=0, at::IntArrayRef dilation=1, int64_t groups=1);
TORCH_API at::Tensor conv3d(const at::Tensor & input, const at::Tensor & weight, const c10::optional<at::Tensor> & bias={}, at::IntArrayRef stride=1, at::IntArrayRef padding=0, at::IntArrayRef dilation=1, int64_t groups=1);
TORCH_API at::Tensor conv1d(const at::Tensor & input, const at::Tensor & weight, const c10::optional<at::Tensor> & bias, at::IntArrayRef stride, c10::string_view padding, at::IntArrayRef dilation=1, int64_t groups=1);
TORCH_API at::Tensor conv2d(const at::Tensor & input, const at::Tensor & weight, const c10::optional<at::Tensor> & bias, at::IntArrayRef stride, c10::string_view padding, at::IntArrayRef dilation=1, int64_t groups=1);
TORCH_API at::Tensor conv3d(const at::Tensor & input, const at::Tensor & weight, const c10::optional<at::Tensor> & bias, at::IntArrayRef stride, c10::string_view padding, at::IntArrayRef dilation=1, int64_t groups=1);
TORCH_API ::std::tuple<at::Tensor,at::Tensor,at::Tensor> conv_tbc_backward(const at::Tensor & self, const at::Tensor & input, const at::Tensor & weight, const at::Tensor & bias, int64_t pad);
TORCH_API at::Tensor conv_transpose1d(const at::Tensor & input, const at::Tensor & weight, const c10::optional<at::Tensor> & bias={}, at::IntArrayRef stride=1, at::IntArrayRef padding=0, at::IntArrayRef output_padding=0, int64_t groups=1, at::IntArrayRef dilation=1);
TORCH_API at::Tensor conv_transpose2d(const at::Tensor & input, const at::Tensor & weight, const c10::optional<at::Tensor> & bias={}, at::IntArrayRef stride=1, at::IntArrayRef padding=0, at::IntArrayRef output_padding=0, int64_t groups=1, at::IntArrayRef dilation=1);
TORCH_API at::Tensor conv_transpose3d(const at::Tensor & input, const at::Tensor & weight, const c10::optional<at::Tensor> & bias={}, at::IntArrayRef stride=1, at::IntArrayRef padding=0, at::IntArrayRef output_padding=0, int64_t groups=1, at::IntArrayRef dilation=1);
TORCH_API at::Tensor cosine_embedding_loss(const at::Tensor & input1, const at::Tensor & input2, const at::Tensor & target, double margin=0.0, int64_t reduction=at::Reduction::Mean);
TORCH_API at::Tensor cov(const at::Tensor & self, int64_t correction=1, const c10::optional<at::Tensor> & fweights={}, const c10::optional<at::Tensor> & aweights={});
TORCH_API at::Tensor corrcoef(const at::Tensor & self);
TORCH_API ::std::tuple<at::Tensor,at::Tensor> cummax(const at::Tensor & self, at::Dimname dim);
TORCH_API ::std::tuple<at::Tensor &,at::Tensor &> cummax_out(at::Tensor & values, at::Tensor & indices, const at::Tensor & self, at::Dimname dim);
TORCH_API ::std::tuple<at::Tensor &,at::Tensor &> cummax_outf(const at::Tensor & self, at::Dimname dim, at::Tensor & values, at::Tensor & indices);
TORCH_API ::std::tuple<at::Tensor,at::Tensor> cummin(const at::Tensor & self, at::Dimname dim);
TORCH_API ::std::tuple<at::Tensor &,at::Tensor &> cummin_out(at::Tensor & values, at::Tensor & indices, const at::Tensor & self, at::Dimname dim);
TORCH_API ::std::tuple<at::Tensor &,at::Tensor &> cummin_outf(const at::Tensor & self, at::Dimname dim, at::Tensor & values, at::Tensor & indices);
TORCH_API at::Tensor cummaxmin_backward(const at::Tensor & grad, const at::Tensor & input, const at::Tensor & indices, int64_t dim);
TORCH_API at::Tensor cumprod(const at::Tensor & self, at::Dimname dim, c10::optional<at::ScalarType> dtype=c10::nullopt);
TORCH_API at::Tensor & cumprod_out(at::Tensor & out, const at::Tensor & self, at::Dimname dim, c10::optional<at::ScalarType> dtype=c10::nullopt);
TORCH_API at::Tensor & cumprod_outf(const at::Tensor & self, at::Dimname dim, c10::optional<at::ScalarType> dtype, at::Tensor & out);
TORCH_API at::Tensor & cumprod_(at::Tensor & self, at::Dimname dim, c10::optional<at::ScalarType> dtype=c10::nullopt);
TORCH_API at::Tensor cumprod_backward(const at::Tensor & grad, const at::Tensor & input, int64_t dim, const at::Tensor & output);
TORCH_API at::Tensor cumsum(const at::Tensor & self, at::Dimname dim, c10::optional<at::ScalarType> dtype=c10::nullopt);
TORCH_API at::Tensor & cumsum_out(at::Tensor & out, const at::Tensor & self, at::Dimname dim, c10::optional<at::ScalarType> dtype=c10::nullopt);
TORCH_API at::Tensor & cumsum_outf(const at::Tensor & self, at::Dimname dim, c10::optional<at::ScalarType> dtype, at::Tensor & out);
TORCH_API at::Tensor & cumsum_(at::Tensor & self, at::Dimname dim, c10::optional<at::ScalarType> dtype=c10::nullopt);
TORCH_API at::Tensor cumulative_trapezoid(const at::Tensor & y, const at::Tensor & x, int64_t dim=-1);
TORCH_API at::Tensor cumulative_trapezoid(const at::Tensor & y, const at::Scalar & dx=1, int64_t dim=-1);
TORCH_API at::Tensor ctc_loss(const at::Tensor & log_probs, const at::Tensor & targets, at::IntArrayRef input_lengths, at::IntArrayRef target_lengths, int64_t blank=0, int64_t reduction=at::Reduction::Mean, bool zero_infinity=false);
TORCH_API at::Tensor ctc_loss(const at::Tensor & log_probs, const at::Tensor & targets, const at::Tensor & input_lengths, const at::Tensor & target_lengths, int64_t blank=0, int64_t reduction=at::Reduction::Mean, bool zero_infinity=false);
TORCH_API at::Tensor diag_embed(const at::Tensor & self, int64_t offset=0, int64_t dim1=-2, int64_t dim2=-1);
TORCH_API at::Tensor diagflat(const at::Tensor & self, int64_t offset=0);
TORCH_API at::Tensor diagonal(const at::Tensor & self, at::Dimname outdim, at::Dimname dim1, at::Dimname dim2, int64_t offset=0);
TORCH_API at::Tensor & fill_diagonal_(at::Tensor & self, const at::Scalar & fill_value, bool wrap=false);
TORCH_API at::Tensor diff(const at::Tensor & self, int64_t n=1, int64_t dim=-1, const c10::optional<at::Tensor> & prepend={}, const c10::optional<at::Tensor> & append={});
TORCH_API at::Tensor & diff_out(at::Tensor & out, const at::Tensor & self, int64_t n=1, int64_t dim=-1, const c10::optional<at::Tensor> & prepend={}, const c10::optional<at::Tensor> & append={});
TORCH_API at::Tensor & diff_outf(const at::Tensor & self, int64_t n, int64_t dim, const c10::optional<at::Tensor> & prepend, const c10::optional<at::Tensor> & append, at::Tensor & out);
TORCH_API ::std::vector<at::Tensor> gradient(const at::Tensor & self, const c10::optional<at::Scalar> & spacing=c10::nullopt, c10::optional<int64_t> dim=c10::nullopt, int64_t edge_order=1);
TORCH_API ::std::vector<at::Tensor> gradient(const at::Tensor & self, const at::Scalar & spacing, at::IntArrayRef dim, int64_t edge_order=1);
TORCH_API ::std::vector<at::Tensor> gradient(const at::Tensor & self, at::IntArrayRef dim, int64_t edge_order=1);
TORCH_API ::std::vector<at::Tensor> gradient(const at::Tensor & self, at::ArrayRef<at::Scalar> spacing, c10::optional<int64_t> dim=c10::nullopt, int64_t edge_order=1);
TORCH_API ::std::vector<at::Tensor> gradient(const at::Tensor & self, at::ArrayRef<at::Scalar> spacing, at::IntArrayRef dim, int64_t edge_order=1);
TORCH_API ::std::vector<at::Tensor> gradient(const at::Tensor & self, at::TensorList spacing, c10::optional<int64_t> dim=c10::nullopt, int64_t edge_order=1);
TORCH_API ::std::vector<at::Tensor> gradient(const at::Tensor & self, at::TensorList spacing, at::IntArrayRef dim, int64_t edge_order=1);
TORCH_API at::Tensor divide(const at::Tensor & self, const at::Tensor & other);
TORCH_API at::Tensor & divide_out(at::Tensor & out, const at::Tensor & self, const at::Tensor & other);
TORCH_API at::Tensor & divide_outf(const at::Tensor & self, const at::Tensor & other, at::Tensor & out);
TORCH_API at::Tensor & divide_(at::Tensor & self, const at::Tensor & other);
TORCH_API at::Tensor divide(const at::Tensor & self, const at::Scalar & other);
TORCH_API at::Tensor & divide_(at::Tensor & self, const at::Scalar & other);
TORCH_API at::Tensor divide(const at::Tensor & self, const at::Tensor & other, c10::optional<c10::string_view> rounding_mode);
TORCH_API at::Tensor & divide_out(at::Tensor & out, const at::Tensor & self, const at::Tensor & other, c10::optional<c10::string_view> rounding_mode);
TORCH_API at::Tensor & divide_outf(const at::Tensor & self, const at::Tensor & other, c10::optional<c10::string_view> rounding_mode, at::Tensor & out);
TORCH_API at::Tensor & divide_(at::Tensor & self, const at::Tensor & other, c10::optional<c10::string_view> rounding_mode);
TORCH_API at::Tensor divide(const at::Tensor & self, const at::Scalar & other, c10::optional<c10::string_view> rounding_mode);
TORCH_API at::Tensor & divide_(at::Tensor & self, const at::Scalar & other, c10::optional<c10::string_view> rounding_mode);
TORCH_API at::Tensor true_divide(const at::Tensor & self, const at::Tensor & other);
TORCH_API at::Tensor & true_divide_out(at::Tensor & out, const at::Tensor & self, const at::Tensor & other);
TORCH_API at::Tensor & true_divide_outf(const at::Tensor & self, const at::Tensor & other, at::Tensor & out);
TORCH_API at::Tensor & true_divide_(at::Tensor & self, const at::Tensor & other);
TORCH_API at::Tensor true_divide(const at::Tensor & self, const at::Scalar & other);
TORCH_API at::Tensor & true_divide_(at::Tensor & self, const at::Scalar & other);
TORCH_API at::Tensor einsum(c10::string_view equation, at::TensorList tensors);
TORCH_API at::Tensor embedding_backward(const at::Tensor & grad, const at::Tensor & indices, int64_t num_weights, int64_t padding_idx, bool scale_grad_by_freq, bool sparse);
TORCH_API at::Tensor embedding_sparse_backward(const at::Tensor & grad, const at::Tensor & indices, int64_t num_weights, int64_t padding_idx, bool scale_grad_by_freq);
TORCH_API ::std::tuple<at::Tensor,at::Tensor> _rowwise_prune(const at::Tensor & weight, const at::Tensor & mask, at::ScalarType compressed_indices_dtype);
TORCH_API at::Tensor row_stack(at::TensorList tensors);
TORCH_API at::Tensor & row_stack_out(at::Tensor & out, at::TensorList tensors);
TORCH_API at::Tensor & row_stack_outf(at::TensorList tensors, at::Tensor & out);
TORCH_API ::std::tuple<at::Tensor,at::Tensor,at::Tensor,at::Tensor> embedding_bag(const at::Tensor & weight, const at::Tensor & indices, const at::Tensor & offsets, bool scale_grad_by_freq=false, int64_t mode=0, bool sparse=false, const c10::optional<at::Tensor> & per_sample_weights={}, bool include_last_offset=false);
TORCH_API ::std::tuple<at::Tensor,at::Tensor,at::Tensor,at::Tensor> embedding_bag(const at::Tensor & weight, const at::Tensor & indices, const at::Tensor & offsets, bool scale_grad_by_freq, int64_t mode, bool sparse, const c10::optional<at::Tensor> & per_sample_weights, bool include_last_offset, c10::optional<int64_t> padding_idx);
TORCH_API at::Tensor _embedding_bag_backward(const at::Tensor & grad, const at::Tensor & indices, const at::Tensor & offsets, const at::Tensor & offset2bag, const at::Tensor & bag_size, const at::Tensor & maximum_indices, int64_t num_weights, bool scale_grad_by_freq, int64_t mode, bool sparse, const c10::optional<at::Tensor> & per_sample_weights, int64_t padding_idx=-1);
TORCH_API at::Tensor _embedding_bag_sparse_backward(const at::Tensor & grad, const at::Tensor & indices, const at::Tensor & offsets, const at::Tensor & offset2bag, const at::Tensor & bag_size, int64_t num_weights, bool scale_grad_by_freq, int64_t mode, const c10::optional<at::Tensor> & per_sample_weights, int64_t padding_idx=-1);
TORCH_API at::Tensor empty(at::IntArrayRef size, c10::optional<at::DimnameList> names, at::TensorOptions options={}, c10::optional<at::MemoryFormat> memory_format=c10::nullopt);
TORCH_API at::Tensor empty(at::IntArrayRef size, c10::optional<at::DimnameList> names, c10::optional<at::ScalarType> dtype, c10::optional<at::Layout> layout, c10::optional<at::Device> device, c10::optional<bool> pin_memory, c10::optional<at::MemoryFormat> memory_format);
TORCH_API at::Tensor new_empty(const at::Tensor & self, at::IntArrayRef size, at::TensorOptions options={});
TORCH_API at::Tensor new_empty(const at::Tensor & self, at::IntArrayRef size, c10::optional<at::ScalarType> dtype, c10::optional<at::Layout> layout, c10::optional<at::Device> device, c10::optional<bool> pin_memory);
TORCH_API at::Tensor new_empty_strided(const at::Tensor & self, at::IntArrayRef size, at::IntArrayRef stride, at::TensorOptions options={});
TORCH_API at::Tensor new_empty_strided(const at::Tensor & self, at::IntArrayRef size, at::IntArrayRef stride, c10::optional<at::ScalarType> dtype, c10::optional<at::Layout> layout, c10::optional<at::Device> device, c10::optional<bool> pin_memory);
TORCH_API at::Tensor new_full(const at::Tensor & self, at::IntArrayRef size, const at::Scalar & fill_value, at::TensorOptions options={});
TORCH_API at::Tensor new_full(const at::Tensor & self, at::IntArrayRef size, const at::Scalar & fill_value, c10::optional<at::ScalarType> dtype, c10::optional<at::Layout> layout, c10::optional<at::Device> device, c10::optional<bool> pin_memory);
TORCH_API at::Tensor new_zeros(const at::Tensor & self, at::IntArrayRef size, at::TensorOptions options={});
TORCH_API at::Tensor new_zeros(const at::Tensor & self, at::IntArrayRef size, c10::optional<at::ScalarType> dtype, c10::optional<at::Layout> layout, c10::optional<at::Device> device, c10::optional<bool> pin_memory);
TORCH_API at::Tensor new_ones(const at::Tensor & self, at::IntArrayRef size, at::TensorOptions options={});
TORCH_API at::Tensor new_ones(const at::Tensor & self, at::IntArrayRef size, c10::optional<at::ScalarType> dtype, c10::optional<at::Layout> layout, c10::optional<at::Device> device, c10::optional<bool> pin_memory);
TORCH_API at::Tensor & empty_out(at::Tensor & out, at::IntArrayRef size, c10::optional<at::MemoryFormat> memory_format=c10::nullopt);
TORCH_API at::Tensor & empty_outf(at::IntArrayRef size, c10::optional<at::MemoryFormat> memory_format, at::Tensor & out);
TORCH_API at::Tensor empty_like(const at::Tensor & self, at::TensorOptions options={}, c10::optional<at::MemoryFormat> memory_format=c10::nullopt);
TORCH_API at::Tensor empty_like(const at::Tensor & self, c10::optional<at::ScalarType> dtype, c10::optional<at::Layout> layout, c10::optional<at::Device> device, c10::optional<bool> pin_memory, c10::optional<at::MemoryFormat> memory_format);
TORCH_API at::Tensor expand_as(const at::Tensor & self, const at::Tensor & other);
TORCH_API at::Tensor eye(int64_t n, at::TensorOptions options={});
TORCH_API at::Tensor eye(int64_t n, c10::optional<at::ScalarType> dtype, c10::optional<at::Layout> layout, c10::optional<at::Device> device, c10::optional<bool> pin_memory);
TORCH_API at::Tensor eye(int64_t n, int64_t m, at::TensorOptions options={});
TORCH_API at::Tensor eye(int64_t n, int64_t m, c10::optional<at::ScalarType> dtype, c10::optional<at::Layout> layout, c10::optional<at::Device> device, c10::optional<bool> pin_memory);
TORCH_API at::Tensor flatten(const at::Tensor & self, int64_t start_dim=0, int64_t end_dim=-1);
TORCH_API at::Tensor flatten(const at::Tensor & self, int64_t start_dim, int64_t end_dim, at::Dimname out_dim);
TORCH_API at::Tensor flatten(const at::Tensor & self, at::Dimname start_dim, at::Dimname end_dim, at::Dimname out_dim);
TORCH_API at::Tensor flatten(const at::Tensor & self, at::DimnameList dims, at::Dimname out_dim);
TORCH_API at::Tensor unflatten(const at::Tensor & self, int64_t dim, at::IntArrayRef sizes, c10::optional<at::DimnameList> names=c10::nullopt);
TORCH_API at::Tensor unflatten(const at::Tensor & self, at::Dimname dim, at::IntArrayRef sizes, at::DimnameList names);
TORCH_API at::Tensor floor_divide(const at::Tensor & self, const at::Scalar & other);
TORCH_API at::Tensor & floor_divide_(at::Tensor & self, const at::Scalar & other);
TORCH_API at::Tensor full(at::IntArrayRef size, const at::Scalar & fill_value, c10::optional<at::DimnameList> names, at::TensorOptions options={});
TORCH_API at::Tensor full(at::IntArrayRef size, const at::Scalar & fill_value, c10::optional<at::DimnameList> names, c10::optional<at::ScalarType> dtype, c10::optional<at::Layout> layout, c10::optional<at::Device> device, c10::optional<bool> pin_memory);
TORCH_API at::Tensor full(at::IntArrayRef size, const at::Scalar & fill_value, at::TensorOptions options={});
TORCH_API at::Tensor full(at::IntArrayRef size, const at::Scalar & fill_value, c10::optional<at::ScalarType> dtype, c10::optional<at::Layout> layout, c10::optional<at::Device> device, c10::optional<bool> pin_memory);
TORCH_API at::Tensor & full_out(at::Tensor & out, at::IntArrayRef size, const at::Scalar & fill_value);
TORCH_API at::Tensor & full_outf(at::IntArrayRef size, const at::Scalar & fill_value, at::Tensor & out);
TORCH_API at::Tensor full_like(const at::Tensor & self, const at::Scalar & fill_value, at::TensorOptions options={}, c10::optional<at::MemoryFormat> memory_format=c10::nullopt);
TORCH_API at::Tensor full_like(const at::Tensor & self, const at::Scalar & fill_value, c10::optional<at::ScalarType> dtype, c10::optional<at::Layout> layout, c10::optional<at::Device> device, c10::optional<bool> pin_memory, c10::optional<at::MemoryFormat> memory_format);
TORCH_API at::Tensor grid_sampler(const at::Tensor & input, const at::Tensor & grid, int64_t interpolation_mode, int64_t padding_mode, bool align_corners);
TORCH_API ::std::tuple<at::Tensor,at::Tensor> _grid_sampler_2d_cpu_fallback_backward(const at::Tensor & grad_output, const at::Tensor & input, const at::Tensor & grid, int64_t interpolation_mode, int64_t padding_mode, bool align_corners);
TORCH_API at::Tensor hann_window(int64_t window_length, at::TensorOptions options={});
TORCH_API at::Tensor hann_window(int64_t window_length, c10::optional<at::ScalarType> dtype, c10::optional<at::Layout> layout, c10::optional<at::Device> device, c10::optional<bool> pin_memory);
TORCH_API at::Tensor hann_window(int64_t window_length, bool periodic, at::TensorOptions options={});
TORCH_API at::Tensor hann_window(int64_t window_length, bool periodic, c10::optional<at::ScalarType> dtype, c10::optional<at::Layout> layout, c10::optional<at::Device> device, c10::optional<bool> pin_memory);
TORCH_API at::Tensor hamming_window(int64_t window_length, at::TensorOptions options={});
TORCH_API at::Tensor hamming_window(int64_t window_length, c10::optional<at::ScalarType> dtype, c10::optional<at::Layout> layout, c10::optional<at::Device> device, c10::optional<bool> pin_memory);
TORCH_API at::Tensor hamming_window(int64_t window_length, bool periodic, at::TensorOptions options={});
TORCH_API at::Tensor hamming_window(int64_t window_length, bool periodic, c10::optional<at::ScalarType> dtype, c10::optional<at::Layout> layout, c10::optional<at::Device> device, c10::optional<bool> pin_memory);
TORCH_API at::Tensor hamming_window(int64_t window_length, bool periodic, double alpha, at::TensorOptions options={});
TORCH_API at::Tensor hamming_window(int64_t window_length, bool periodic, double alpha, c10::optional<at::ScalarType> dtype, c10::optional<at::Layout> layout, c10::optional<at::Device> device, c10::optional<bool> pin_memory);
TORCH_API at::Tensor hamming_window(int64_t window_length, bool periodic, double alpha, double beta, at::TensorOptions options={});
TORCH_API at::Tensor hamming_window(int64_t window_length, bool periodic, double alpha, double beta, c10::optional<at::ScalarType> dtype, c10::optional<at::Layout> layout, c10::optional<at::Device> device, c10::optional<bool> pin_memory);
TORCH_API at::Tensor kaiser_window(int64_t window_length, at::TensorOptions options={});
TORCH_API at::Tensor kaiser_window(int64_t window_length, c10::optional<at::ScalarType> dtype, c10::optional<at::Layout> layout, c10::optional<at::Device> device, c10::optional<bool> pin_memory);
TORCH_API at::Tensor kaiser_window(int64_t window_length, bool periodic, at::TensorOptions options={});
TORCH_API at::Tensor kaiser_window(int64_t window_length, bool periodic, c10::optional<at::ScalarType> dtype, c10::optional<at::Layout> layout, c10::optional<at::Device> device, c10::optional<bool> pin_memory);
TORCH_API at::Tensor kaiser_window(int64_t window_length, bool periodic, double beta, at::TensorOptions options={});
TORCH_API at::Tensor kaiser_window(int64_t window_length, bool periodic, double beta, c10::optional<at::ScalarType> dtype, c10::optional<at::Layout> layout, c10::optional<at::Device> device, c10::optional<bool> pin_memory);
TORCH_API at::Tensor hinge_embedding_loss(const at::Tensor & self, const at::Tensor & target, double margin=1.0, int64_t reduction=at::Reduction::Mean);
TORCH_API at::Tensor group_norm(const at::Tensor & input, int64_t num_groups, const c10::optional<at::Tensor> & weight={}, const c10::optional<at::Tensor> & bias={}, double eps=1e-05, bool cudnn_enabled=true);
TORCH_API ::std::tuple<at::Tensor,at::Tensor,at::Tensor> native_group_norm(const at::Tensor & input, const c10::optional<at::Tensor> & weight, const c10::optional<at::Tensor> & bias, int64_t N, int64_t C, int64_t HxW, int64_t group, double eps);
TORCH_API int64_t _cufft_get_plan_cache_size(int64_t device_index);
TORCH_API int64_t _cufft_get_plan_cache_max_size(int64_t device_index);
TORCH_API void _cufft_set_plan_cache_max_size(int64_t device_index, int64_t max_size);
TORCH_API void _cufft_clear_plan_cache(int64_t device_index);
TORCH_API at::Tensor index_copy(const at::Tensor & self, int64_t dim, const at::Tensor & index, const at::Tensor & source);
TORCH_API at::Tensor & index_copy_(at::Tensor & self, at::Dimname dim, const at::Tensor & index, const at::Tensor & source);
TORCH_API at::Tensor index_copy(const at::Tensor & self, at::Dimname dim, const at::Tensor & index, const at::Tensor & source);
TORCH_API at::Tensor index_put(const at::Tensor & self, const c10::List<c10::optional<at::Tensor>> & indices, const at::Tensor & values, bool accumulate=false);
TORCH_API at::Tensor instance_norm(const at::Tensor & input, const c10::optional<at::Tensor> & weight, const c10::optional<at::Tensor> & bias, const c10::optional<at::Tensor> & running_mean, const c10::optional<at::Tensor> & running_var, bool use_input_stats, double momentum, double eps, bool cudnn_enabled);
TORCH_API at::Tensor isclose(const at::Tensor & self, const at::Tensor & other, double rtol=1e-05, double atol=1e-08, bool equal_nan=false);
TORCH_API bool is_distributed(const at::Tensor & self);
TORCH_API bool is_floating_point(const at::Tensor & self);
TORCH_API bool is_complex(const at::Tensor & self);
TORCH_API bool is_conj(const at::Tensor & self);
TORCH_API bool is_neg(const at::Tensor & self);
TORCH_API at::Tensor isreal(const at::Tensor & self);
TORCH_API bool is_nonzero(const at::Tensor & self);
TORCH_API bool is_same_size(const at::Tensor & self, const at::Tensor & other);
TORCH_API bool is_signed(const at::Tensor & self);
TORCH_API bool is_inference(const at::Tensor & self);
TORCH_API at::Tensor kron(const at::Tensor & self, const at::Tensor & other);
TORCH_API at::Tensor & kron_out(at::Tensor & out, const at::Tensor & self, const at::Tensor & other);
TORCH_API at::Tensor & kron_outf(const at::Tensor & self, const at::Tensor & other, at::Tensor & out);
TORCH_API ::std::tuple<at::Tensor,at::Tensor> kthvalue(const at::Tensor & self, int64_t k, at::Dimname dim, bool keepdim=false);
TORCH_API ::std::tuple<at::Tensor &,at::Tensor &> kthvalue_out(at::Tensor & values, at::Tensor & indices, const at::Tensor & self, int64_t k, at::Dimname dim, bool keepdim=false);
TORCH_API ::std::tuple<at::Tensor &,at::Tensor &> kthvalue_outf(const at::Tensor & self, int64_t k, at::Dimname dim, bool keepdim, at::Tensor & values, at::Tensor & indices);
TORCH_API at::Tensor layer_norm(const at::Tensor & input, at::IntArrayRef normalized_shape, const c10::optional<at::Tensor> & weight={}, const c10::optional<at::Tensor> & bias={}, double eps=1e-05, bool cudnn_enable=true);
TORCH_API ::std::tuple<at::Tensor,at::Tensor,at::Tensor> native_layer_norm(const at::Tensor & input, at::IntArrayRef normalized_shape, const c10::optional<at::Tensor> & weight, const c10::optional<at::Tensor> & bias, double eps);
TORCH_API at::Tensor linear(const at::Tensor & input, const at::Tensor & weight, const c10::optional<at::Tensor> & bias={});
TORCH_API at::Tensor & linear_out(at::Tensor & out, const at::Tensor & input, const at::Tensor & weight, const c10::optional<at::Tensor> & bias={});
TORCH_API at::Tensor & linear_outf(const at::Tensor & input, const at::Tensor & weight, const c10::optional<at::Tensor> & bias, at::Tensor & out);
TORCH_API at::Tensor fbgemm_linear_int8_weight_fp32_activation(const at::Tensor & input, const at::Tensor & weight, const at::Tensor & packed, const at::Tensor & col_offsets, const at::Scalar & weight_scale, const at::Scalar & weight_zero_point, const at::Tensor & bias);
TORCH_API at::Tensor fbgemm_linear_int8_weight(const at::Tensor & input, const at::Tensor & weight, const at::Tensor & packed, const at::Tensor & col_offsets, const at::Scalar & weight_scale, const at::Scalar & weight_zero_point, const at::Tensor & bias);
TORCH_API ::std::tuple<at::Tensor,at::Tensor,double,int64_t> fbgemm_linear_quantize_weight(const at::Tensor & input);
TORCH_API at::Tensor fbgemm_pack_gemm_matrix_fp16(const at::Tensor & input);
TORCH_API at::Tensor fbgemm_linear_fp16_weight_fp32_activation(const at::Tensor & input, const at::Tensor & packed_weight, const at::Tensor & bias);
TORCH_API at::Tensor fbgemm_linear_fp16_weight(const at::Tensor & input, const at::Tensor & packed_weight, const at::Tensor & bias);
TORCH_API at::Tensor fbgemm_pack_quantized_matrix(const at::Tensor & input);
TORCH_API at::Tensor fbgemm_pack_quantized_matrix(const at::Tensor & input, int64_t K, int64_t N);
TORCH_API at::Tensor ldexp(const at::Tensor & self, const at::Tensor & other);
TORCH_API at::Tensor & ldexp_out(at::Tensor & out, const at::Tensor & self, const at::Tensor & other);
TORCH_API at::Tensor & ldexp_outf(const at::Tensor & self, const at::Tensor & other, at::Tensor & out);
TORCH_API at::Tensor & ldexp_(at::Tensor & self, const at::Tensor & other);
TORCH_API at::Tensor linspace(const at::Scalar & start, const at::Scalar & end, c10::optional<int64_t> steps=c10::nullopt, at::TensorOptions options={});
TORCH_API at::Tensor linspace(const at::Scalar & start, const at::Scalar & end, c10::optional<int64_t> steps, c10::optional<at::ScalarType> dtype, c10::optional<at::Layout> layout, c10::optional<at::Device> device, c10::optional<bool> pin_memory);
TORCH_API at::Tensor logspace(const at::Scalar & start, const at::Scalar & end, c10::optional<int64_t> steps=c10::nullopt, double base=10.0, at::TensorOptions options={});
TORCH_API at::Tensor logspace(const at::Scalar & start, const at::Scalar & end, c10::optional<int64_t> steps, double base, c10::optional<at::ScalarType> dtype, c10::optional<at::Layout> layout, c10::optional<at::Device> device, c10::optional<bool> pin_memory);
TORCH_API at::Tensor log_softmax(const at::Tensor & self, int64_t dim, c10::optional<at::ScalarType> dtype=c10::nullopt);
TORCH_API at::Tensor log_softmax(const at::Tensor & self, at::Dimname dim, c10::optional<at::ScalarType> dtype=c10::nullopt);
TORCH_API at::Tensor logcumsumexp(const at::Tensor & self, at::Dimname dim);
TORCH_API at::Tensor & logcumsumexp_out(at::Tensor & out, const at::Tensor & self, at::Dimname dim);
TORCH_API at::Tensor & logcumsumexp_outf(const at::Tensor & self, at::Dimname dim, at::Tensor & out);
TORCH_API at::Tensor logsumexp(const at::Tensor & self, at::DimnameList dim, bool keepdim=false);
TORCH_API at::Tensor & logsumexp_out(at::Tensor & out, const at::Tensor & self, at::DimnameList dim, bool keepdim=false);
TORCH_API at::Tensor & logsumexp_outf(const at::Tensor & self, at::DimnameList dim, bool keepdim, at::Tensor & out);
TORCH_API at::Tensor margin_ranking_loss(const at::Tensor & input1, const at::Tensor & input2, const at::Tensor & target, double margin=0.0, int64_t reduction=at::Reduction::Mean);
TORCH_API at::Tensor matmul(const at::Tensor & self, const at::Tensor & other);
TORCH_API at::Tensor & matmul_out(at::Tensor & out, const at::Tensor & self, const at::Tensor & other);
TORCH_API at::Tensor & matmul_outf(const at::Tensor & self, const at::Tensor & other, at::Tensor & out);
TORCH_API at::Tensor matrix_rank(const at::Tensor & self, double tol, bool symmetric=false);
TORCH_API at::Tensor matrix_rank(const at::Tensor & self, bool symmetric=false);
TORCH_API at::Tensor matrix_power(const at::Tensor & self, int64_t n);
TORCH_API at::Tensor & matrix_power_out(at::Tensor & out, const at::Tensor & self, int64_t n);
TORCH_API at::Tensor & matrix_power_outf(const at::Tensor & self, int64_t n, at::Tensor & out);
TORCH_API at::Tensor matrix_exp_backward(const at::Tensor & self, const at::Tensor & grad);
TORCH_API ::std::tuple<at::Tensor,at::Tensor> max(const at::Tensor & self, at::Dimname dim, bool keepdim=false);
TORCH_API ::std::tuple<at::Tensor &,at::Tensor &> max_out(at::Tensor & max, at::Tensor & max_values, const at::Tensor & self, at::Dimname dim, bool keepdim=false);
TORCH_API ::std::tuple<at::Tensor &,at::Tensor &> max_outf(const at::Tensor & self, at::Dimname dim, bool keepdim, at::Tensor & max, at::Tensor & max_values);
TORCH_API at::Tensor value_selecting_reduction_backward(const at::Tensor & grad, int64_t dim, const at::Tensor & indices, at::IntArrayRef sizes, bool keepdim);
TORCH_API ::std::tuple<at::Tensor,at::Tensor> max_pool1d_with_indices(const at::Tensor & self, at::IntArrayRef kernel_size, at::IntArrayRef stride={}, at::IntArrayRef padding=0, at::IntArrayRef dilation=1, bool ceil_mode=false);
TORCH_API at::Tensor max_pool1d(const at::Tensor & self, at::IntArrayRef kernel_size, at::IntArrayRef stride={}, at::IntArrayRef padding=0, at::IntArrayRef dilation=1, bool ceil_mode=false);
TORCH_API at::Tensor max_pool2d(const at::Tensor & self, at::IntArrayRef kernel_size, at::IntArrayRef stride={}, at::IntArrayRef padding=0, at::IntArrayRef dilation=1, bool ceil_mode=false);
TORCH_API at::Tensor max_pool3d(const at::Tensor & self, at::IntArrayRef kernel_size, at::IntArrayRef stride={}, at::IntArrayRef padding=0, at::IntArrayRef dilation=1, bool ceil_mode=false);
TORCH_API at::Tensor mean(const at::Tensor & self, at::DimnameList dim, bool keepdim=false, c10::optional<at::ScalarType> dtype=c10::nullopt);
TORCH_API at::Tensor & mean_out(at::Tensor & out, const at::Tensor & self, at::DimnameList dim, bool keepdim=false, c10::optional<at::ScalarType> dtype=c10::nullopt);
TORCH_API at::Tensor & mean_outf(const at::Tensor & self, at::DimnameList dim, bool keepdim, c10::optional<at::ScalarType> dtype, at::Tensor & out);
TORCH_API at::Tensor nanmean(const at::Tensor & self, at::IntArrayRef dim={}, bool keepdim=false, c10::optional<at::ScalarType> dtype=c10::nullopt);
TORCH_API at::Tensor & nanmean_out(at::Tensor & out, const at::Tensor & self, at::IntArrayRef dim={}, bool keepdim=false, c10::optional<at::ScalarType> dtype=c10::nullopt);
TORCH_API at::Tensor & nanmean_outf(const at::Tensor & self, at::IntArrayRef dim, bool keepdim, c10::optional<at::ScalarType> dtype, at::Tensor & out);
TORCH_API ::std::tuple<at::Tensor,at::Tensor> median(const at::Tensor & self, at::Dimname dim, bool keepdim=false);
TORCH_API ::std::tuple<at::Tensor &,at::Tensor &> median_out(at::Tensor & values, at::Tensor & indices, const at::Tensor & self, at::Dimname dim, bool keepdim=false);
TORCH_API ::std::tuple<at::Tensor &,at::Tensor &> median_outf(const at::Tensor & self, at::Dimname dim, bool keepdim, at::Tensor & values, at::Tensor & indices);
TORCH_API ::std::tuple<at::Tensor,at::Tensor> nanmedian(const at::Tensor & self, at::Dimname dim, bool keepdim=false);
TORCH_API ::std::tuple<at::Tensor &,at::Tensor &> nanmedian_out(at::Tensor & values, at::Tensor & indices, const at::Tensor & self, at::Dimname dim, bool keepdim=false);
TORCH_API ::std::tuple<at::Tensor &,at::Tensor &> nanmedian_outf(const at::Tensor & self, at::Dimname dim, bool keepdim, at::Tensor & values, at::Tensor & indices);
TORCH_API ::std::tuple<at::Tensor,at::Tensor> min(const at::Tensor & self, at::Dimname dim, bool keepdim=false);
TORCH_API ::std::tuple<at::Tensor &,at::Tensor &> min_out(at::Tensor & min, at::Tensor & min_indices, const at::Tensor & self, at::Dimname dim, bool keepdim=false);
TORCH_API ::std::tuple<at::Tensor &,at::Tensor &> min_outf(const at::Tensor & self, at::Dimname dim, bool keepdim, at::Tensor & min, at::Tensor & min_indices);
TORCH_API at::Tensor mkldnn_convolution_backward_input(at::IntArrayRef self_size, const at::Tensor & grad_output, const at::Tensor & weight, at::IntArrayRef padding, at::IntArrayRef stride, at::IntArrayRef dilation, int64_t groups, bool bias_defined);
TORCH_API ::std::tuple<at::Tensor,at::Tensor> mkldnn_convolution_backward_weights(at::IntArrayRef weight_size, const at::Tensor & grad_output, const at::Tensor & self, at::IntArrayRef padding, at::IntArrayRef stride, at::IntArrayRef dilation, int64_t groups, bool bias_defined);
TORCH_API at::Tensor _sparse_mm(const at::Tensor & sparse, const at::Tensor & dense);
TORCH_API ::std::tuple<at::Tensor,at::Tensor> mode(const at::Tensor & self, at::Dimname dim, bool keepdim=false);
TORCH_API ::std::tuple<at::Tensor &,at::Tensor &> mode_out(at::Tensor & values, at::Tensor & indices, const at::Tensor & self, at::Dimname dim, bool keepdim=false);
TORCH_API ::std::tuple<at::Tensor &,at::Tensor &> mode_outf(const at::Tensor & self, at::Dimname dim, bool keepdim, at::Tensor & values, at::Tensor & indices);
TORCH_API at::Tensor multiply(const at::Tensor & self, const at::Tensor & other);
TORCH_API at::Tensor & multiply_out(at::Tensor & out, const at::Tensor & self, const at::Tensor & other);
TORCH_API at::Tensor & multiply_outf(const at::Tensor & self, const at::Tensor & other, at::Tensor & out);
TORCH_API at::Tensor & multiply_(at::Tensor & self, const at::Tensor & other);
TORCH_API at::Tensor multiply(const at::Tensor & self, const at::Scalar & other);
TORCH_API at::Tensor & multiply_(at::Tensor & self, const at::Scalar & other);
TORCH_API at::Tensor narrow(const at::Tensor & self, int64_t dim, int64_t start, int64_t length);
TORCH_API at::Tensor narrow(const at::Tensor & self, int64_t dim, const at::Tensor & start, int64_t length);
TORCH_API bool is_vulkan_available();
TORCH_API bool _nnpack_available();
TORCH_API ::std::tuple<at::Tensor,at::Tensor,at::Tensor> _nnpack_spatial_convolution_backward(const at::Tensor & input, const at::Tensor & grad_output, const at::Tensor & weight, at::IntArrayRef padding, ::std::array<bool,3> output_mask);
TORCH_API at::Tensor _nnpack_spatial_convolution_backward_input(const at::Tensor & input, const at::Tensor & grad_output, const at::Tensor & weight, at::IntArrayRef padding);
TORCH_API at::Tensor _nnpack_spatial_convolution_backward_weight(const at::Tensor & input, at::IntArrayRef weightsize, const at::Tensor & grad_output, at::IntArrayRef padding);
TORCH_API at::Tensor ones(at::IntArrayRef size, c10::optional<at::DimnameList> names, at::TensorOptions options={});
TORCH_API at::Tensor ones(at::IntArrayRef size, c10::optional<at::DimnameList> names, c10::optional<at::ScalarType> dtype, c10::optional<at::Layout> layout, c10::optional<at::Device> device, c10::optional<bool> pin_memory);
TORCH_API at::Tensor ones(at::IntArrayRef size, at::TensorOptions options={});
TORCH_API at::Tensor ones(at::IntArrayRef size, c10::optional<at::ScalarType> dtype, c10::optional<at::Layout> layout, c10::optional<at::Device> device, c10::optional<bool> pin_memory);
TORCH_API at::Tensor & ones_out(at::Tensor & out, at::IntArrayRef size);
TORCH_API at::Tensor & ones_outf(at::IntArrayRef size, at::Tensor & out);
TORCH_API at::Tensor ones_like(const at::Tensor & self, at::TensorOptions options={}, c10::optional<at::MemoryFormat> memory_format=c10::nullopt);
TORCH_API at::Tensor ones_like(const at::Tensor & self, c10::optional<at::ScalarType> dtype, c10::optional<at::Layout> layout, c10::optional<at::Device> device, c10::optional<bool> pin_memory, c10::optional<at::MemoryFormat> memory_format);
TORCH_API at::Tensor pairwise_distance(const at::Tensor & x1, const at::Tensor & x2, double p=2, double eps=1e-06, bool keepdim=false);
TORCH_API at::Tensor cdist(const at::Tensor & x1, const at::Tensor & x2, double p=2, c10::optional<int64_t> compute_mode=c10::nullopt);
TORCH_API at::Tensor pdist(const at::Tensor & self, double p=2);
TORCH_API at::Tensor cosine_similarity(const at::Tensor & x1, const at::Tensor & x2, int64_t dim=1, double eps=1e-08);
TORCH_API at::Tensor movedim(const at::Tensor & self, at::IntArrayRef source, at::IntArrayRef destination);
TORCH_API at::Tensor movedim(const at::Tensor & self, int64_t source, int64_t destination);
TORCH_API at::Tensor moveaxis(const at::Tensor & self, at::IntArrayRef source, at::IntArrayRef destination);
TORCH_API at::Tensor moveaxis(const at::Tensor & self, int64_t source, int64_t destination);
TORCH_API at::Tensor numpy_T(const at::Tensor & self);
TORCH_API at::Tensor pixel_shuffle(const at::Tensor & self, int64_t upscale_factor);
TORCH_API at::Tensor pixel_unshuffle(const at::Tensor & self, int64_t downscale_factor);
TORCH_API at::Tensor pin_memory(const at::Tensor & self, c10::optional<at::Device> device=c10::nullopt);
TORCH_API at::Tensor pinverse(const at::Tensor & self, double rcond=1e-15);
TORCH_API at::Tensor poisson_nll_loss(const at::Tensor & input, const at::Tensor & target, bool log_input, bool full, double eps, int64_t reduction);
TORCH_API at::Tensor scalar_tensor(const at::Scalar & s, at::TensorOptions options={});
TORCH_API at::Tensor scalar_tensor(const at::Scalar & s, c10::optional<at::ScalarType> dtype, c10::optional<at::Layout> layout, c10::optional<at::Device> device, c10::optional<bool> pin_memory);
TORCH_API at::Tensor rand(at::IntArrayRef size, c10::optional<at::DimnameList> names, at::TensorOptions options={});
TORCH_API at::Tensor rand(at::IntArrayRef size, c10::optional<at::DimnameList> names, c10::optional<at::ScalarType> dtype, c10::optional<at::Layout> layout, c10::optional<at::Device> device, c10::optional<bool> pin_memory);
TORCH_API at::Tensor rand(at::IntArrayRef size, c10::optional<at::Generator> generator, c10::optional<at::DimnameList> names, at::TensorOptions options={});
TORCH_API at::Tensor rand(at::IntArrayRef size, c10::optional<at::Generator> generator, c10::optional<at::DimnameList> names, c10::optional<at::ScalarType> dtype, c10::optional<at::Layout> layout, c10::optional<at::Device> device, c10::optional<bool> pin_memory);
TORCH_API at::Tensor rand(at::IntArrayRef size, at::TensorOptions options={});
TORCH_API at::Tensor rand(at::IntArrayRef size, c10::optional<at::ScalarType> dtype, c10::optional<at::Layout> layout, c10::optional<at::Device> device, c10::optional<bool> pin_memory);
TORCH_API at::Tensor rand(at::IntArrayRef size, c10::optional<at::Generator> generator, at::TensorOptions options={});
TORCH_API at::Tensor rand(at::IntArrayRef size, c10::optional<at::Generator> generator, c10::optional<at::ScalarType> dtype, c10::optional<at::Layout> layout, c10::optional<at::Device> device, c10::optional<bool> pin_memory);
TORCH_API at::Tensor & rand_out(at::Tensor & out, at::IntArrayRef size);
TORCH_API at::Tensor & rand_outf(at::IntArrayRef size, at::Tensor & out);
TORCH_API at::Tensor & rand_out(at::Tensor & out, at::IntArrayRef size, c10::optional<at::Generator> generator);
TORCH_API at::Tensor & rand_outf(at::IntArrayRef size, c10::optional<at::Generator> generator, at::Tensor & out);
TORCH_API at::Tensor rand_like(const at::Tensor & self, at::TensorOptions options={}, c10::optional<at::MemoryFormat> memory_format=c10::nullopt);
TORCH_API at::Tensor rand_like(const at::Tensor & self, c10::optional<at::ScalarType> dtype, c10::optional<at::Layout> layout, c10::optional<at::Device> device, c10::optional<bool> pin_memory, c10::optional<at::MemoryFormat> memory_format);
TORCH_API at::Tensor randint(int64_t high, at::IntArrayRef size, at::TensorOptions options={});
TORCH_API at::Tensor randint(int64_t high, at::IntArrayRef size, c10::optional<at::ScalarType> dtype, c10::optional<at::Layout> layout, c10::optional<at::Device> device, c10::optional<bool> pin_memory);
TORCH_API at::Tensor randint(int64_t high, at::IntArrayRef size, c10::optional<at::Generator> generator, at::TensorOptions options={});
TORCH_API at::Tensor randint(int64_t high, at::IntArrayRef size, c10::optional<at::Generator> generator, c10::optional<at::ScalarType> dtype, c10::optional<at::Layout> layout, c10::optional<at::Device> device, c10::optional<bool> pin_memory);
TORCH_API at::Tensor randint(int64_t low, int64_t high, at::IntArrayRef size, at::TensorOptions options={});
TORCH_API at::Tensor randint(int64_t low, int64_t high, at::IntArrayRef size, c10::optional<at::ScalarType> dtype, c10::optional<at::Layout> layout, c10::optional<at::Device> device, c10::optional<bool> pin_memory);
TORCH_API at::Tensor randint(int64_t low, int64_t high, at::IntArrayRef size, c10::optional<at::Generator> generator, at::TensorOptions options={});
TORCH_API at::Tensor randint(int64_t low, int64_t high, at::IntArrayRef size, c10::optional<at::Generator> generator, c10::optional<at::ScalarType> dtype, c10::optional<at::Layout> layout, c10::optional<at::Device> device, c10::optional<bool> pin_memory);
TORCH_API at::Tensor & randint_out(at::Tensor & out, int64_t high, at::IntArrayRef size);
TORCH_API at::Tensor & randint_outf(int64_t high, at::IntArrayRef size, at::Tensor & out);
TORCH_API at::Tensor & randint_out(at::Tensor & out, int64_t high, at::IntArrayRef size, c10::optional<at::Generator> generator);
TORCH_API at::Tensor & randint_outf(int64_t high, at::IntArrayRef size, c10::optional<at::Generator> generator, at::Tensor & out);
TORCH_API at::Tensor & randint_out(at::Tensor & out, int64_t low, int64_t high, at::IntArrayRef size);
TORCH_API at::Tensor & randint_outf(int64_t low, int64_t high, at::IntArrayRef size, at::Tensor & out);
TORCH_API at::Tensor & randint_out(at::Tensor & out, int64_t low, int64_t high, at::IntArrayRef size, c10::optional<at::Generator> generator);
TORCH_API at::Tensor & randint_outf(int64_t low, int64_t high, at::IntArrayRef size, c10::optional<at::Generator> generator, at::Tensor & out);
TORCH_API at::Tensor randint_like(const at::Tensor & self, int64_t high, at::TensorOptions options={}, c10::optional<at::MemoryFormat> memory_format=c10::nullopt);
TORCH_API at::Tensor randint_like(const at::Tensor & self, int64_t high, c10::optional<at::ScalarType> dtype, c10::optional<at::Layout> layout, c10::optional<at::Device> device, c10::optional<bool> pin_memory, c10::optional<at::MemoryFormat> memory_format);
TORCH_API at::Tensor randint_like(const at::Tensor & self, int64_t low, int64_t high, at::TensorOptions options={}, c10::optional<at::MemoryFormat> memory_format=c10::nullopt);
TORCH_API at::Tensor randint_like(const at::Tensor & self, int64_t low, int64_t high, c10::optional<at::ScalarType> dtype, c10::optional<at::Layout> layout, c10::optional<at::Device> device, c10::optional<bool> pin_memory, c10::optional<at::MemoryFormat> memory_format);
TORCH_API at::Tensor randn(at::IntArrayRef size, at::TensorOptions options={});
TORCH_API at::Tensor randn(at::IntArrayRef size, c10::optional<at::ScalarType> dtype, c10::optional<at::Layout> layout, c10::optional<at::Device> device, c10::optional<bool> pin_memory);
TORCH_API at::Tensor randn(at::IntArrayRef size, c10::optional<at::Generator> generator, at::TensorOptions options={});
TORCH_API at::Tensor randn(at::IntArrayRef size, c10::optional<at::Generator> generator, c10::optional<at::ScalarType> dtype, c10::optional<at::Layout> layout, c10::optional<at::Device> device, c10::optional<bool> pin_memory);
TORCH_API at::Tensor randn(at::IntArrayRef size, c10::optional<at::DimnameList> names, at::TensorOptions options={});
TORCH_API at::Tensor randn(at::IntArrayRef size, c10::optional<at::DimnameList> names, c10::optional<at::ScalarType> dtype, c10::optional<at::Layout> layout, c10::optional<at::Device> device, c10::optional<bool> pin_memory);
TORCH_API at::Tensor randn(at::IntArrayRef size, c10::optional<at::Generator> generator, c10::optional<at::DimnameList> names, at::TensorOptions options={});
TORCH_API at::Tensor randn(at::IntArrayRef size, c10::optional<at::Generator> generator, c10::optional<at::DimnameList> names, c10::optional<at::ScalarType> dtype, c10::optional<at::Layout> layout, c10::optional<at::Device> device, c10::optional<bool> pin_memory);
TORCH_API at::Tensor & randn_out(at::Tensor & out, at::IntArrayRef size);
TORCH_API at::Tensor & randn_outf(at::IntArrayRef size, at::Tensor & out);
TORCH_API at::Tensor & randn_out(at::Tensor & out, at::IntArrayRef size, c10::optional<at::Generator> generator);
TORCH_API at::Tensor & randn_outf(at::IntArrayRef size, c10::optional<at::Generator> generator, at::Tensor & out);
TORCH_API at::Tensor randn_like(const at::Tensor & self, at::TensorOptions options={}, c10::optional<at::MemoryFormat> memory_format=c10::nullopt);
TORCH_API at::Tensor randn_like(const at::Tensor & self, c10::optional<at::ScalarType> dtype, c10::optional<at::Layout> layout, c10::optional<at::Device> device, c10::optional<bool> pin_memory, c10::optional<at::MemoryFormat> memory_format);
TORCH_API at::Tensor randperm(int64_t n, at::TensorOptions options=at::kLong);
TORCH_API at::Tensor randperm(int64_t n, c10::optional<at::ScalarType> dtype, c10::optional<at::Layout> layout, c10::optional<at::Device> device, c10::optional<bool> pin_memory);
TORCH_API at::Tensor randperm(int64_t n, c10::optional<at::Generator> generator, at::TensorOptions options=at::kLong);
TORCH_API at::Tensor randperm(int64_t n, c10::optional<at::Generator> generator, c10::optional<at::ScalarType> dtype, c10::optional<at::Layout> layout, c10::optional<at::Device> device, c10::optional<bool> pin_memory);
TORCH_API at::Tensor & randperm_out(at::Tensor & out, int64_t n);
TORCH_API at::Tensor & randperm_outf(int64_t n, at::Tensor & out);
TORCH_API at::Tensor range(const at::Scalar & start, const at::Scalar & end, const at::Scalar & step=1, at::TensorOptions options={});
TORCH_API at::Tensor range(const at::Scalar & start, const at::Scalar & end, const at::Scalar & step, c10::optional<at::ScalarType> dtype, c10::optional<at::Layout> layout, c10::optional<at::Device> device, c10::optional<bool> pin_memory);
TORCH_API at::Tensor range(const at::Scalar & start, const at::Scalar & end, at::TensorOptions options={});
TORCH_API at::Tensor range(const at::Scalar & start, const at::Scalar & end, c10::optional<at::ScalarType> dtype, c10::optional<at::Layout> layout, c10::optional<at::Device> device, c10::optional<bool> pin_memory);
TORCH_API at::Tensor ravel(const at::Tensor & self);
TORCH_API at::Tensor negative(const at::Tensor & self);
TORCH_API at::Tensor & negative_out(at::Tensor & out, const at::Tensor & self);
TORCH_API at::Tensor & negative_outf(const at::Tensor & self, at::Tensor & out);
TORCH_API at::Tensor & negative_(at::Tensor & self);
TORCH_API at::Tensor repeat_interleave(const at::Tensor & self, const at::Tensor & repeats, c10::optional<int64_t> dim=c10::nullopt, c10::optional<int64_t> output_size=c10::nullopt);
TORCH_API at::Tensor repeat_interleave(const at::Tensor & self, int64_t repeats, c10::optional<int64_t> dim=c10::nullopt, c10::optional<int64_t> output_size=c10::nullopt);
TORCH_API at::Tensor reshape(const at::Tensor & self, at::IntArrayRef shape);
TORCH_API at::Tensor reshape_as(const at::Tensor & self, const at::Tensor & other);
TORCH_API at::Tensor rrelu(const at::Tensor & self, const at::Scalar & lower=0.125, const at::Scalar & upper=0.3333333333333333, bool training=false, c10::optional<at::Generator> generator=c10::nullopt);
TORCH_API at::Tensor & rrelu_(at::Tensor & self, const at::Scalar & lower=0.125, const at::Scalar & upper=0.3333333333333333, bool training=false, c10::optional<at::Generator> generator=c10::nullopt);
TORCH_API at::Tensor relu6(const at::Tensor & self);
TORCH_API at::Tensor & relu6_(at::Tensor & self);
TORCH_API at::Tensor infinitely_differentiable_gelu_backward(const at::Tensor & grad, const at::Tensor & self);
TORCH_API at::Tensor select(const at::Tensor & self, at::Dimname dim, int64_t index);
TORCH_API at::Tensor selu(const at::Tensor & self);
TORCH_API at::Tensor & selu_(at::Tensor & self);
TORCH_API at::Tensor silu_backward(const at::Tensor & grad_output, const at::Tensor & self);
TORCH_API at::Tensor mish_backward(const at::Tensor & grad_output, const at::Tensor & self);
TORCH_API int64_t size(const at::Tensor & self, int64_t dim);
TORCH_API int64_t size(const at::Tensor & self, at::Dimname dim);
TORCH_API at::Tensor smm(const at::Tensor & self, const at::Tensor & mat2);
TORCH_API at::Tensor softmax(const at::Tensor & self, int64_t dim, c10::optional<at::ScalarType> dtype=c10::nullopt);
TORCH_API at::Tensor softmax(const at::Tensor & self, at::Dimname dim, c10::optional<at::ScalarType> dtype=c10::nullopt);
TORCH_API ::std::vector<at::Tensor> hsplit(const at::Tensor & self, int64_t sections);
TORCH_API ::std::vector<at::Tensor> hsplit(const at::Tensor & self, at::IntArrayRef indices);
TORCH_API ::std::vector<at::Tensor> vsplit(const at::Tensor & self, int64_t sections);
TORCH_API ::std::vector<at::Tensor> vsplit(const at::Tensor & self, at::IntArrayRef indices);
TORCH_API ::std::vector<at::Tensor> dsplit(const at::Tensor & self, int64_t sections);
TORCH_API ::std::vector<at::Tensor> dsplit(const at::Tensor & self, at::IntArrayRef indices);
TORCH_API at::Tensor squeeze(const at::Tensor & self, at::Dimname dim);
TORCH_API at::Tensor & squeeze_(at::Tensor & self, at::Dimname dim);
TORCH_API at::Tensor sspaddmm(const at::Tensor & self, const at::Tensor & mat1, const at::Tensor & mat2, const at::Scalar & beta=1, const at::Scalar & alpha=1);
TORCH_API at::Tensor hstack(at::TensorList tensors);
TORCH_API at::Tensor & hstack_out(at::Tensor & out, at::TensorList tensors);
TORCH_API at::Tensor & hstack_outf(at::TensorList tensors, at::Tensor & out);
TORCH_API at::Tensor vstack(at::TensorList tensors);
TORCH_API at::Tensor & vstack_out(at::Tensor & out, at::TensorList tensors);
TORCH_API at::Tensor & vstack_outf(at::TensorList tensors, at::Tensor & out);
TORCH_API at::Tensor dstack(at::TensorList tensors);
TORCH_API at::Tensor & dstack_out(at::Tensor & out, at::TensorList tensors);
TORCH_API at::Tensor & dstack_outf(at::TensorList tensors, at::Tensor & out);
TORCH_API at::Tensor stft(const at::Tensor & self, int64_t n_fft, c10::optional<int64_t> hop_length=c10::nullopt, c10::optional<int64_t> win_length=c10::nullopt, const c10::optional<at::Tensor> & window={}, bool normalized=false, c10::optional<bool> onesided=c10::nullopt, c10::optional<bool> return_complex=c10::nullopt);
TORCH_API at::Tensor istft(const at::Tensor & self, int64_t n_fft, c10::optional<int64_t> hop_length=c10::nullopt, c10::optional<int64_t> win_length=c10::nullopt, const c10::optional<at::Tensor> & window={}, bool center=true, bool normalized=false, c10::optional<bool> onesided=c10::nullopt, c10::optional<int64_t> length=c10::nullopt, bool return_complex=false);
TORCH_API int64_t stride(const at::Tensor & self, int64_t dim);
TORCH_API int64_t stride(const at::Tensor & self, at::Dimname dim);
TORCH_API at::Tensor sum(const at::Tensor & self, at::DimnameList dim, bool keepdim=false, c10::optional<at::ScalarType> dtype=c10::nullopt);
TORCH_API at::Tensor & sum_out(at::Tensor & out, const at::Tensor & self, at::DimnameList dim, bool keepdim=false, c10::optional<at::ScalarType> dtype=c10::nullopt);
TORCH_API at::Tensor & sum_outf(const at::Tensor & self, at::DimnameList dim, bool keepdim, c10::optional<at::ScalarType> dtype, at::Tensor & out);
TORCH_API at::Tensor sum_to_size(const at::Tensor & self, at::IntArrayRef size);
TORCH_API at::Tensor square(const at::Tensor & self);
TORCH_API at::Tensor & square_(at::Tensor & self);
TORCH_API at::Tensor std(const at::Tensor & self, bool unbiased=true);
TORCH_API at::Tensor std(const at::Tensor & self, at::IntArrayRef dim, bool unbiased=true, bool keepdim=false);
TORCH_API at::Tensor & std_out(at::Tensor & out, const at::Tensor & self, at::IntArrayRef dim, bool unbiased=true, bool keepdim=false);
TORCH_API at::Tensor & std_outf(const at::Tensor & self, at::IntArrayRef dim, bool unbiased, bool keepdim, at::Tensor & out);
TORCH_API ::std::tuple<at::Tensor,at::Tensor> std_mean(const at::Tensor & self, bool unbiased=true);
TORCH_API ::std::tuple<at::Tensor,at::Tensor> std_mean(const at::Tensor & self, at::IntArrayRef dim, bool unbiased=true, bool keepdim=false);
TORCH_API ::std::tuple<at::Tensor,at::Tensor> std_mean(const at::Tensor & self, at::DimnameList dim, bool unbiased=true, bool keepdim=false);
TORCH_API ::std::tuple<at::Tensor,at::Tensor> std_mean(const at::Tensor & self, at::DimnameList dim, c10::optional<int64_t> correction, bool keepdim=false);
TORCH_API at::Tensor std(const at::Tensor & self, at::DimnameList dim, bool unbiased=true, bool keepdim=false);
TORCH_API at::Tensor & std_out(at::Tensor & out, const at::Tensor & self, at::DimnameList dim, bool unbiased=true, bool keepdim=false);
TORCH_API at::Tensor & std_outf(const at::Tensor & self, at::DimnameList dim, bool unbiased, bool keepdim, at::Tensor & out);
TORCH_API at::Tensor std(const at::Tensor & self, at::DimnameList dim, c10::optional<int64_t> correction, bool keepdim=false);
TORCH_API at::Tensor & std_out(at::Tensor & out, const at::Tensor & self, at::DimnameList dim, c10::optional<int64_t> correction, bool keepdim=false);
TORCH_API at::Tensor & std_outf(const at::Tensor & self, at::DimnameList dim, c10::optional<int64_t> correction, bool keepdim, at::Tensor & out);
TORCH_API at::Tensor prod(const at::Tensor & self, at::Dimname dim, bool keepdim=false, c10::optional<at::ScalarType> dtype=c10::nullopt);
TORCH_API at::Tensor & prod_out(at::Tensor & out, const at::Tensor & self, at::Dimname dim, bool keepdim=false, c10::optional<at::ScalarType> dtype=c10::nullopt);
TORCH_API at::Tensor & prod_outf(const at::Tensor & self, at::Dimname dim, bool keepdim, c10::optional<at::ScalarType> dtype, at::Tensor & out);
TORCH_API at::Tensor tensordot(const at::Tensor & self, const at::Tensor & other, at::IntArrayRef dims_self, at::IntArrayRef dims_other);
TORCH_API at::Tensor tile(const at::Tensor & self, at::IntArrayRef dims);
TORCH_API at::Tensor transpose(const at::Tensor & self, at::Dimname dim0, at::Dimname dim1);
TORCH_API at::Tensor one_hot(const at::Tensor & self, int64_t num_classes=-1);
TORCH_API at::Tensor fliplr(const at::Tensor & self);
TORCH_API at::Tensor flipud(const at::Tensor & self);
TORCH_API at::Tensor trapezoid(const at::Tensor & y, const at::Tensor & x, int64_t dim=-1);
TORCH_API at::Tensor trapezoid(const at::Tensor & y, const at::Scalar & dx=1, int64_t dim=-1);
TORCH_API at::Tensor trapz(const at::Tensor & y, const at::Tensor & x, int64_t dim=-1);
TORCH_API at::Tensor trapz(const at::Tensor & y, double dx=1, int64_t dim=-1);
TORCH_API at::Tensor triplet_margin_loss(const at::Tensor & anchor, const at::Tensor & positive, const at::Tensor & negative, double margin=1.0, double p=2, double eps=1e-06, bool swap=false, int64_t reduction=at::Reduction::Mean);
TORCH_API at::Tensor fix(const at::Tensor & self);
TORCH_API at::Tensor & fix_out(at::Tensor & out, const at::Tensor & self);
TORCH_API at::Tensor & fix_outf(const at::Tensor & self, at::Tensor & out);
TORCH_API at::Tensor & fix_(at::Tensor & self);
TORCH_API at::Tensor type_as(const at::Tensor & self, const at::Tensor & other);
TORCH_API bool _has_compatible_shallow_copy_type(const at::Tensor & self, const at::Tensor & from);
TORCH_API at::Tensor vander(const at::Tensor & x, c10::optional<int64_t> N=c10::nullopt, bool increasing=false);
TORCH_API at::Tensor var(const at::Tensor & self, bool unbiased=true);
TORCH_API at::Tensor var(const at::Tensor & self, at::IntArrayRef dim, bool unbiased=true, bool keepdim=false);
TORCH_API at::Tensor & var_out(at::Tensor & out, const at::Tensor & self, at::IntArrayRef dim, bool unbiased=true, bool keepdim=false);
TORCH_API at::Tensor & var_outf(const at::Tensor & self, at::IntArrayRef dim, bool unbiased, bool keepdim, at::Tensor & out);
TORCH_API at::Tensor var(const at::Tensor & self, at::DimnameList dim, bool unbiased=true, bool keepdim=false);
TORCH_API at::Tensor & var_out(at::Tensor & out, const at::Tensor & self, at::DimnameList dim, bool unbiased=true, bool keepdim=false);
TORCH_API at::Tensor & var_outf(const at::Tensor & self, at::DimnameList dim, bool unbiased, bool keepdim, at::Tensor & out);
TORCH_API at::Tensor var(const at::Tensor & self, at::DimnameList dim, c10::optional<int64_t> correction, bool keepdim=false);
TORCH_API at::Tensor & var_out(at::Tensor & out, const at::Tensor & self, at::DimnameList dim, c10::optional<int64_t> correction, bool keepdim=false);
TORCH_API at::Tensor & var_outf(const at::Tensor & self, at::DimnameList dim, c10::optional<int64_t> correction, bool keepdim, at::Tensor & out);
TORCH_API ::std::tuple<at::Tensor,at::Tensor> var_mean(const at::Tensor & self, bool unbiased=true);
TORCH_API ::std::tuple<at::Tensor,at::Tensor> var_mean(const at::Tensor & self, at::IntArrayRef dim, bool unbiased=true, bool keepdim=false);
TORCH_API ::std::tuple<at::Tensor,at::Tensor> var_mean(const at::Tensor & self, at::DimnameList dim, bool unbiased=true, bool keepdim=false);
TORCH_API ::std::tuple<at::Tensor,at::Tensor> var_mean(const at::Tensor & self, at::DimnameList dim, c10::optional<int64_t> correction, bool keepdim=false);
TORCH_API at::Tensor view_as(const at::Tensor & self, const at::Tensor & other);
TORCH_API at::Tensor where(const at::Tensor & condition, const at::Tensor & self, const at::Tensor & other);
TORCH_API at::Tensor where(const at::Tensor & condition, const at::Scalar & self, const at::Tensor & other);
TORCH_API at::Tensor where(const at::Tensor & condition, const at::Tensor & self, const at::Scalar & other);
TORCH_API at::Tensor where(const at::Tensor & condition, const at::Scalar & self, const at::Scalar & other);
TORCH_API ::std::vector<at::Tensor> where(const at::Tensor & condition);
TORCH_API at::Tensor norm_except_dim(const at::Tensor & v, int64_t pow=2, int64_t dim=0);
TORCH_API at::Tensor _weight_norm(const at::Tensor & v, const at::Tensor & g, int64_t dim=0);
TORCH_API ::std::tuple<at::Tensor,at::Tensor> _weight_norm_differentiable_backward(const at::Tensor & grad_w, const at::Tensor & saved_v, const at::Tensor & saved_g, const at::Tensor & saved_norms, int64_t dim);
TORCH_API at::Tensor zeros(at::IntArrayRef size, c10::optional<at::DimnameList> names, at::TensorOptions options={});
TORCH_API at::Tensor zeros(at::IntArrayRef size, c10::optional<at::DimnameList> names, c10::optional<at::ScalarType> dtype, c10::optional<at::Layout> layout, c10::optional<at::Device> device, c10::optional<bool> pin_memory);
TORCH_API at::Tensor zeros(at::IntArrayRef size, at::TensorOptions options={});
TORCH_API at::Tensor zeros(at::IntArrayRef size, c10::optional<at::ScalarType> dtype, c10::optional<at::Layout> layout, c10::optional<at::Device> device, c10::optional<bool> pin_memory);
TORCH_API at::Tensor & zeros_out(at::Tensor & out, at::IntArrayRef size);
TORCH_API at::Tensor & zeros_outf(at::IntArrayRef size, at::Tensor & out);
TORCH_API at::Tensor zeros_like(const at::Tensor & self, at::TensorOptions options={}, c10::optional<at::MemoryFormat> memory_format=c10::nullopt);
TORCH_API at::Tensor zeros_like(const at::Tensor & self, c10::optional<at::ScalarType> dtype, c10::optional<at::Layout> layout, c10::optional<at::Device> device, c10::optional<bool> pin_memory, c10::optional<at::MemoryFormat> memory_format);
TORCH_API at::Tensor _sparse_sum(const at::Tensor & self);
TORCH_API at::Tensor _sparse_sum(const at::Tensor & self, at::ScalarType dtype);
TORCH_API at::Tensor _sparse_sum(const at::Tensor & self, at::IntArrayRef dim, at::ScalarType dtype);
TORCH_API at::Tensor _sparse_softmax(const at::Tensor & self, int64_t dim, c10::optional<at::ScalarType> dtype=c10::nullopt);
TORCH_API at::Tensor _sparse_softmax(const at::Tensor & self, at::Dimname dim, c10::optional<at::ScalarType> dtype=c10::nullopt);
TORCH_API at::Tensor _sparse_log_softmax(const at::Tensor & self, int64_t dim, c10::optional<at::ScalarType> dtype=c10::nullopt);
TORCH_API at::Tensor _sparse_log_softmax(const at::Tensor & self, at::Dimname dim, c10::optional<at::ScalarType> dtype=c10::nullopt);
TORCH_API at::Tensor norm(const at::Tensor & self, const c10::optional<at::Scalar> & p, at::DimnameList dim, bool keepdim, at::ScalarType dtype);
TORCH_API at::Tensor & norm_out(at::Tensor & out, const at::Tensor & self, const c10::optional<at::Scalar> & p, at::DimnameList dim, bool keepdim, at::ScalarType dtype);
TORCH_API at::Tensor & norm_outf(const at::Tensor & self, const c10::optional<at::Scalar> & p, at::DimnameList dim, bool keepdim, at::ScalarType dtype, at::Tensor & out);
TORCH_API at::Tensor norm(const at::Tensor & self, const c10::optional<at::Scalar> & p, at::DimnameList dim, bool keepdim=false);
TORCH_API at::Tensor & norm_out(at::Tensor & out, const at::Tensor & self, const c10::optional<at::Scalar> & p, at::DimnameList dim, bool keepdim=false);
TORCH_API at::Tensor & norm_outf(const at::Tensor & self, const c10::optional<at::Scalar> & p, at::DimnameList dim, bool keepdim, at::Tensor & out);
TORCH_API at::Tensor frobenius_norm(const at::Tensor & self);
TORCH_API at::Tensor frobenius_norm(const at::Tensor & self, at::IntArrayRef dim, bool keepdim=false);
TORCH_API at::Tensor & frobenius_norm_out(at::Tensor & out, const at::Tensor & self, at::IntArrayRef dim, bool keepdim=false);
TORCH_API at::Tensor & frobenius_norm_outf(const at::Tensor & self, at::IntArrayRef dim, bool keepdim, at::Tensor & out);
TORCH_API at::Tensor nuclear_norm(const at::Tensor & self, bool keepdim=false);
TORCH_API at::Tensor & nuclear_norm_out(at::Tensor & out, const at::Tensor & self, bool keepdim=false);
TORCH_API at::Tensor & nuclear_norm_outf(const at::Tensor & self, bool keepdim, at::Tensor & out);
TORCH_API at::Tensor nuclear_norm(const at::Tensor & self, at::IntArrayRef dim, bool keepdim=false);
TORCH_API at::Tensor & nuclear_norm_out(at::Tensor & out, const at::Tensor & self, at::IntArrayRef dim, bool keepdim=false);
TORCH_API at::Tensor & nuclear_norm_outf(const at::Tensor & self, at::IntArrayRef dim, bool keepdim, at::Tensor & out);
TORCH_API at::Tensor positive(const at::Tensor & self);
TORCH_API at::Tensor subtract(const at::Tensor & self, const at::Tensor & other, const at::Scalar & alpha=1);
TORCH_API at::Tensor & subtract_out(at::Tensor & out, const at::Tensor & self, const at::Tensor & other, const at::Scalar & alpha=1);
TORCH_API at::Tensor & subtract_outf(const at::Tensor & self, const at::Tensor & other, const at::Scalar & alpha, at::Tensor & out);
TORCH_API at::Tensor & subtract_(at::Tensor & self, const at::Tensor & other, const at::Scalar & alpha=1);
TORCH_API at::Tensor subtract(const at::Tensor & self, const at::Scalar & other, const at::Scalar & alpha=1);
TORCH_API at::Tensor & subtract_(at::Tensor & self, const at::Scalar & other, const at::Scalar & alpha=1);
TORCH_API at::Tensor sparse_csr_tensor(const at::Tensor & crow_indices, const at::Tensor & col_indices, const at::Tensor & values, at::IntArrayRef size, at::TensorOptions options);
TORCH_API at::Tensor sparse_csr_tensor(const at::Tensor & crow_indices, const at::Tensor & col_indices, const at::Tensor & values, at::IntArrayRef size, c10::optional<at::ScalarType> dtype, c10::optional<at::Layout> layout, c10::optional<at::Device> device, c10::optional<bool> pin_memory);
TORCH_API at::Tensor sparse_csr_tensor(const at::Tensor & crow_indices, const at::Tensor & col_indices, const at::Tensor & values, at::TensorOptions options);
TORCH_API at::Tensor sparse_csr_tensor(const at::Tensor & crow_indices, const at::Tensor & col_indices, const at::Tensor & values, c10::optional<at::ScalarType> dtype, c10::optional<at::Layout> layout, c10::optional<at::Device> device, c10::optional<bool> pin_memory);
TORCH_API at::Tensor _sparse_csr_tensor_unsafe(const at::Tensor & crow_indices, const at::Tensor & col_indices, const at::Tensor & values, at::IntArrayRef size, at::TensorOptions options={});
TORCH_API at::Tensor _sparse_csr_tensor_unsafe(const at::Tensor & crow_indices, const at::Tensor & col_indices, const at::Tensor & values, at::IntArrayRef size, c10::optional<at::ScalarType> dtype, c10::optional<at::Layout> layout, c10::optional<at::Device> device, c10::optional<bool> pin_memory);
TORCH_API at::Tensor sparse_coo_tensor(at::IntArrayRef size, at::TensorOptions options);
TORCH_API at::Tensor sparse_coo_tensor(at::IntArrayRef size, c10::optional<at::ScalarType> dtype, c10::optional<at::Layout> layout, c10::optional<at::Device> device, c10::optional<bool> pin_memory);
TORCH_API at::Tensor sparse_coo_tensor(const at::Tensor & indices, const at::Tensor & values, at::TensorOptions options={});
TORCH_API at::Tensor sparse_coo_tensor(const at::Tensor & indices, const at::Tensor & values, c10::optional<at::ScalarType> dtype, c10::optional<at::Layout> layout, c10::optional<at::Device> device, c10::optional<bool> pin_memory);
TORCH_API at::Tensor sparse_coo_tensor(const at::Tensor & indices, const at::Tensor & values, at::IntArrayRef size, at::TensorOptions options={});
TORCH_API at::Tensor sparse_coo_tensor(const at::Tensor & indices, const at::Tensor & values, at::IntArrayRef size, c10::optional<at::ScalarType> dtype, c10::optional<at::Layout> layout, c10::optional<at::Device> device, c10::optional<bool> pin_memory);
TORCH_API at::Tensor _sparse_coo_tensor_unsafe(const at::Tensor & indices, const at::Tensor & values, at::IntArrayRef size, at::TensorOptions options={});
TORCH_API at::Tensor _sparse_coo_tensor_unsafe(const at::Tensor & indices, const at::Tensor & values, at::IntArrayRef size, c10::optional<at::ScalarType> dtype, c10::optional<at::Layout> layout, c10::optional<at::Device> device, c10::optional<bool> pin_memory);
TORCH_API void _validate_sparse_coo_tensor_args(const at::Tensor & indices, const at::Tensor & values, at::IntArrayRef size);
TORCH_API void _validate_sparse_csr_tensor_args(const at::Tensor & crow_indices, const at::Tensor & col_indices, const at::Tensor & values, at::IntArrayRef size);
TORCH_API ::std::vector<at::Tensor> _to_cpu(at::TensorList tensors);
TORCH_API at::Tensor to_dense_backward(const at::Tensor & grad, const at::Tensor & input);
TORCH_API at::Tensor coalesce(const at::Tensor & self);
TORCH_API ::std::vector<at::Tensor> unbind(const at::Tensor & self, at::Dimname dim);
TORCH_API at::Tensor to_mkldnn_backward(const at::Tensor & grad, const at::Tensor & input);
TORCH_API at::Tensor fake_quantize_per_tensor_affine(const at::Tensor & self, double scale, int64_t zero_point, int64_t quant_min, int64_t quant_max);
TORCH_API at::Tensor fake_quantize_per_tensor_affine(const at::Tensor & self, const at::Tensor & scale, const at::Tensor & zero_point, int64_t quant_min, int64_t quant_max);
TORCH_API at::Tensor fake_quantize_per_tensor_affine_cachemask_backward(const at::Tensor & grad, const at::Tensor & mask);
TORCH_API ::std::tuple<at::Tensor,at::Tensor,at::Tensor> _fake_quantize_learnable_per_tensor_affine_backward(const at::Tensor & grad, const at::Tensor & self, const at::Tensor & scale, const at::Tensor & zero_point, int64_t quant_min, int64_t quant_max, double grad_factor=1.0);
TORCH_API at::Tensor fake_quantize_per_channel_affine(const at::Tensor & self, const at::Tensor & scale, const at::Tensor & zero_point, int64_t axis, int64_t quant_min, int64_t quant_max);
TORCH_API at::Tensor fake_quantize_per_channel_affine_cachemask_backward(const at::Tensor & grad, const at::Tensor & mask);
TORCH_API ::std::tuple<at::Tensor,at::Tensor,at::Tensor> _fake_quantize_learnable_per_channel_affine_backward(const at::Tensor & grad, const at::Tensor & self, const at::Tensor & scale, const at::Tensor & zero_point, int64_t axis, int64_t quant_min, int64_t quant_max, double grad_factor=1.0);
TORCH_API at::Tensor fused_moving_avg_obs_fake_quant(const at::Tensor & self, const at::Tensor & observer_on, const at::Tensor & fake_quant_on, at::Tensor & running_min, at::Tensor & running_max, at::Tensor & scale, at::Tensor & zero_point, double averaging_const, int64_t quant_min, int64_t quant_max, int64_t ch_axis, bool per_row_fake_quant=false, bool symmetric_quant=false);
TORCH_API ::std::tuple<double,int64_t> _choose_qparams_per_tensor(const at::Tensor & self, bool reduce_range=false);
TORCH_API at::Tensor _saturate_weight_to_fp16(const at::Tensor & weight);
TORCH_API ::std::tuple<at::Tensor,at::Tensor> choose_qparams_optimized(const at::Tensor & input, int64_t numel, int64_t n_bins, double ratio, int64_t bit_width);
TORCH_API at::Tensor to(const at::Tensor & self, at::TensorOptions options={}, bool non_blocking=false, bool copy=false, c10::optional<at::MemoryFormat> memory_format=c10::nullopt);
TORCH_API at::Tensor to(const at::Tensor & self, c10::optional<at::ScalarType> dtype, c10::optional<at::Layout> layout, c10::optional<at::Device> device, c10::optional<bool> pin_memory, bool non_blocking, bool copy, c10::optional<at::MemoryFormat> memory_format);
TORCH_API at::Tensor to(const at::Tensor & self, at::Device device, at::ScalarType dtype, bool non_blocking=false, bool copy=false, c10::optional<at::MemoryFormat> memory_format=c10::nullopt);
TORCH_API at::Tensor to(const at::Tensor & self, at::ScalarType dtype, bool non_blocking=false, bool copy=false, c10::optional<at::MemoryFormat> memory_format=c10::nullopt);
TORCH_API at::Tensor to(const at::Tensor & self, const at::Tensor & other, bool non_blocking=false, bool copy=false, c10::optional<at::MemoryFormat> memory_format=c10::nullopt);
TORCH_API ::std::vector<at::Tensor> meshgrid(at::TensorList tensors);
TORCH_API ::std::vector<at::Tensor> meshgrid(at::TensorList tensors, c10::string_view indexing);
TORCH_API at::Tensor cartesian_prod(at::TensorList tensors);
TORCH_API at::Tensor combinations(const at::Tensor & self, int64_t r=2, bool with_replacement=false);
TORCH_API at::Scalar item(const at::Tensor & self);
TORCH_API at::ScalarType result_type(const at::Tensor & tensor, const at::Tensor & other);
TORCH_API at::ScalarType result_type(const at::Tensor & tensor, const at::Scalar & other);
TORCH_API at::ScalarType result_type(const at::Scalar & scalar, const at::Tensor & tensor);
TORCH_API at::ScalarType result_type(const at::Scalar & scalar1, const at::Scalar & scalar2);
TORCH_API bool can_cast(at::ScalarType from, at::ScalarType to);
TORCH_API at::ScalarType promote_types(at::ScalarType type1, at::ScalarType type2);
TORCH_API ::std::tuple<at::Tensor,at::Tensor,at::Tensor,at::Tensor,at::Tensor> _thnn_differentiable_lstm_cell_backward(const c10::optional<at::Tensor> & grad_hy, const c10::optional<at::Tensor> & grad_cy, const at::Tensor & input_gates, const at::Tensor & hidden_gates, const c10::optional<at::Tensor> & input_bias, const c10::optional<at::Tensor> & hidden_bias, const at::Tensor & cx, const at::Tensor & cy);
TORCH_API ::std::tuple<at::Tensor,at::Tensor,at::Tensor,at::Tensor,at::Tensor> _thnn_differentiable_gru_cell_backward(const at::Tensor & grad_hy, const at::Tensor & input_gates, const at::Tensor & hidden_gates, const at::Tensor & hx, const c10::optional<at::Tensor> & input_bias, const c10::optional<at::Tensor> & hidden_bias);
TORCH_API ::std::tuple<at::Tensor,at::Tensor,at::Tensor> lstm(const at::Tensor & input, at::TensorList hx, at::TensorList params, bool has_biases, int64_t num_layers, double dropout, bool train, bool bidirectional, bool batch_first);
TORCH_API ::std::tuple<at::Tensor,at::Tensor,at::Tensor> lstm(const at::Tensor & data, const at::Tensor & batch_sizes, at::TensorList hx, at::TensorList params, bool has_biases, int64_t num_layers, double dropout, bool train, bool bidirectional);
TORCH_API ::std::tuple<at::Tensor,at::Tensor> gru(const at::Tensor & input, const at::Tensor & hx, at::TensorList params, bool has_biases, int64_t num_layers, double dropout, bool train, bool bidirectional, bool batch_first);
TORCH_API ::std::tuple<at::Tensor,at::Tensor> gru(const at::Tensor & data, const at::Tensor & batch_sizes, const at::Tensor & hx, at::TensorList params, bool has_biases, int64_t num_layers, double dropout, bool train, bool bidirectional);
TORCH_API ::std::tuple<at::Tensor,at::Tensor> rnn_tanh(const at::Tensor & input, const at::Tensor & hx, at::TensorList params, bool has_biases, int64_t num_layers, double dropout, bool train, bool bidirectional, bool batch_first);
TORCH_API ::std::tuple<at::Tensor,at::Tensor> rnn_tanh(const at::Tensor & data, const at::Tensor & batch_sizes, const at::Tensor & hx, at::TensorList params, bool has_biases, int64_t num_layers, double dropout, bool train, bool bidirectional);
TORCH_API ::std::tuple<at::Tensor,at::Tensor> rnn_relu(const at::Tensor & input, const at::Tensor & hx, at::TensorList params, bool has_biases, int64_t num_layers, double dropout, bool train, bool bidirectional, bool batch_first);
TORCH_API ::std::tuple<at::Tensor,at::Tensor> rnn_relu(const at::Tensor & data, const at::Tensor & batch_sizes, const at::Tensor & hx, at::TensorList params, bool has_biases, int64_t num_layers, double dropout, bool train, bool bidirectional);
TORCH_API ::std::tuple<at::Tensor,at::Tensor> lstm_cell(const at::Tensor & input, at::TensorList hx, const at::Tensor & w_ih, const at::Tensor & w_hh, const c10::optional<at::Tensor> & b_ih={}, const c10::optional<at::Tensor> & b_hh={});
TORCH_API at::Tensor gru_cell(const at::Tensor & input, const at::Tensor & hx, const at::Tensor & w_ih, const at::Tensor & w_hh, const c10::optional<at::Tensor> & b_ih={}, const c10::optional<at::Tensor> & b_hh={});
TORCH_API at::Tensor rnn_tanh_cell(const at::Tensor & input, const at::Tensor & hx, const at::Tensor & w_ih, const at::Tensor & w_hh, const c10::optional<at::Tensor> & b_ih={}, const c10::optional<at::Tensor> & b_hh={});
TORCH_API at::Tensor rnn_relu_cell(const at::Tensor & input, const at::Tensor & hx, const at::Tensor & w_ih, const at::Tensor & w_hh, const c10::optional<at::Tensor> & b_ih={}, const c10::optional<at::Tensor> & b_hh={});
TORCH_API ::std::tuple<at::Tensor,at::Tensor> quantized_lstm_cell(const at::Tensor & input, at::TensorList hx, const at::Tensor & w_ih, const at::Tensor & w_hh, const at::Tensor & b_ih, const at::Tensor & b_hh, const at::Tensor & packed_ih, const at::Tensor & packed_hh, const at::Tensor & col_offsets_ih, const at::Tensor & col_offsets_hh, const at::Scalar & scale_ih, const at::Scalar & scale_hh, const at::Scalar & zero_point_ih, const at::Scalar & zero_point_hh);
TORCH_API at::Tensor quantized_gru_cell(const at::Tensor & input, const at::Tensor & hx, const at::Tensor & w_ih, const at::Tensor & w_hh, const at::Tensor & b_ih, const at::Tensor & b_hh, const at::Tensor & packed_ih, const at::Tensor & packed_hh, const at::Tensor & col_offsets_ih, const at::Tensor & col_offsets_hh, const at::Scalar & scale_ih, const at::Scalar & scale_hh, const at::Scalar & zero_point_ih, const at::Scalar & zero_point_hh);
TORCH_API at::Tensor quantized_rnn_relu_cell(const at::Tensor & input, const at::Tensor & hx, const at::Tensor & w_ih, const at::Tensor & w_hh, const at::Tensor & b_ih, const at::Tensor & b_hh, const at::Tensor & packed_ih, const at::Tensor & packed_hh, const at::Tensor & col_offsets_ih, const at::Tensor & col_offsets_hh, const at::Scalar & scale_ih, const at::Scalar & scale_hh, const at::Scalar & zero_point_ih, const at::Scalar & zero_point_hh);
TORCH_API at::Tensor quantized_rnn_tanh_cell(const at::Tensor & input, const at::Tensor & hx, const at::Tensor & w_ih, const at::Tensor & w_hh, const at::Tensor & b_ih, const at::Tensor & b_hh, const at::Tensor & packed_ih, const at::Tensor & packed_hh, const at::Tensor & col_offsets_ih, const at::Tensor & col_offsets_hh, const at::Scalar & scale_ih, const at::Scalar & scale_hh, const at::Scalar & zero_point_ih, const at::Scalar & zero_point_hh);
TORCH_API at::Tensor _pack_padded_sequence_backward(const at::Tensor & grad, at::IntArrayRef input_size, const at::Tensor & batch_sizes, bool batch_first);
TORCH_API ::std::tuple<at::Tensor,at::Tensor> _pad_packed_sequence(const at::Tensor & data, const at::Tensor & batch_sizes, bool batch_first, const at::Scalar & padding_value, int64_t total_length);
TORCH_API at::Tensor masked_fill(const at::Tensor & self, const at::Tensor & mask, const at::Scalar & value);
TORCH_API at::Tensor masked_fill(const at::Tensor & self, const at::Tensor & mask, const at::Tensor & value);
TORCH_API at::Tensor masked_scatter(const at::Tensor & self, const at::Tensor & mask, const at::Tensor & source);
TORCH_API at::Tensor put(const at::Tensor & self, const at::Tensor & index, const at::Tensor & source, bool accumulate=false);
TORCH_API at::Tensor & index_add_(at::Tensor & self, int64_t dim, const at::Tensor & index, const at::Tensor & source);
TORCH_API at::Tensor index_add(const at::Tensor & self, int64_t dim, const at::Tensor & index, const at::Tensor & source);
TORCH_API at::Tensor index_add(const at::Tensor & self, int64_t dim, const at::Tensor & index, const at::Tensor & source, const at::Scalar & alpha);
TORCH_API at::Tensor index_add(const at::Tensor & self, at::Dimname dim, const at::Tensor & index, const at::Tensor & source, const at::Scalar & alpha=1);
TORCH_API at::Tensor index_fill(const at::Tensor & self, int64_t dim, const at::Tensor & index, const at::Scalar & value);
TORCH_API at::Tensor index_fill(const at::Tensor & self, int64_t dim, const at::Tensor & index, const at::Tensor & value);
TORCH_API at::Tensor & index_fill_(at::Tensor & self, at::Dimname dim, const at::Tensor & index, const at::Scalar & value);
TORCH_API at::Tensor index_fill(const at::Tensor & self, at::Dimname dim, const at::Tensor & index, const at::Scalar & value);
TORCH_API at::Tensor & index_fill_(at::Tensor & self, at::Dimname dim, const at::Tensor & index, const at::Tensor & value);
TORCH_API at::Tensor index_fill(const at::Tensor & self, at::Dimname dim, const at::Tensor & index, const at::Tensor & value);
TORCH_API at::Tensor scatter(const at::Tensor & self, at::Dimname dim, const at::Tensor & index, const at::Tensor & src);
TORCH_API at::Tensor scatter(const at::Tensor & self, at::Dimname dim, const at::Tensor & index, const at::Scalar & value);
TORCH_API at::Tensor scatter_add(const at::Tensor & self, at::Dimname dim, const at::Tensor & index, const at::Tensor & src);
TORCH_API at::Tensor & bitwise_and_(at::Tensor & self, const at::Scalar & other);
TORCH_API at::Tensor __and__(const at::Tensor & self, const at::Scalar & other);
TORCH_API at::Tensor & __iand__(at::Tensor & self, const at::Scalar & other);
TORCH_API at::Tensor __and__(const at::Tensor & self, const at::Tensor & other);
TORCH_API at::Tensor & __iand__(at::Tensor & self, const at::Tensor & other);
TORCH_API at::Tensor bitwise_or(const at::Tensor & self, const at::Scalar & other);
TORCH_API at::Tensor & bitwise_or_(at::Tensor & self, const at::Scalar & other);
TORCH_API at::Tensor __or__(const at::Tensor & self, const at::Scalar & other);
TORCH_API at::Tensor & __ior__(at::Tensor & self, const at::Scalar & other);
TORCH_API at::Tensor __or__(const at::Tensor & self, const at::Tensor & other);
TORCH_API at::Tensor & __ior__(at::Tensor & self, const at::Tensor & other);
TORCH_API at::Tensor bitwise_xor(const at::Tensor & self, const at::Scalar & other);
TORCH_API at::Tensor & bitwise_xor_(at::Tensor & self, const at::Scalar & other);
TORCH_API at::Tensor __xor__(const at::Tensor & self, const at::Scalar & other);
TORCH_API at::Tensor & __ixor__(at::Tensor & self, const at::Scalar & other);
TORCH_API at::Tensor __xor__(const at::Tensor & self, const at::Tensor & other);
TORCH_API at::Tensor & __ixor__(at::Tensor & self, const at::Tensor & other);
TORCH_API at::Tensor diag_backward(const at::Tensor & grad, at::IntArrayRef input_sizes, int64_t diagonal);
TORCH_API at::Tensor trace_backward(const at::Tensor & grad, at::IntArrayRef sizes);
TORCH_API at::Tensor not_equal(const at::Tensor & self, const at::Scalar & other);
TORCH_API at::Tensor & not_equal_out(at::Tensor & out, const at::Tensor & self, const at::Scalar & other);
TORCH_API at::Tensor & not_equal_outf(const at::Tensor & self, const at::Scalar & other, at::Tensor & out);
TORCH_API at::Tensor & not_equal_(at::Tensor & self, const at::Scalar & other);
TORCH_API at::Tensor not_equal(const at::Tensor & self, const at::Tensor & other);
TORCH_API at::Tensor & not_equal_out(at::Tensor & out, const at::Tensor & self, const at::Tensor & other);
TORCH_API at::Tensor & not_equal_outf(const at::Tensor & self, const at::Tensor & other, at::Tensor & out);
TORCH_API at::Tensor & not_equal_(at::Tensor & self, const at::Tensor & other);
TORCH_API at::Tensor greater_equal(const at::Tensor & self, const at::Scalar & other);
TORCH_API at::Tensor & greater_equal_out(at::Tensor & out, const at::Tensor & self, const at::Scalar & other);
TORCH_API at::Tensor & greater_equal_outf(const at::Tensor & self, const at::Scalar & other, at::Tensor & out);
TORCH_API at::Tensor & greater_equal_(at::Tensor & self, const at::Scalar & other);
TORCH_API at::Tensor greater_equal(const at::Tensor & self, const at::Tensor & other);
TORCH_API at::Tensor & greater_equal_out(at::Tensor & out, const at::Tensor & self, const at::Tensor & other);
TORCH_API at::Tensor & greater_equal_outf(const at::Tensor & self, const at::Tensor & other, at::Tensor & out);
TORCH_API at::Tensor & greater_equal_(at::Tensor & self, const at::Tensor & other);
TORCH_API at::Tensor less_equal(const at::Tensor & self, const at::Scalar & other);
TORCH_API at::Tensor & less_equal_out(at::Tensor & out, const at::Tensor & self, const at::Scalar & other);
TORCH_API at::Tensor & less_equal_outf(const at::Tensor & self, const at::Scalar & other, at::Tensor & out);
TORCH_API at::Tensor & less_equal_(at::Tensor & self, const at::Scalar & other);
TORCH_API at::Tensor less_equal(const at::Tensor & self, const at::Tensor & other);
TORCH_API at::Tensor & less_equal_out(at::Tensor & out, const at::Tensor & self, const at::Tensor & other);
TORCH_API at::Tensor & less_equal_outf(const at::Tensor & self, const at::Tensor & other, at::Tensor & out);
TORCH_API at::Tensor & less_equal_(at::Tensor & self, const at::Tensor & other);
TORCH_API at::Tensor greater(const at::Tensor & self, const at::Scalar & other);
TORCH_API at::Tensor & greater_out(at::Tensor & out, const at::Tensor & self, const at::Scalar & other);
TORCH_API at::Tensor & greater_outf(const at::Tensor & self, const at::Scalar & other, at::Tensor & out);
TORCH_API at::Tensor & greater_(at::Tensor & self, const at::Scalar & other);
TORCH_API at::Tensor greater(const at::Tensor & self, const at::Tensor & other);
TORCH_API at::Tensor & greater_out(at::Tensor & out, const at::Tensor & self, const at::Tensor & other);
TORCH_API at::Tensor & greater_outf(const at::Tensor & self, const at::Tensor & other, at::Tensor & out);
TORCH_API at::Tensor & greater_(at::Tensor & self, const at::Tensor & other);
TORCH_API at::Tensor less(const at::Tensor & self, const at::Scalar & other);
TORCH_API at::Tensor & less_out(at::Tensor & out, const at::Tensor & self, const at::Scalar & other);
TORCH_API at::Tensor & less_outf(const at::Tensor & self, const at::Scalar & other, at::Tensor & out);
TORCH_API at::Tensor & less_(at::Tensor & self, const at::Scalar & other);
TORCH_API at::Tensor less(const at::Tensor & self, const at::Tensor & other);
TORCH_API at::Tensor & less_out(at::Tensor & out, const at::Tensor & self, const at::Tensor & other);
TORCH_API at::Tensor & less_outf(const at::Tensor & self, const at::Tensor & other, at::Tensor & out);
TORCH_API at::Tensor & less_(at::Tensor & self, const at::Tensor & other);
TORCH_API at::Tensor take_along_dim(const at::Tensor & self, const at::Tensor & indices, c10::optional<int64_t> dim=c10::nullopt);
TORCH_API at::Tensor & take_along_dim_out(at::Tensor & out, const at::Tensor & self, const at::Tensor & indices, c10::optional<int64_t> dim=c10::nullopt);
TORCH_API at::Tensor & take_along_dim_outf(const at::Tensor & self, const at::Tensor & indices, c10::optional<int64_t> dim, at::Tensor & out);
TORCH_API at::Tensor index_select(const at::Tensor & self, at::Dimname dim, const at::Tensor & index);
TORCH_API at::Tensor & index_select_out(at::Tensor & out, const at::Tensor & self, at::Dimname dim, const at::Tensor & index);
TORCH_API at::Tensor & index_select_outf(const at::Tensor & self, at::Dimname dim, const at::Tensor & index, at::Tensor & out);
TORCH_API at::Tensor index_select_backward(const at::Tensor & grad, at::IntArrayRef self_sizes, int64_t dim, const at::Tensor & index);
TORCH_API at::Tensor masked_select_backward(const at::Tensor & grad, const at::Tensor & input, const at::Tensor & mask);
TORCH_API ::std::vector<at::Tensor> nonzero_numpy(const at::Tensor & self);
TORCH_API at::Tensor gather_backward(const at::Tensor & grad, const at::Tensor & self, int64_t dim, const at::Tensor & index, bool sparse_grad);
TORCH_API at::Tensor gather(const at::Tensor & self, at::Dimname dim, const at::Tensor & index, bool sparse_grad=false);
TORCH_API at::Tensor & gather_out(at::Tensor & out, const at::Tensor & self, at::Dimname dim, const at::Tensor & index, bool sparse_grad=false);
TORCH_API at::Tensor & gather_outf(const at::Tensor & self, at::Dimname dim, const at::Tensor & index, bool sparse_grad, at::Tensor & out);
TORCH_API at::Tensor _gather_sparse_backward(const at::Tensor & self, int64_t dim, const at::Tensor & index, const at::Tensor & grad);
TORCH_API at::Tensor cross_entropy_loss(const at::Tensor & self, const at::Tensor & target, const c10::optional<at::Tensor> & weight={}, int64_t reduction=at::Reduction::Mean, int64_t ignore_index=-100, double label_smoothing=0.0);
TORCH_API ::std::tuple<at::Tensor,at::Tensor,at::Tensor> svd(const at::Tensor & self, bool some=true, bool compute_uv=true);
TORCH_API ::std::tuple<at::Tensor &,at::Tensor &,at::Tensor &> svd_out(at::Tensor & U, at::Tensor & S, at::Tensor & V, const at::Tensor & self, bool some=true, bool compute_uv=true);
TORCH_API ::std::tuple<at::Tensor &,at::Tensor &,at::Tensor &> svd_outf(const at::Tensor & self, bool some, bool compute_uv, at::Tensor & U, at::Tensor & S, at::Tensor & V);
TORCH_API at::Tensor swapaxes(const at::Tensor & self, int64_t axis0, int64_t axis1);
TORCH_API at::Tensor & swapaxes_(at::Tensor & self, int64_t axis0, int64_t axis1);
TORCH_API at::Tensor swapdims(const at::Tensor & self, int64_t dim0, int64_t dim1);
TORCH_API at::Tensor & swapdims_(at::Tensor & self, int64_t dim0, int64_t dim1);
TORCH_API ::std::tuple<at::Tensor,at::Tensor> qr(const at::Tensor & self, bool some=true);
TORCH_API ::std::tuple<at::Tensor &,at::Tensor &> qr_out(at::Tensor & Q, at::Tensor & R, const at::Tensor & self, bool some=true);
TORCH_API ::std::tuple<at::Tensor &,at::Tensor &> qr_outf(const at::Tensor & self, bool some, at::Tensor & Q, at::Tensor & R);
TORCH_API at::Tensor orgqr(const at::Tensor & self, const at::Tensor & input2);
TORCH_API at::Tensor & orgqr_out(at::Tensor & out, const at::Tensor & self, const at::Tensor & input2);
TORCH_API at::Tensor & orgqr_outf(const at::Tensor & self, const at::Tensor & input2, at::Tensor & out);
TORCH_API at::Tensor max(const at::Tensor & self, const at::Tensor & other);
TORCH_API at::Tensor & max_out(at::Tensor & out, const at::Tensor & self, const at::Tensor & other);
TORCH_API at::Tensor & max_outf(const at::Tensor & self, const at::Tensor & other, at::Tensor & out);
TORCH_API at::Tensor min(const at::Tensor & self, const at::Tensor & other);
TORCH_API at::Tensor & min_out(at::Tensor & out, const at::Tensor & self, const at::Tensor & other);
TORCH_API at::Tensor & min_outf(const at::Tensor & self, const at::Tensor & other, at::Tensor & out);
TORCH_API at::Tensor quantile(const at::Tensor & self, double q, c10::optional<int64_t> dim=c10::nullopt, bool keepdim=false);
TORCH_API at::Tensor & quantile_out(at::Tensor & out, const at::Tensor & self, double q, c10::optional<int64_t> dim=c10::nullopt, bool keepdim=false);
TORCH_API at::Tensor & quantile_outf(const at::Tensor & self, double q, c10::optional<int64_t> dim, bool keepdim, at::Tensor & out);
TORCH_API at::Tensor quantile(const at::Tensor & self, const at::Tensor & q, c10::optional<int64_t> dim=c10::nullopt, bool keepdim=false);
TORCH_API at::Tensor & quantile_out(at::Tensor & out, const at::Tensor & self, const at::Tensor & q, c10::optional<int64_t> dim=c10::nullopt, bool keepdim=false);
TORCH_API at::Tensor & quantile_outf(const at::Tensor & self, const at::Tensor & q, c10::optional<int64_t> dim, bool keepdim, at::Tensor & out);
TORCH_API at::Tensor nanquantile(const at::Tensor & self, double q, c10::optional<int64_t> dim=c10::nullopt, bool keepdim=false);
TORCH_API at::Tensor & nanquantile_out(at::Tensor & out, const at::Tensor & self, double q, c10::optional<int64_t> dim=c10::nullopt, bool keepdim=false);
TORCH_API at::Tensor & nanquantile_outf(const at::Tensor & self, double q, c10::optional<int64_t> dim, bool keepdim, at::Tensor & out);
TORCH_API at::Tensor nanquantile(const at::Tensor & self, const at::Tensor & q, c10::optional<int64_t> dim=c10::nullopt, bool keepdim=false);
TORCH_API at::Tensor & nanquantile_out(at::Tensor & out, const at::Tensor & self, const at::Tensor & q, c10::optional<int64_t> dim=c10::nullopt, bool keepdim=false);
TORCH_API at::Tensor & nanquantile_outf(const at::Tensor & self, const at::Tensor & q, c10::optional<int64_t> dim, bool keepdim, at::Tensor & out);
TORCH_API at::Tensor quantile(const at::Tensor & self, double q, c10::optional<int64_t> dim, bool keepdim, c10::string_view interpolation);
TORCH_API at::Tensor & quantile_out(at::Tensor & out, const at::Tensor & self, double q, c10::optional<int64_t> dim, bool keepdim, c10::string_view interpolation);
TORCH_API at::Tensor & quantile_outf(const at::Tensor & self, double q, c10::optional<int64_t> dim, bool keepdim, c10::string_view interpolation, at::Tensor & out);
TORCH_API at::Tensor quantile(const at::Tensor & self, const at::Tensor & q, c10::optional<int64_t> dim, bool keepdim, c10::string_view interpolation);
TORCH_API at::Tensor & quantile_out(at::Tensor & out, const at::Tensor & self, const at::Tensor & q, c10::optional<int64_t> dim, bool keepdim, c10::string_view interpolation);
TORCH_API at::Tensor & quantile_outf(const at::Tensor & self, const at::Tensor & q, c10::optional<int64_t> dim, bool keepdim, c10::string_view interpolation, at::Tensor & out);
TORCH_API at::Tensor nanquantile(const at::Tensor & self, double q, c10::optional<int64_t> dim, bool keepdim, c10::string_view interpolation);
TORCH_API at::Tensor & nanquantile_out(at::Tensor & out, const at::Tensor & self, double q, c10::optional<int64_t> dim, bool keepdim, c10::string_view interpolation);
TORCH_API at::Tensor & nanquantile_outf(const at::Tensor & self, double q, c10::optional<int64_t> dim, bool keepdim, c10::string_view interpolation, at::Tensor & out);
TORCH_API at::Tensor nanquantile(const at::Tensor & self, const at::Tensor & q, c10::optional<int64_t> dim, bool keepdim, c10::string_view interpolation);
TORCH_API at::Tensor & nanquantile_out(at::Tensor & out, const at::Tensor & self, const at::Tensor & q, c10::optional<int64_t> dim, bool keepdim, c10::string_view interpolation);
TORCH_API at::Tensor & nanquantile_outf(const at::Tensor & self, const at::Tensor & q, c10::optional<int64_t> dim, bool keepdim, c10::string_view interpolation, at::Tensor & out);
TORCH_API ::std::tuple<at::Tensor,at::Tensor> sort(const at::Tensor & self, at::Dimname dim, bool descending=false);
TORCH_API ::std::tuple<at::Tensor &,at::Tensor &> sort_out(at::Tensor & values, at::Tensor & indices, const at::Tensor & self, at::Dimname dim, bool descending=false);
TORCH_API ::std::tuple<at::Tensor &,at::Tensor &> sort_outf(const at::Tensor & self, at::Dimname dim, bool descending, at::Tensor & values, at::Tensor & indices);
TORCH_API ::std::tuple<at::Tensor,at::Tensor> sort(const at::Tensor & self, c10::optional<bool> stable, at::Dimname dim, bool descending=false);
TORCH_API ::std::tuple<at::Tensor &,at::Tensor &> sort_out(at::Tensor & values, at::Tensor & indices, const at::Tensor & self, c10::optional<bool> stable, at::Dimname dim, bool descending=false);
TORCH_API ::std::tuple<at::Tensor &,at::Tensor &> sort_outf(const at::Tensor & self, c10::optional<bool> stable, at::Dimname dim, bool descending, at::Tensor & values, at::Tensor & indices);
TORCH_API at::Tensor msort(const at::Tensor & self);
TORCH_API at::Tensor & msort_out(at::Tensor & out, const at::Tensor & self);
TORCH_API at::Tensor & msort_outf(const at::Tensor & self, at::Tensor & out);
TORCH_API at::Tensor argsort(const at::Tensor & self, int64_t dim=-1, bool descending=false);
TORCH_API at::Tensor argsort(const at::Tensor & self, at::Dimname dim, bool descending=false);
TORCH_API at::Tensor float_power(const at::Tensor & self, const at::Tensor & exponent);
TORCH_API at::Tensor & float_power_out(at::Tensor & out, const at::Tensor & self, const at::Tensor & exponent);
TORCH_API at::Tensor & float_power_outf(const at::Tensor & self, const at::Tensor & exponent, at::Tensor & out);
TORCH_API at::Tensor & float_power_(at::Tensor & self, const at::Tensor & exponent);
TORCH_API at::Tensor float_power(const at::Scalar & self, const at::Tensor & exponent);
TORCH_API at::Tensor & float_power_out(at::Tensor & out, const at::Scalar & self, const at::Tensor & exponent);
TORCH_API at::Tensor & float_power_outf(const at::Scalar & self, const at::Tensor & exponent, at::Tensor & out);
TORCH_API at::Tensor float_power(const at::Tensor & self, const at::Scalar & exponent);
TORCH_API at::Tensor & float_power_out(at::Tensor & out, const at::Tensor & self, const at::Scalar & exponent);
TORCH_API at::Tensor & float_power_outf(const at::Tensor & self, const at::Scalar & exponent, at::Tensor & out);
TORCH_API at::Tensor & float_power_(at::Tensor & self, const at::Scalar & exponent);
TORCH_API at::Tensor normal(double mean, double std, at::IntArrayRef size, c10::optional<at::Generator> generator=c10::nullopt, at::TensorOptions options={});
TORCH_API at::Tensor normal(double mean, double std, at::IntArrayRef size, c10::optional<at::Generator> generator, c10::optional<at::ScalarType> dtype, c10::optional<at::Layout> layout, c10::optional<at::Device> device, c10::optional<bool> pin_memory);
TORCH_API at::Tensor & normal_out(at::Tensor & out, double mean, double std, at::IntArrayRef size, c10::optional<at::Generator> generator=c10::nullopt);
TORCH_API at::Tensor & normal_outf(double mean, double std, at::IntArrayRef size, c10::optional<at::Generator> generator, at::Tensor & out);
TORCH_API at::Tensor multilabel_margin_loss(const at::Tensor & self, const at::Tensor & target, int64_t reduction=at::Reduction::Mean);
TORCH_API at::Tensor & multilabel_margin_loss_out(at::Tensor & out, const at::Tensor & self, const at::Tensor & target, int64_t reduction=at::Reduction::Mean);
TORCH_API at::Tensor & multilabel_margin_loss_outf(const at::Tensor & self, const at::Tensor & target, int64_t reduction, at::Tensor & out);
TORCH_API at::Tensor nll_loss(const at::Tensor & self, const at::Tensor & target, const c10::optional<at::Tensor> & weight={}, int64_t reduction=at::Reduction::Mean, int64_t ignore_index=-100);
TORCH_API at::Tensor & nll_loss_out(at::Tensor & out, const at::Tensor & self, const at::Tensor & target, const c10::optional<at::Tensor> & weight={}, int64_t reduction=at::Reduction::Mean, int64_t ignore_index=-100);
TORCH_API at::Tensor & nll_loss_outf(const at::Tensor & self, const at::Tensor & target, const c10::optional<at::Tensor> & weight, int64_t reduction, int64_t ignore_index, at::Tensor & out);
TORCH_API at::Tensor nll_loss_nd(const at::Tensor & self, const at::Tensor & target, const c10::optional<at::Tensor> & weight={}, int64_t reduction=at::Reduction::Mean, int64_t ignore_index=-100);
TORCH_API at::Tensor nll_loss2d(const at::Tensor & self, const at::Tensor & target, const c10::optional<at::Tensor> & weight={}, int64_t reduction=at::Reduction::Mean, int64_t ignore_index=-100);
TORCH_API at::Tensor & nll_loss2d_out(at::Tensor & out, const at::Tensor & self, const at::Tensor & target, const c10::optional<at::Tensor> & weight={}, int64_t reduction=at::Reduction::Mean, int64_t ignore_index=-100);
TORCH_API at::Tensor & nll_loss2d_outf(const at::Tensor & self, const at::Tensor & target, const c10::optional<at::Tensor> & weight, int64_t reduction, int64_t ignore_index, at::Tensor & out);
TORCH_API at::Tensor log_sigmoid(const at::Tensor & self);
TORCH_API at::Tensor & log_sigmoid_out(at::Tensor & out, const at::Tensor & self);
TORCH_API at::Tensor & log_sigmoid_outf(const at::Tensor & self, at::Tensor & out);
TORCH_API at::Tensor adaptive_avg_pool2d(const at::Tensor & self, at::IntArrayRef output_size);
TORCH_API at::Tensor adaptive_avg_pool3d(const at::Tensor & self, at::IntArrayRef output_size);
TORCH_API at::Tensor thnn_conv2d(const at::Tensor & self, const at::Tensor & weight, at::IntArrayRef kernel_size, const c10::optional<at::Tensor> & bias={}, at::IntArrayRef stride=1, at::IntArrayRef padding=0);
TORCH_API at::Tensor & thnn_conv2d_out(at::Tensor & out, const at::Tensor & self, const at::Tensor & weight, at::IntArrayRef kernel_size, const c10::optional<at::Tensor> & bias={}, at::IntArrayRef stride=1, at::IntArrayRef padding=0);
TORCH_API at::Tensor & thnn_conv2d_outf(const at::Tensor & self, const at::Tensor & weight, at::IntArrayRef kernel_size, const c10::optional<at::Tensor> & bias, at::IntArrayRef stride, at::IntArrayRef padding, at::Tensor & out);
TORCH_API at::Tensor slow_conv3d(const at::Tensor & self, const at::Tensor & weight, at::IntArrayRef kernel_size, const c10::optional<at::Tensor> & bias={}, at::IntArrayRef stride=1, at::IntArrayRef padding=0);
TORCH_API at::Tensor & slow_conv3d_out(at::Tensor & out, const at::Tensor & self, const at::Tensor & weight, at::IntArrayRef kernel_size, const c10::optional<at::Tensor> & bias={}, at::IntArrayRef stride=1, at::IntArrayRef padding=0);
TORCH_API at::Tensor & slow_conv3d_outf(const at::Tensor & self, const at::Tensor & weight, at::IntArrayRef kernel_size, const c10::optional<at::Tensor> & bias, at::IntArrayRef stride, at::IntArrayRef padding, at::Tensor & out);
TORCH_API at::Tensor column_stack(at::TensorList tensors);
TORCH_API at::Tensor & column_stack_out(at::Tensor & out, at::TensorList tensors);
TORCH_API at::Tensor & column_stack_outf(at::TensorList tensors, at::Tensor & out);
TORCH_API at::Tensor isfinite(const at::Tensor & self);
TORCH_API at::Tensor isinf(const at::Tensor & self);
TORCH_API at::Tensor _add_batch_dim(const at::Tensor & self, int64_t batch_dim, int64_t level);
TORCH_API at::Tensor _remove_batch_dim(const at::Tensor & self, int64_t level, int64_t batch_size, int64_t out_dim);
TORCH_API at::Tensor special_expm1(const at::Tensor & self);
TORCH_API at::Tensor & special_expm1_out(at::Tensor & out, const at::Tensor & self);
TORCH_API at::Tensor & special_expm1_outf(const at::Tensor & self, at::Tensor & out);
TORCH_API at::Tensor special_exp2(const at::Tensor & self);
TORCH_API at::Tensor & special_exp2_out(at::Tensor & out, const at::Tensor & self);
TORCH_API at::Tensor & special_exp2_outf(const at::Tensor & self, at::Tensor & out);
TORCH_API at::Tensor special_psi(const at::Tensor & self);
TORCH_API at::Tensor & special_psi_out(at::Tensor & out, const at::Tensor & self);
TORCH_API at::Tensor & special_psi_outf(const at::Tensor & self, at::Tensor & out);
TORCH_API at::Tensor special_digamma(const at::Tensor & self);
TORCH_API at::Tensor & special_digamma_out(at::Tensor & out, const at::Tensor & self);
TORCH_API at::Tensor & special_digamma_outf(const at::Tensor & self, at::Tensor & out);
TORCH_API at::Tensor special_gammaln(const at::Tensor & self);
TORCH_API at::Tensor & special_gammaln_out(at::Tensor & out, const at::Tensor & self);
TORCH_API at::Tensor & special_gammaln_outf(const at::Tensor & self, at::Tensor & out);
TORCH_API at::Tensor special_erf(const at::Tensor & self);
TORCH_API at::Tensor & special_erf_out(at::Tensor & out, const at::Tensor & self);
TORCH_API at::Tensor & special_erf_outf(const at::Tensor & self, at::Tensor & out);
TORCH_API at::Tensor special_erfc(const at::Tensor & self);
TORCH_API at::Tensor & special_erfc_out(at::Tensor & out, const at::Tensor & self);
TORCH_API at::Tensor & special_erfc_outf(const at::Tensor & self, at::Tensor & out);
TORCH_API at::Tensor special_erfinv(const at::Tensor & self);
TORCH_API at::Tensor & special_erfinv_out(at::Tensor & out, const at::Tensor & self);
TORCH_API at::Tensor & special_erfinv_outf(const at::Tensor & self, at::Tensor & out);
TORCH_API at::Tensor special_ndtr(const at::Tensor & self);
TORCH_API at::Tensor & special_ndtr_out(at::Tensor & out, const at::Tensor & self);
TORCH_API at::Tensor & special_ndtr_outf(const at::Tensor & self, at::Tensor & out);
TORCH_API at::Tensor special_xlogy(const at::Tensor & self, const at::Tensor & other);
TORCH_API at::Tensor & special_xlogy_out(at::Tensor & out, const at::Tensor & self, const at::Tensor & other);
TORCH_API at::Tensor & special_xlogy_outf(const at::Tensor & self, const at::Tensor & other, at::Tensor & out);
TORCH_API at::Tensor special_xlogy(const at::Scalar & self, const at::Tensor & other);
TORCH_API at::Tensor & special_xlogy_out(at::Tensor & out, const at::Scalar & self, const at::Tensor & other);
TORCH_API at::Tensor & special_xlogy_outf(const at::Scalar & self, const at::Tensor & other, at::Tensor & out);
TORCH_API at::Tensor special_xlogy(const at::Tensor & self, const at::Scalar & other);
TORCH_API at::Tensor & special_xlogy_out(at::Tensor & out, const at::Tensor & self, const at::Scalar & other);
TORCH_API at::Tensor & special_xlogy_outf(const at::Tensor & self, const at::Scalar & other, at::Tensor & out);
TORCH_API at::Tensor special_i0(const at::Tensor & self);
TORCH_API at::Tensor & special_i0_out(at::Tensor & out, const at::Tensor & self);
TORCH_API at::Tensor & special_i0_outf(const at::Tensor & self, at::Tensor & out);
TORCH_API at::Tensor special_logit(const at::Tensor & self, c10::optional<double> eps=c10::nullopt);
TORCH_API at::Tensor & special_logit_out(at::Tensor & out, const at::Tensor & self, c10::optional<double> eps=c10::nullopt);
TORCH_API at::Tensor & special_logit_outf(const at::Tensor & self, c10::optional<double> eps, at::Tensor & out);
TORCH_API at::Tensor special_polygamma(int64_t n, const at::Tensor & self);
TORCH_API at::Tensor & special_polygamma_out(at::Tensor & out, int64_t n, const at::Tensor & self);
TORCH_API at::Tensor & special_polygamma_outf(int64_t n, const at::Tensor & self, at::Tensor & out);
TORCH_API at::Tensor special_logsumexp(const at::Tensor & self, at::IntArrayRef dim, bool keepdim=false);
TORCH_API at::Tensor & special_logsumexp_out(at::Tensor & out, const at::Tensor & self, at::IntArrayRef dim, bool keepdim=false);
TORCH_API at::Tensor & special_logsumexp_outf(const at::Tensor & self, at::IntArrayRef dim, bool keepdim, at::Tensor & out);
TORCH_API at::Tensor special_expit(const at::Tensor & self);
TORCH_API at::Tensor & special_expit_out(at::Tensor & out, const at::Tensor & self);
TORCH_API at::Tensor & special_expit_outf(const at::Tensor & self, at::Tensor & out);
TORCH_API at::Tensor special_sinc(const at::Tensor & self);
TORCH_API at::Tensor & special_sinc_out(at::Tensor & out, const at::Tensor & self);
TORCH_API at::Tensor & special_sinc_outf(const at::Tensor & self, at::Tensor & out);
TORCH_API at::Tensor special_round(const at::Tensor & self);
TORCH_API at::Tensor & special_round_out(at::Tensor & out, const at::Tensor & self);
TORCH_API at::Tensor & special_round_outf(const at::Tensor & self, at::Tensor & out);
TORCH_API at::Tensor special_log1p(const at::Tensor & self);
TORCH_API at::Tensor & special_log1p_out(at::Tensor & out, const at::Tensor & self);
TORCH_API at::Tensor & special_log1p_outf(const at::Tensor & self, at::Tensor & out);
TORCH_API at::Tensor special_log_softmax(const at::Tensor & self, int64_t dim, c10::optional<at::ScalarType> dtype=c10::nullopt);
TORCH_API at::Tensor special_gammainc(const at::Tensor & self, const at::Tensor & other);
TORCH_API at::Tensor & special_gammainc_out(at::Tensor & out, const at::Tensor & self, const at::Tensor & other);
TORCH_API at::Tensor & special_gammainc_outf(const at::Tensor & self, const at::Tensor & other, at::Tensor & out);
TORCH_API at::Tensor special_gammaincc(const at::Tensor & self, const at::Tensor & other);
TORCH_API at::Tensor & special_gammaincc_out(at::Tensor & out, const at::Tensor & self, const at::Tensor & other);
TORCH_API at::Tensor & special_gammaincc_outf(const at::Tensor & self, const at::Tensor & other, at::Tensor & out);
TORCH_API at::Tensor special_multigammaln(const at::Tensor & self, int64_t p);
TORCH_API at::Tensor & special_multigammaln_out(at::Tensor & out, const at::Tensor & self, int64_t p);
TORCH_API at::Tensor & special_multigammaln_outf(const at::Tensor & self, int64_t p, at::Tensor & out);
TORCH_API at::Tensor fft_fft(const at::Tensor & self, c10::optional<int64_t> n=c10::nullopt, int64_t dim=-1, c10::optional<c10::string_view> norm=c10::nullopt);
TORCH_API at::Tensor & fft_fft_out(at::Tensor & out, const at::Tensor & self, c10::optional<int64_t> n=c10::nullopt, int64_t dim=-1, c10::optional<c10::string_view> norm=c10::nullopt);
TORCH_API at::Tensor & fft_fft_outf(const at::Tensor & self, c10::optional<int64_t> n, int64_t dim, c10::optional<c10::string_view> norm, at::Tensor & out);
TORCH_API at::Tensor fft_ifft(const at::Tensor & self, c10::optional<int64_t> n=c10::nullopt, int64_t dim=-1, c10::optional<c10::string_view> norm=c10::nullopt);
TORCH_API at::Tensor & fft_ifft_out(at::Tensor & out, const at::Tensor & self, c10::optional<int64_t> n=c10::nullopt, int64_t dim=-1, c10::optional<c10::string_view> norm=c10::nullopt);
TORCH_API at::Tensor & fft_ifft_outf(const at::Tensor & self, c10::optional<int64_t> n, int64_t dim, c10::optional<c10::string_view> norm, at::Tensor & out);
TORCH_API at::Tensor fft_rfft(const at::Tensor & self, c10::optional<int64_t> n=c10::nullopt, int64_t dim=-1, c10::optional<c10::string_view> norm=c10::nullopt);
TORCH_API at::Tensor & fft_rfft_out(at::Tensor & out, const at::Tensor & self, c10::optional<int64_t> n=c10::nullopt, int64_t dim=-1, c10::optional<c10::string_view> norm=c10::nullopt);
TORCH_API at::Tensor & fft_rfft_outf(const at::Tensor & self, c10::optional<int64_t> n, int64_t dim, c10::optional<c10::string_view> norm, at::Tensor & out);
TORCH_API at::Tensor fft_irfft(const at::Tensor & self, c10::optional<int64_t> n=c10::nullopt, int64_t dim=-1, c10::optional<c10::string_view> norm=c10::nullopt);
TORCH_API at::Tensor & fft_irfft_out(at::Tensor & out, const at::Tensor & self, c10::optional<int64_t> n=c10::nullopt, int64_t dim=-1, c10::optional<c10::string_view> norm=c10::nullopt);
TORCH_API at::Tensor & fft_irfft_outf(const at::Tensor & self, c10::optional<int64_t> n, int64_t dim, c10::optional<c10::string_view> norm, at::Tensor & out);
TORCH_API at::Tensor fft_hfft(const at::Tensor & self, c10::optional<int64_t> n=c10::nullopt, int64_t dim=-1, c10::optional<c10::string_view> norm=c10::nullopt);
TORCH_API at::Tensor & fft_hfft_out(at::Tensor & out, const at::Tensor & self, c10::optional<int64_t> n=c10::nullopt, int64_t dim=-1, c10::optional<c10::string_view> norm=c10::nullopt);
TORCH_API at::Tensor & fft_hfft_outf(const at::Tensor & self, c10::optional<int64_t> n, int64_t dim, c10::optional<c10::string_view> norm, at::Tensor & out);
TORCH_API at::Tensor fft_ihfft(const at::Tensor & self, c10::optional<int64_t> n=c10::nullopt, int64_t dim=-1, c10::optional<c10::string_view> norm=c10::nullopt);
TORCH_API at::Tensor & fft_ihfft_out(at::Tensor & out, const at::Tensor & self, c10::optional<int64_t> n=c10::nullopt, int64_t dim=-1, c10::optional<c10::string_view> norm=c10::nullopt);
TORCH_API at::Tensor & fft_ihfft_outf(const at::Tensor & self, c10::optional<int64_t> n, int64_t dim, c10::optional<c10::string_view> norm, at::Tensor & out);
TORCH_API at::Tensor fft_fft2(const at::Tensor & self, c10::optional<at::IntArrayRef> s=c10::nullopt, at::IntArrayRef dim={-2,-1}, c10::optional<c10::string_view> norm=c10::nullopt);
TORCH_API at::Tensor & fft_fft2_out(at::Tensor & out, const at::Tensor & self, c10::optional<at::IntArrayRef> s=c10::nullopt, at::IntArrayRef dim={-2,-1}, c10::optional<c10::string_view> norm=c10::nullopt);
TORCH_API at::Tensor & fft_fft2_outf(const at::Tensor & self, c10::optional<at::IntArrayRef> s, at::IntArrayRef dim, c10::optional<c10::string_view> norm, at::Tensor & out);
TORCH_API at::Tensor fft_ifft2(const at::Tensor & self, c10::optional<at::IntArrayRef> s=c10::nullopt, at::IntArrayRef dim={-2,-1}, c10::optional<c10::string_view> norm=c10::nullopt);
TORCH_API at::Tensor & fft_ifft2_out(at::Tensor & out, const at::Tensor & self, c10::optional<at::IntArrayRef> s=c10::nullopt, at::IntArrayRef dim={-2,-1}, c10::optional<c10::string_view> norm=c10::nullopt);
TORCH_API at::Tensor & fft_ifft2_outf(const at::Tensor & self, c10::optional<at::IntArrayRef> s, at::IntArrayRef dim, c10::optional<c10::string_view> norm, at::Tensor & out);
TORCH_API at::Tensor fft_rfft2(const at::Tensor & self, c10::optional<at::IntArrayRef> s=c10::nullopt, at::IntArrayRef dim={-2,-1}, c10::optional<c10::string_view> norm=c10::nullopt);
TORCH_API at::Tensor & fft_rfft2_out(at::Tensor & out, const at::Tensor & self, c10::optional<at::IntArrayRef> s=c10::nullopt, at::IntArrayRef dim={-2,-1}, c10::optional<c10::string_view> norm=c10::nullopt);
TORCH_API at::Tensor & fft_rfft2_outf(const at::Tensor & self, c10::optional<at::IntArrayRef> s, at::IntArrayRef dim, c10::optional<c10::string_view> norm, at::Tensor & out);
TORCH_API at::Tensor fft_irfft2(const at::Tensor & self, c10::optional<at::IntArrayRef> s=c10::nullopt, at::IntArrayRef dim={-2,-1}, c10::optional<c10::string_view> norm=c10::nullopt);
TORCH_API at::Tensor & fft_irfft2_out(at::Tensor & out, const at::Tensor & self, c10::optional<at::IntArrayRef> s=c10::nullopt, at::IntArrayRef dim={-2,-1}, c10::optional<c10::string_view> norm=c10::nullopt);
TORCH_API at::Tensor & fft_irfft2_outf(const at::Tensor & self, c10::optional<at::IntArrayRef> s, at::IntArrayRef dim, c10::optional<c10::string_view> norm, at::Tensor & out);
TORCH_API at::Tensor fft_fftn(const at::Tensor & self, c10::optional<at::IntArrayRef> s=c10::nullopt, c10::optional<at::IntArrayRef> dim=c10::nullopt, c10::optional<c10::string_view> norm=c10::nullopt);
TORCH_API at::Tensor & fft_fftn_out(at::Tensor & out, const at::Tensor & self, c10::optional<at::IntArrayRef> s=c10::nullopt, c10::optional<at::IntArrayRef> dim=c10::nullopt, c10::optional<c10::string_view> norm=c10::nullopt);
TORCH_API at::Tensor & fft_fftn_outf(const at::Tensor & self, c10::optional<at::IntArrayRef> s, c10::optional<at::IntArrayRef> dim, c10::optional<c10::string_view> norm, at::Tensor & out);
TORCH_API at::Tensor fft_ifftn(const at::Tensor & self, c10::optional<at::IntArrayRef> s=c10::nullopt, c10::optional<at::IntArrayRef> dim=c10::nullopt, c10::optional<c10::string_view> norm=c10::nullopt);
TORCH_API at::Tensor & fft_ifftn_out(at::Tensor & out, const at::Tensor & self, c10::optional<at::IntArrayRef> s=c10::nullopt, c10::optional<at::IntArrayRef> dim=c10::nullopt, c10::optional<c10::string_view> norm=c10::nullopt);
TORCH_API at::Tensor & fft_ifftn_outf(const at::Tensor & self, c10::optional<at::IntArrayRef> s, c10::optional<at::IntArrayRef> dim, c10::optional<c10::string_view> norm, at::Tensor & out);
TORCH_API at::Tensor fft_rfftn(const at::Tensor & self, c10::optional<at::IntArrayRef> s=c10::nullopt, c10::optional<at::IntArrayRef> dim=c10::nullopt, c10::optional<c10::string_view> norm=c10::nullopt);
TORCH_API at::Tensor & fft_rfftn_out(at::Tensor & out, const at::Tensor & self, c10::optional<at::IntArrayRef> s=c10::nullopt, c10::optional<at::IntArrayRef> dim=c10::nullopt, c10::optional<c10::string_view> norm=c10::nullopt);
TORCH_API at::Tensor & fft_rfftn_outf(const at::Tensor & self, c10::optional<at::IntArrayRef> s, c10::optional<at::IntArrayRef> dim, c10::optional<c10::string_view> norm, at::Tensor & out);
TORCH_API at::Tensor fft_irfftn(const at::Tensor & self, c10::optional<at::IntArrayRef> s=c10::nullopt, c10::optional<at::IntArrayRef> dim=c10::nullopt, c10::optional<c10::string_view> norm=c10::nullopt);
TORCH_API at::Tensor & fft_irfftn_out(at::Tensor & out, const at::Tensor & self, c10::optional<at::IntArrayRef> s=c10::nullopt, c10::optional<at::IntArrayRef> dim=c10::nullopt, c10::optional<c10::string_view> norm=c10::nullopt);
TORCH_API at::Tensor & fft_irfftn_outf(const at::Tensor & self, c10::optional<at::IntArrayRef> s, c10::optional<at::IntArrayRef> dim, c10::optional<c10::string_view> norm, at::Tensor & out);
TORCH_API at::Tensor fft_fftfreq(int64_t n, double d=1.0, at::TensorOptions options={});
TORCH_API at::Tensor fft_fftfreq(int64_t n, double d, c10::optional<at::ScalarType> dtype, c10::optional<at::Layout> layout, c10::optional<at::Device> device, c10::optional<bool> pin_memory);
TORCH_API at::Tensor & fft_fftfreq_out(at::Tensor & out, int64_t n, double d=1.0);
TORCH_API at::Tensor & fft_fftfreq_outf(int64_t n, double d, at::Tensor & out);
TORCH_API at::Tensor fft_rfftfreq(int64_t n, double d=1.0, at::TensorOptions options={});
TORCH_API at::Tensor fft_rfftfreq(int64_t n, double d, c10::optional<at::ScalarType> dtype, c10::optional<at::Layout> layout, c10::optional<at::Device> device, c10::optional<bool> pin_memory);
TORCH_API at::Tensor & fft_rfftfreq_out(at::Tensor & out, int64_t n, double d=1.0);
TORCH_API at::Tensor & fft_rfftfreq_outf(int64_t n, double d, at::Tensor & out);
TORCH_API at::Tensor fft_fftshift(const at::Tensor & self, c10::optional<at::IntArrayRef> dim=c10::nullopt);
TORCH_API at::Tensor fft_ifftshift(const at::Tensor & self, c10::optional<at::IntArrayRef> dim=c10::nullopt);
TORCH_API at::Tensor linalg_cholesky(const at::Tensor & self, bool upper=false);
TORCH_API at::Tensor & linalg_cholesky_out(at::Tensor & out, const at::Tensor & self, bool upper=false);
TORCH_API at::Tensor & linalg_cholesky_outf(const at::Tensor & self, bool upper, at::Tensor & out);
TORCH_API at::Tensor linalg_det(const at::Tensor & self);
TORCH_API at::Tensor & linalg_det_out(at::Tensor & out, const at::Tensor & self);
TORCH_API at::Tensor & linalg_det_outf(const at::Tensor & self, at::Tensor & out);
TORCH_API at::Tensor det(const at::Tensor & self);
TORCH_API at::Tensor linalg_matmul(const at::Tensor & self, const at::Tensor & other);
TORCH_API at::Tensor & linalg_matmul_out(at::Tensor & out, const at::Tensor & self, const at::Tensor & other);
TORCH_API at::Tensor & linalg_matmul_outf(const at::Tensor & self, const at::Tensor & other, at::Tensor & out);
TORCH_API at::Tensor linalg_eigvals(const at::Tensor & self);
TORCH_API at::Tensor & linalg_eigvals_out(at::Tensor & out, const at::Tensor & self);
TORCH_API at::Tensor & linalg_eigvals_outf(const at::Tensor & self, at::Tensor & out);
TORCH_API at::Tensor linalg_eigvalsh(const at::Tensor & self, c10::string_view UPLO="L");
TORCH_API at::Tensor linalg_inv(const at::Tensor & self);
TORCH_API at::Tensor & linalg_inv_out(at::Tensor & out, const at::Tensor & self);
TORCH_API at::Tensor & linalg_inv_outf(const at::Tensor & self, at::Tensor & out);
TORCH_API at::Tensor inner(const at::Tensor & self, const at::Tensor & other);
TORCH_API at::Tensor & inner_out(at::Tensor & out, const at::Tensor & self, const at::Tensor & other);
TORCH_API at::Tensor & inner_outf(const at::Tensor & self, const at::Tensor & other, at::Tensor & out);
TORCH_API at::Tensor outer(const at::Tensor & self, const at::Tensor & vec2);
TORCH_API at::Tensor & outer_out(at::Tensor & out, const at::Tensor & self, const at::Tensor & vec2);
TORCH_API at::Tensor & outer_outf(const at::Tensor & self, const at::Tensor & vec2, at::Tensor & out);
TORCH_API at::Tensor ger(const at::Tensor & self, const at::Tensor & vec2);
TORCH_API at::Tensor & ger_out(at::Tensor & out, const at::Tensor & self, const at::Tensor & vec2);
TORCH_API at::Tensor & ger_outf(const at::Tensor & self, const at::Tensor & vec2, at::Tensor & out);
TORCH_API at::Tensor linalg_norm(const at::Tensor & self, const c10::optional<at::Scalar> & ord=c10::nullopt, c10::optional<at::IntArrayRef> dim=c10::nullopt, bool keepdim=false, c10::optional<at::ScalarType> dtype=c10::nullopt);
TORCH_API at::Tensor & linalg_norm_out(at::Tensor & out, const at::Tensor & self, const c10::optional<at::Scalar> & ord=c10::nullopt, c10::optional<at::IntArrayRef> dim=c10::nullopt, bool keepdim=false, c10::optional<at::ScalarType> dtype=c10::nullopt);
TORCH_API at::Tensor & linalg_norm_outf(const at::Tensor & self, const c10::optional<at::Scalar> & ord, c10::optional<at::IntArrayRef> dim, bool keepdim, c10::optional<at::ScalarType> dtype, at::Tensor & out);
TORCH_API at::Tensor linalg_norm(const at::Tensor & self, c10::string_view ord, c10::optional<at::IntArrayRef> dim=c10::nullopt, bool keepdim=false, c10::optional<at::ScalarType> dtype=c10::nullopt);
TORCH_API at::Tensor & linalg_norm_out(at::Tensor & out, const at::Tensor & self, c10::string_view ord, c10::optional<at::IntArrayRef> dim=c10::nullopt, bool keepdim=false, c10::optional<at::ScalarType> dtype=c10::nullopt);
TORCH_API at::Tensor & linalg_norm_outf(const at::Tensor & self, c10::string_view ord, c10::optional<at::IntArrayRef> dim, bool keepdim, c10::optional<at::ScalarType> dtype, at::Tensor & out);
TORCH_API at::Tensor linalg_matrix_norm(const at::Tensor & self, const at::Scalar & ord, at::IntArrayRef dim={-2,-1}, bool keepdim=false, c10::optional<at::ScalarType> dtype=c10::nullopt);
TORCH_API at::Tensor & linalg_matrix_norm_out(at::Tensor & out, const at::Tensor & self, const at::Scalar & ord, at::IntArrayRef dim={-2,-1}, bool keepdim=false, c10::optional<at::ScalarType> dtype=c10::nullopt);
TORCH_API at::Tensor & linalg_matrix_norm_outf(const at::Tensor & self, const at::Scalar & ord, at::IntArrayRef dim, bool keepdim, c10::optional<at::ScalarType> dtype, at::Tensor & out);
TORCH_API at::Tensor linalg_matrix_norm(const at::Tensor & self, c10::string_view ord="fro", at::IntArrayRef dim={-2,-1}, bool keepdim=false, c10::optional<at::ScalarType> dtype=c10::nullopt);
TORCH_API at::Tensor & linalg_matrix_norm_out(at::Tensor & out, const at::Tensor & self, c10::string_view ord="fro", at::IntArrayRef dim={-2,-1}, bool keepdim=false, c10::optional<at::ScalarType> dtype=c10::nullopt);
TORCH_API at::Tensor & linalg_matrix_norm_outf(const at::Tensor & self, c10::string_view ord, at::IntArrayRef dim, bool keepdim, c10::optional<at::ScalarType> dtype, at::Tensor & out);
TORCH_API ::std::tuple<at::Tensor,at::Tensor,at::Tensor> linalg_svd(const at::Tensor & self, bool full_matrices=true);
TORCH_API ::std::tuple<at::Tensor &,at::Tensor &,at::Tensor &> linalg_svd_out(at::Tensor & U, at::Tensor & S, at::Tensor & Vh, const at::Tensor & self, bool full_matrices=true);
TORCH_API ::std::tuple<at::Tensor &,at::Tensor &,at::Tensor &> linalg_svd_outf(const at::Tensor & self, bool full_matrices, at::Tensor & U, at::Tensor & S, at::Tensor & Vh);
TORCH_API at::Tensor linalg_svdvals(const at::Tensor & input);
TORCH_API at::Tensor & linalg_svdvals_out(at::Tensor & out, const at::Tensor & input);
TORCH_API at::Tensor & linalg_svdvals_outf(const at::Tensor & input, at::Tensor & out);
TORCH_API at::Tensor linalg_cond(const at::Tensor & self, const c10::optional<at::Scalar> & p=c10::nullopt);
TORCH_API at::Tensor & linalg_cond_out(at::Tensor & out, const at::Tensor & self, const c10::optional<at::Scalar> & p=c10::nullopt);
TORCH_API at::Tensor & linalg_cond_outf(const at::Tensor & self, const c10::optional<at::Scalar> & p, at::Tensor & out);
TORCH_API at::Tensor linalg_cond(const at::Tensor & self, c10::string_view p);
TORCH_API at::Tensor & linalg_cond_out(at::Tensor & out, const at::Tensor & self, c10::string_view p);
TORCH_API at::Tensor & linalg_cond_outf(const at::Tensor & self, c10::string_view p, at::Tensor & out);
TORCH_API at::Tensor linalg_pinv(const at::Tensor & self, double rcond=1e-15, bool hermitian=false);
TORCH_API at::Tensor & linalg_pinv_out(at::Tensor & out, const at::Tensor & self, double rcond=1e-15, bool hermitian=false);
TORCH_API at::Tensor & linalg_pinv_outf(const at::Tensor & self, double rcond, bool hermitian, at::Tensor & out);
TORCH_API at::Tensor linalg_pinv(const at::Tensor & self, const at::Tensor & rcond, bool hermitian=false);
TORCH_API at::Tensor & linalg_pinv_out(at::Tensor & out, const at::Tensor & self, const at::Tensor & rcond, bool hermitian=false);
TORCH_API at::Tensor & linalg_pinv_outf(const at::Tensor & self, const at::Tensor & rcond, bool hermitian, at::Tensor & out);
TORCH_API at::Tensor linalg_tensorinv(const at::Tensor & self, int64_t ind=2);
TORCH_API at::Tensor & linalg_tensorinv_out(at::Tensor & out, const at::Tensor & self, int64_t ind=2);
TORCH_API at::Tensor & linalg_tensorinv_outf(const at::Tensor & self, int64_t ind, at::Tensor & out);
TORCH_API at::Tensor linalg_tensorsolve(const at::Tensor & self, const at::Tensor & other, c10::optional<at::IntArrayRef> dims=c10::nullopt);
TORCH_API at::Tensor & linalg_tensorsolve_out(at::Tensor & out, const at::Tensor & self, const at::Tensor & other, c10::optional<at::IntArrayRef> dims=c10::nullopt);
TORCH_API at::Tensor & linalg_tensorsolve_outf(const at::Tensor & self, const at::Tensor & other, c10::optional<at::IntArrayRef> dims, at::Tensor & out);
TORCH_API at::Tensor linalg_matrix_power(const at::Tensor & self, int64_t n);
TORCH_API at::Tensor & linalg_matrix_power_out(at::Tensor & out, const at::Tensor & self, int64_t n);
TORCH_API at::Tensor & linalg_matrix_power_outf(const at::Tensor & self, int64_t n, at::Tensor & out);
TORCH_API at::Tensor linalg_matrix_rank(const at::Tensor & self, c10::optional<double> tol=c10::nullopt, bool hermitian=false);
TORCH_API at::Tensor & linalg_matrix_rank_out(at::Tensor & out, const at::Tensor & self, c10::optional<double> tol=c10::nullopt, bool hermitian=false);
TORCH_API at::Tensor & linalg_matrix_rank_outf(const at::Tensor & self, c10::optional<double> tol, bool hermitian, at::Tensor & out);
TORCH_API at::Tensor linalg_matrix_rank(const at::Tensor & input, const at::Tensor & tol, bool hermitian=false);
TORCH_API at::Tensor & linalg_matrix_rank_out(at::Tensor & out, const at::Tensor & input, const at::Tensor & tol, bool hermitian=false);
TORCH_API at::Tensor & linalg_matrix_rank_outf(const at::Tensor & input, const at::Tensor & tol, bool hermitian, at::Tensor & out);
TORCH_API at::Tensor linalg_multi_dot(at::TensorList tensors);
TORCH_API at::Tensor & linalg_multi_dot_out(at::Tensor & out, at::TensorList tensors);
TORCH_API at::Tensor & linalg_multi_dot_outf(at::TensorList tensors, at::Tensor & out);
TORCH_API at::Tensor _test_serialization_subcmul(const at::Tensor & self, const at::Tensor & other, const at::Scalar & alpha=1);
TORCH_API at::Tensor _test_string_default(const at::Tensor & dummy, c10::string_view a="\"'\\", c10::string_view b="\"'\\");
TORCH_API at::Tensor _test_ambiguous_defaults(const at::Tensor & dummy, int64_t a=1, int64_t b=1);
TORCH_API at::Tensor _test_ambiguous_defaults(const at::Tensor & dummy, int64_t a, c10::string_view b);
TORCH_API at::Tensor pad_sequence(at::TensorList sequences, bool batch_first=false, double padding_value=0.0);
TORCH_API at::Tensor flatten_dense_tensors(at::TensorList tensors);
TORCH_API ::std::vector<at::Tensor> unflatten_dense_tensors(const at::Tensor & flat, at::TensorList tensors);

} // namespace compositeimplicitautograd
} // namespace at
