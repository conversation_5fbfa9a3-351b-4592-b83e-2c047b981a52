// @generated by tools/codegen/gen.py from DispatchKeyFunctions_inl.h

// NB: The implementing C++ file is RegisterDispatchKey.cpp

// The only #includes we need are for custom classes that have defaults in the C++ API
#include <c10/core/MemoryFormat.h>
#include <c10/core/Scalar.h>
#include <ATen/core/Reduction.h>

namespace at {
namespace compositeexplicitautograd {

TORCH_API at::Tensor _fw_primal(const at::Tensor & self, int64_t level);
TORCH_API at::Tensor abs(const at::Tensor & self);
TORCH_API at::Tensor & abs_(at::Tensor & self);
TORCH_API at::Tensor sgn(const at::Tensor & self);
TORCH_API at::Tensor & sgn_(at::Tensor & self);
TORCH_API at::Tensor _conj(const at::Tensor & self);
TORCH_API at::Tensor _conj_physical(const at::Tensor & self);
TORCH_API at::Tensor & conj_physical_(at::Tensor & self);
TORCH_API at::Tensor _neg_view(const at::Tensor & self);
TORCH_API at::Tensor acos(const at::Tensor & self);
TORCH_API at::Tensor & acos_(at::Tensor & self);
TORCH_API at::Tensor add(const at::Tensor & self, const at::Tensor & other, const at::Scalar & alpha=1);
TORCH_API at::Tensor & add_(at::Tensor & self, const at::Tensor & other, const at::Scalar & alpha=1);
TORCH_API at::Tensor add(const at::Tensor & self, const at::Scalar & other, const at::Scalar & alpha=1);
TORCH_API at::Tensor & add_(at::Tensor & self, const at::Scalar & other, const at::Scalar & alpha=1);
TORCH_API at::Tensor addmv(const at::Tensor & self, const at::Tensor & mat, const at::Tensor & vec, const at::Scalar & beta=1, const at::Scalar & alpha=1);
TORCH_API at::Tensor & addmv_(at::Tensor & self, const at::Tensor & mat, const at::Tensor & vec, const at::Scalar & beta=1, const at::Scalar & alpha=1);
TORCH_API at::Tensor & addr_(at::Tensor & self, const at::Tensor & vec1, const at::Tensor & vec2, const at::Scalar & beta=1, const at::Scalar & alpha=1);
TORCH_API at::Tensor affine_grid_generator(const at::Tensor & theta, at::IntArrayRef size, bool align_corners);
TORCH_API at::Tensor all(const at::Tensor & self, int64_t dim, bool keepdim=false);
TORCH_API at::Tensor any(const at::Tensor & self, int64_t dim, bool keepdim=false);
TORCH_API at::Tensor argmax(const at::Tensor & self, c10::optional<int64_t> dim=c10::nullopt, bool keepdim=false);
TORCH_API at::Tensor argmin(const at::Tensor & self, c10::optional<int64_t> dim=c10::nullopt, bool keepdim=false);
TORCH_API at::Tensor acosh(const at::Tensor & self);
TORCH_API at::Tensor & acosh_(at::Tensor & self);
TORCH_API at::Tensor asinh(const at::Tensor & self);
TORCH_API at::Tensor & asinh_(at::Tensor & self);
TORCH_API at::Tensor atanh(const at::Tensor & self);
TORCH_API at::Tensor & atanh_(at::Tensor & self);
TORCH_API const at::Tensor & as_strided_(const at::Tensor & self, at::IntArrayRef size, at::IntArrayRef stride, c10::optional<int64_t> storage_offset=c10::nullopt);
TORCH_API at::Tensor asin(const at::Tensor & self);
TORCH_API at::Tensor & asin_(at::Tensor & self);
TORCH_API at::Tensor atan(const at::Tensor & self);
TORCH_API at::Tensor & atan_(at::Tensor & self);
TORCH_API at::Tensor bernoulli(const at::Tensor & self, c10::optional<at::Generator> generator=c10::nullopt);
TORCH_API at::Tensor binary_cross_entropy_with_logits(const at::Tensor & self, const at::Tensor & target, const c10::optional<at::Tensor> & weight={}, const c10::optional<at::Tensor> & pos_weight={}, int64_t reduction=at::Reduction::Mean);
TORCH_API at::Tensor bitwise_not(const at::Tensor & self);
TORCH_API at::Tensor & bitwise_not_(at::Tensor & self);
TORCH_API at::Tensor copysign(const at::Tensor & self, const at::Tensor & other);
TORCH_API at::Tensor & copysign_(at::Tensor & self, const at::Tensor & other);
TORCH_API at::Tensor copysign(const at::Tensor & self, const at::Scalar & other);
TORCH_API at::Tensor & copysign_out(at::Tensor & out, const at::Tensor & self, const at::Scalar & other);
TORCH_API at::Tensor & copysign_outf(const at::Tensor & self, const at::Scalar & other, at::Tensor & out);
TORCH_API at::Tensor & copysign_(at::Tensor & self, const at::Scalar & other);
TORCH_API at::Tensor cat(at::TensorList tensors, int64_t dim=0);
TORCH_API at::Tensor & cat_out(at::Tensor & out, at::TensorList tensors, int64_t dim=0);
TORCH_API at::Tensor & cat_outf(at::TensorList tensors, int64_t dim, at::Tensor & out);
TORCH_API at::Tensor ceil(const at::Tensor & self);
TORCH_API at::Tensor & ceil_(at::Tensor & self);
TORCH_API at::Tensor clamp(const at::Tensor & self, const c10::optional<at::Scalar> & min, const c10::optional<at::Scalar> & max=c10::nullopt);
TORCH_API at::Tensor & clamp_(at::Tensor & self, const c10::optional<at::Scalar> & min, const c10::optional<at::Scalar> & max=c10::nullopt);
TORCH_API at::Tensor & clamp_(at::Tensor & self, const c10::optional<at::Tensor> & min={}, const c10::optional<at::Tensor> & max={});
TORCH_API at::Tensor clamp_max(const at::Tensor & self, const at::Scalar & max);
TORCH_API at::Tensor & clamp_max_(at::Tensor & self, const at::Scalar & max);
TORCH_API at::Tensor clamp_max(const at::Tensor & self, const at::Tensor & max);
TORCH_API at::Tensor & clamp_max_(at::Tensor & self, const at::Tensor & max);
TORCH_API at::Tensor clamp_min(const at::Tensor & self, const at::Scalar & min);
TORCH_API at::Tensor & clamp_min_(at::Tensor & self, const at::Scalar & min);
TORCH_API at::Tensor clamp_min(const at::Tensor & self, const at::Tensor & min);
TORCH_API at::Tensor & clamp_min_(at::Tensor & self, const at::Tensor & min);
TORCH_API at::Tensor complex(const at::Tensor & real, const at::Tensor & imag);
TORCH_API at::Tensor polar(const at::Tensor & abs, const at::Tensor & angle);
TORCH_API at::Tensor constant_pad_nd(const at::Tensor & self, at::IntArrayRef pad, const at::Scalar & value=0);
TORCH_API at::Tensor convolution_overrideable(const at::Tensor & input, const at::Tensor & weight, const c10::optional<at::Tensor> & bias, at::IntArrayRef stride, at::IntArrayRef padding, at::IntArrayRef dilation, bool transposed, at::IntArrayRef output_padding, int64_t groups);
TORCH_API ::std::tuple<at::Tensor,at::Tensor,at::Tensor> convolution_backward_overrideable(const at::Tensor & grad_output, const at::Tensor & input, const at::Tensor & weight, at::IntArrayRef stride, at::IntArrayRef padding, at::IntArrayRef dilation, bool transposed, at::IntArrayRef output_padding, int64_t groups, ::std::array<bool,3> output_mask);
TORCH_API at::Tensor conv_tbc(const at::Tensor & self, const at::Tensor & weight, const at::Tensor & bias, int64_t pad=0);
TORCH_API at::Tensor & copy_(at::Tensor & self, const at::Tensor & src, bool non_blocking=false);
TORCH_API at::Tensor cos(const at::Tensor & self);
TORCH_API at::Tensor & cos_(at::Tensor & self);
TORCH_API at::Tensor cosh(const at::Tensor & self);
TORCH_API at::Tensor & cosh_(at::Tensor & self);
TORCH_API at::Tensor count_nonzero(const at::Tensor & self, c10::optional<int64_t> dim=c10::nullopt);
TORCH_API ::std::tuple<at::Tensor,at::Tensor> cummax(const at::Tensor & self, int64_t dim);
TORCH_API ::std::tuple<at::Tensor &,at::Tensor &> cummax_out(at::Tensor & values, at::Tensor & indices, const at::Tensor & self, int64_t dim);
TORCH_API ::std::tuple<at::Tensor &,at::Tensor &> cummax_outf(const at::Tensor & self, int64_t dim, at::Tensor & values, at::Tensor & indices);
TORCH_API ::std::tuple<at::Tensor,at::Tensor> cummin(const at::Tensor & self, int64_t dim);
TORCH_API ::std::tuple<at::Tensor &,at::Tensor &> cummin_out(at::Tensor & values, at::Tensor & indices, const at::Tensor & self, int64_t dim);
TORCH_API ::std::tuple<at::Tensor &,at::Tensor &> cummin_outf(const at::Tensor & self, int64_t dim, at::Tensor & values, at::Tensor & indices);
TORCH_API at::Tensor cumprod(const at::Tensor & self, int64_t dim, c10::optional<at::ScalarType> dtype=c10::nullopt);
TORCH_API at::Tensor & cumprod_(at::Tensor & self, int64_t dim, c10::optional<at::ScalarType> dtype=c10::nullopt);
TORCH_API at::Tensor cumsum(const at::Tensor & self, int64_t dim, c10::optional<at::ScalarType> dtype=c10::nullopt);
TORCH_API at::Tensor & cumsum_(at::Tensor & self, int64_t dim, c10::optional<at::ScalarType> dtype=c10::nullopt);
TORCH_API at::Tensor diagonal(const at::Tensor & self, int64_t offset=0, int64_t dim1=0, int64_t dim2=1);
TORCH_API at::Tensor diagonal_backward(const at::Tensor & grad_output, at::IntArrayRef input_sizes, int64_t offset, int64_t dim1, int64_t dim2);
TORCH_API at::Tensor div(const at::Tensor & self, const at::Tensor & other);
TORCH_API at::Tensor & div_(at::Tensor & self, const at::Tensor & other);
TORCH_API at::Tensor div(const at::Tensor & self, const at::Tensor & other, c10::optional<c10::string_view> rounding_mode);
TORCH_API at::Tensor & div_(at::Tensor & self, const at::Tensor & other, c10::optional<c10::string_view> rounding_mode);
TORCH_API at::Tensor div(const at::Tensor & self, const at::Scalar & other);
TORCH_API at::Tensor & div_(at::Tensor & self, const at::Scalar & other);
TORCH_API at::Tensor div(const at::Tensor & self, const at::Scalar & other, c10::optional<c10::string_view> rounding_mode);
TORCH_API at::Tensor & div_(at::Tensor & self, const at::Scalar & other, c10::optional<c10::string_view> rounding_mode);
TORCH_API at::Tensor & dot_out(at::Tensor & out, const at::Tensor & self, const at::Tensor & tensor);
TORCH_API at::Tensor & dot_outf(const at::Tensor & self, const at::Tensor & tensor, at::Tensor & out);
TORCH_API at::Tensor & vdot_out(at::Tensor & out, const at::Tensor & self, const at::Tensor & other);
TORCH_API at::Tensor & vdot_outf(const at::Tensor & self, const at::Tensor & other, at::Tensor & out);
TORCH_API at::Tensor embedding(const at::Tensor & weight, const at::Tensor & indices, int64_t padding_idx=-1, bool scale_grad_by_freq=false, bool sparse=false);
TORCH_API at::Tensor erf(const at::Tensor & self);
TORCH_API at::Tensor & erf_(at::Tensor & self);
TORCH_API at::Tensor erfc(const at::Tensor & self);
TORCH_API at::Tensor & erfc_(at::Tensor & self);
TORCH_API at::Tensor exp(const at::Tensor & self);
TORCH_API at::Tensor & exp_(at::Tensor & self);
TORCH_API at::Tensor exp2(const at::Tensor & self);
TORCH_API at::Tensor & exp2_(at::Tensor & self);
TORCH_API at::Tensor expm1(const at::Tensor & self);
TORCH_API at::Tensor & expm1_(at::Tensor & self);
TORCH_API at::Tensor expand(const at::Tensor & self, at::IntArrayRef size, bool implicit=false);
TORCH_API at::Tensor floor(const at::Tensor & self);
TORCH_API at::Tensor & floor_(at::Tensor & self);
TORCH_API at::Tensor frac(const at::Tensor & self);
TORCH_API at::Tensor & frac_(at::Tensor & self);
TORCH_API at::Tensor gcd(const at::Tensor & self, const at::Tensor & other);
TORCH_API at::Tensor & gcd_(at::Tensor & self, const at::Tensor & other);
TORCH_API at::Tensor lcm(const at::Tensor & self, const at::Tensor & other);
TORCH_API at::Tensor & lcm_(at::Tensor & self, const at::Tensor & other);
TORCH_API at::Tensor _grid_sampler_2d_cpu_fallback(const at::Tensor & input, const at::Tensor & grid, int64_t interpolation_mode, int64_t padding_mode, bool align_corners);
TORCH_API at::Tensor & index_copy_(at::Tensor & self, int64_t dim, const at::Tensor & index, const at::Tensor & source);
TORCH_API at::Tensor & index_put_(at::Tensor & self, const c10::List<c10::optional<at::Tensor>> & indices, const at::Tensor & values, bool accumulate=false);
TORCH_API at::Tensor inverse(const at::Tensor & self);
TORCH_API at::Tensor & inverse_out(at::Tensor & out, const at::Tensor & self);
TORCH_API at::Tensor & inverse_outf(const at::Tensor & self, at::Tensor & out);
TORCH_API at::Tensor isin(const at::Tensor & elements, const at::Tensor & test_elements, bool assume_unique=false, bool invert=false);
TORCH_API at::Tensor isin(const at::Tensor & elements, const at::Scalar & test_element, bool assume_unique=false, bool invert=false);
TORCH_API at::Tensor isin(const at::Scalar & element, const at::Tensor & test_elements, bool assume_unique=false, bool invert=false);
TORCH_API at::Tensor kl_div(const at::Tensor & self, const at::Tensor & target, int64_t reduction=at::Reduction::Mean, bool log_target=false);
TORCH_API ::std::tuple<at::Tensor,at::Tensor> kthvalue(const at::Tensor & self, int64_t k, int64_t dim=-1, bool keepdim=false);
TORCH_API at::Tensor nan_to_num(const at::Tensor & self, c10::optional<double> nan=c10::nullopt, c10::optional<double> posinf=c10::nullopt, c10::optional<double> neginf=c10::nullopt);
TORCH_API at::Tensor & nan_to_num_(at::Tensor & self, c10::optional<double> nan=c10::nullopt, c10::optional<double> posinf=c10::nullopt, c10::optional<double> neginf=c10::nullopt);
TORCH_API at::Tensor log(const at::Tensor & self);
TORCH_API at::Tensor & log_(at::Tensor & self);
TORCH_API at::Tensor log10(const at::Tensor & self);
TORCH_API at::Tensor & log10_(at::Tensor & self);
TORCH_API at::Tensor log1p(const at::Tensor & self);
TORCH_API at::Tensor & log1p_(at::Tensor & self);
TORCH_API at::Tensor log2(const at::Tensor & self);
TORCH_API at::Tensor & log2_(at::Tensor & self);
TORCH_API at::Tensor logaddexp(const at::Tensor & self, const at::Tensor & other);
TORCH_API at::Tensor logaddexp2(const at::Tensor & self, const at::Tensor & other);
TORCH_API at::Tensor xlogy(const at::Tensor & self, const at::Tensor & other);
TORCH_API at::Tensor & xlogy_(at::Tensor & self, const at::Tensor & other);
TORCH_API at::Tensor xlogy(const at::Scalar & self, const at::Tensor & other);
TORCH_API at::Tensor & xlogy_out(at::Tensor & out, const at::Scalar & self, const at::Tensor & other);
TORCH_API at::Tensor & xlogy_outf(const at::Scalar & self, const at::Tensor & other, at::Tensor & out);
TORCH_API at::Tensor xlogy(const at::Tensor & self, const at::Scalar & other);
TORCH_API at::Tensor & xlogy_out(at::Tensor & out, const at::Tensor & self, const at::Scalar & other);
TORCH_API at::Tensor & xlogy_outf(const at::Tensor & self, const at::Scalar & other, at::Tensor & out);
TORCH_API at::Tensor & xlogy_(at::Tensor & self, const at::Scalar & other);
TORCH_API at::Tensor logdet(const at::Tensor & self);
TORCH_API at::Tensor _log_softmax(const at::Tensor & self, int64_t dim, bool half_to_float);
TORCH_API at::Tensor _log_softmax_backward_data(const at::Tensor & grad_output, const at::Tensor & output, int64_t dim, const at::Tensor & self);
TORCH_API at::Tensor logcumsumexp(const at::Tensor & self, int64_t dim);
TORCH_API at::Tensor & logcumsumexp_out(at::Tensor & out, const at::Tensor & self, int64_t dim);
TORCH_API at::Tensor & logcumsumexp_outf(const at::Tensor & self, int64_t dim, at::Tensor & out);
TORCH_API at::Tensor logsumexp(const at::Tensor & self, at::IntArrayRef dim, bool keepdim=false);
TORCH_API at::Tensor & logsumexp_out(at::Tensor & out, const at::Tensor & self, at::IntArrayRef dim, bool keepdim=false);
TORCH_API at::Tensor & logsumexp_outf(const at::Tensor & self, at::IntArrayRef dim, bool keepdim, at::Tensor & out);
TORCH_API ::std::tuple<at::Tensor,at::Tensor> aminmax(const at::Tensor & self, c10::optional<int64_t> dim=c10::nullopt, bool keepdim=false);
TORCH_API at::Tensor amax(const at::Tensor & self, at::IntArrayRef dim={}, bool keepdim=false);
TORCH_API at::Tensor mean(const at::Tensor & self, c10::optional<at::ScalarType> dtype=c10::nullopt);
TORCH_API at::Tensor mean(const at::Tensor & self, at::IntArrayRef dim, bool keepdim=false, c10::optional<at::ScalarType> dtype=c10::nullopt);
TORCH_API ::std::tuple<at::Tensor,at::Tensor> median(const at::Tensor & self, int64_t dim, bool keepdim=false);
TORCH_API ::std::tuple<at::Tensor,at::Tensor> nanmedian(const at::Tensor & self, int64_t dim, bool keepdim=false);
TORCH_API at::Tensor amin(const at::Tensor & self, at::IntArrayRef dim={}, bool keepdim=false);
TORCH_API at::Tensor mkldnn_convolution(const at::Tensor & self, const at::Tensor & weight, const c10::optional<at::Tensor> & bias, at::IntArrayRef padding, at::IntArrayRef stride, at::IntArrayRef dilation, int64_t groups);
TORCH_API ::std::tuple<at::Tensor,at::Tensor,at::Tensor> mkldnn_convolution_backward(const at::Tensor & self, const at::Tensor & grad_output, const at::Tensor & weight, at::IntArrayRef padding, at::IntArrayRef stride, at::IntArrayRef dilation, int64_t groups, ::std::array<bool,3> output_mask);
TORCH_API at::Tensor mm(const at::Tensor & self, const at::Tensor & mat2);
TORCH_API ::std::tuple<at::Tensor &,at::Tensor &> mode_out(at::Tensor & values, at::Tensor & indices, const at::Tensor & self, int64_t dim=-1, bool keepdim=false);
TORCH_API ::std::tuple<at::Tensor &,at::Tensor &> mode_outf(const at::Tensor & self, int64_t dim, bool keepdim, at::Tensor & values, at::Tensor & indices);
TORCH_API at::Tensor mul(const at::Tensor & self, const at::Tensor & other);
TORCH_API at::Tensor & mul_(at::Tensor & self, const at::Tensor & other);
TORCH_API at::Tensor mul(const at::Tensor & self, const at::Scalar & other);
TORCH_API at::Tensor & mul_(at::Tensor & self, const at::Scalar & other);
TORCH_API at::Tensor & mv_out(at::Tensor & out, const at::Tensor & self, const at::Tensor & vec);
TORCH_API at::Tensor & mv_outf(const at::Tensor & self, const at::Tensor & vec, at::Tensor & out);
TORCH_API at::Tensor mvlgamma(const at::Tensor & self, int64_t p);
TORCH_API at::Tensor & mvlgamma_(at::Tensor & self, int64_t p);
TORCH_API at::Tensor narrow_copy(const at::Tensor & self, int64_t dim, int64_t start, int64_t length);
TORCH_API at::Tensor _nnpack_spatial_convolution(const at::Tensor & input, const at::Tensor & weight, const c10::optional<at::Tensor> & bias, at::IntArrayRef padding, at::IntArrayRef stride=1);
TORCH_API at::Tensor _euclidean_dist(const at::Tensor & x1, const at::Tensor & x2);
TORCH_API at::Tensor permute(const at::Tensor & self, at::IntArrayRef dims);
TORCH_API bool is_pinned(const at::Tensor & self, c10::optional<at::Device> device=c10::nullopt);
TORCH_API at::Tensor rad2deg(const at::Tensor & self);
TORCH_API at::Tensor & rad2deg_out(at::Tensor & out, const at::Tensor & self);
TORCH_API at::Tensor & rad2deg_outf(const at::Tensor & self, at::Tensor & out);
TORCH_API at::Tensor & rad2deg_(at::Tensor & self);
TORCH_API at::Tensor deg2rad(const at::Tensor & self);
TORCH_API at::Tensor & deg2rad_out(at::Tensor & out, const at::Tensor & self);
TORCH_API at::Tensor & deg2rad_outf(const at::Tensor & self, at::Tensor & out);
TORCH_API at::Tensor & deg2rad_(at::Tensor & self);
TORCH_API at::Tensor reciprocal(const at::Tensor & self);
TORCH_API at::Tensor & reciprocal_(at::Tensor & self);
TORCH_API at::Tensor neg(const at::Tensor & self);
TORCH_API at::Tensor & neg_(at::Tensor & self);
TORCH_API at::Tensor repeat(const at::Tensor & self, at::IntArrayRef repeats);
TORCH_API at::Tensor round(const at::Tensor & self);
TORCH_API at::Tensor & round_(at::Tensor & self);
TORCH_API at::Tensor gelu(const at::Tensor & self);
TORCH_API at::Tensor gelu_backward(const at::Tensor & grad, const at::Tensor & self);
TORCH_API at::Tensor hardshrink(const at::Tensor & self, const at::Scalar & lambd=0.5);
TORCH_API at::Tensor hardshrink_backward(const at::Tensor & grad_out, const at::Tensor & self, const at::Scalar & lambd);
TORCH_API at::Tensor rsqrt(const at::Tensor & self);
TORCH_API at::Tensor & rsqrt_(at::Tensor & self);
TORCH_API at::Tensor select(const at::Tensor & self, int64_t dim, int64_t index);
TORCH_API at::Tensor select_backward(const at::Tensor & grad_output, at::IntArrayRef input_sizes, int64_t dim, int64_t index);
TORCH_API at::Tensor celu(const at::Tensor & self, const at::Scalar & alpha=1.0);
TORCH_API at::Tensor & celu_(at::Tensor & self, const at::Scalar & alpha=1.0);
TORCH_API at::Tensor silu(const at::Tensor & self);
TORCH_API at::Tensor & silu_(at::Tensor & self);
TORCH_API at::Tensor silu_backward(const at::Tensor & grad_output, const at::Tensor & self);
TORCH_API at::Tensor mish(const at::Tensor & self);
TORCH_API at::Tensor & mish_(at::Tensor & self);
TORCH_API at::Tensor sigmoid(const at::Tensor & self);
TORCH_API at::Tensor & sigmoid_(at::Tensor & self);
TORCH_API at::Tensor sin(const at::Tensor & self);
TORCH_API at::Tensor & sin_(at::Tensor & self);
TORCH_API at::Tensor sinc(const at::Tensor & self);
TORCH_API at::Tensor & sinc_(at::Tensor & self);
TORCH_API at::Tensor sinh(const at::Tensor & self);
TORCH_API at::Tensor & sinh_(at::Tensor & self);
TORCH_API at::Tensor detach(const at::Tensor & self);
TORCH_API at::Tensor & detach_(at::Tensor & self);
TORCH_API at::Tensor slice(const at::Tensor & self, int64_t dim=0, c10::optional<int64_t> start=c10::nullopt, c10::optional<int64_t> end=c10::nullopt, int64_t step=1);
TORCH_API at::Tensor slice_backward(const at::Tensor & grad_output, at::IntArrayRef input_sizes, int64_t dim, int64_t start, int64_t end, int64_t step);
TORCH_API ::std::tuple<at::Tensor,at::Tensor> slogdet(const at::Tensor & self);
TORCH_API at::Tensor _softmax(const at::Tensor & self, int64_t dim, bool half_to_float);
TORCH_API at::Tensor _softmax_backward_data(const at::Tensor & grad_output, const at::Tensor & output, int64_t dim, const at::Tensor & self);
TORCH_API ::std::vector<at::Tensor> unsafe_split(const at::Tensor & self, int64_t split_size, int64_t dim=0);
TORCH_API ::std::vector<at::Tensor> split(const at::Tensor & self, int64_t split_size, int64_t dim=0);
TORCH_API ::std::vector<at::Tensor> unsafe_split_with_sizes(const at::Tensor & self, at::IntArrayRef split_sizes, int64_t dim=0);
TORCH_API ::std::vector<at::Tensor> split_with_sizes(const at::Tensor & self, at::IntArrayRef split_sizes, int64_t dim=0);
TORCH_API at::Tensor squeeze(const at::Tensor & self);
TORCH_API at::Tensor & squeeze_(at::Tensor & self);
TORCH_API at::Tensor squeeze(const at::Tensor & self, int64_t dim);
TORCH_API at::Tensor & squeeze_(at::Tensor & self, int64_t dim);
TORCH_API at::Tensor stack(at::TensorList tensors, int64_t dim=0);
TORCH_API at::Tensor & stack_out(at::Tensor & out, at::TensorList tensors, int64_t dim=0);
TORCH_API at::Tensor & stack_outf(at::TensorList tensors, int64_t dim, at::Tensor & out);
TORCH_API at::Tensor _stack(at::TensorList tensors, int64_t dim=0);
TORCH_API at::Tensor & _stack_out(at::Tensor & out, at::TensorList tensors, int64_t dim=0);
TORCH_API at::Tensor & _stack_outf(at::TensorList tensors, int64_t dim, at::Tensor & out);
TORCH_API at::Tensor sum(const at::Tensor & self, c10::optional<at::ScalarType> dtype=c10::nullopt);
TORCH_API at::Tensor sum(const at::Tensor & self, at::IntArrayRef dim, bool keepdim=false, c10::optional<at::ScalarType> dtype=c10::nullopt);
TORCH_API at::Tensor sqrt(const at::Tensor & self);
TORCH_API at::Tensor & sqrt_(at::Tensor & self);
TORCH_API at::Tensor prod(const at::Tensor & self, int64_t dim, bool keepdim=false, c10::optional<at::ScalarType> dtype=c10::nullopt);
TORCH_API at::Tensor t(const at::Tensor & self);
TORCH_API at::Tensor & t_(at::Tensor & self);
TORCH_API at::Tensor tan(const at::Tensor & self);
TORCH_API at::Tensor & tan_(at::Tensor & self);
TORCH_API at::Tensor tanh(const at::Tensor & self);
TORCH_API at::Tensor & tanh_(at::Tensor & self);
TORCH_API at::Tensor threshold(const at::Tensor & self, const at::Scalar & threshold, const at::Scalar & value);
TORCH_API at::Tensor & threshold_(at::Tensor & self, const at::Scalar & threshold, const at::Scalar & value);
TORCH_API at::Tensor threshold_backward(const at::Tensor & grad_output, const at::Tensor & self, const at::Scalar & threshold);
TORCH_API at::Tensor transpose(const at::Tensor & self, int64_t dim0, int64_t dim1);
TORCH_API at::Tensor & transpose_(at::Tensor & self, int64_t dim0, int64_t dim1);
TORCH_API at::Tensor rot90(const at::Tensor & self, int64_t k=1, at::IntArrayRef dims={0,1});
TORCH_API at::Tensor _trilinear(const at::Tensor & i1, const at::Tensor & i2, const at::Tensor & i3, at::IntArrayRef expand1, at::IntArrayRef expand2, at::IntArrayRef expand3, at::IntArrayRef sumdim, int64_t unroll_dim=1);
TORCH_API at::Tensor trunc(const at::Tensor & self);
TORCH_API at::Tensor & trunc_(at::Tensor & self);
TORCH_API at::Tensor _unsafe_view(const at::Tensor & self, at::IntArrayRef size);
TORCH_API at::Tensor unsqueeze(const at::Tensor & self, int64_t dim);
TORCH_API at::Tensor & unsqueeze_(at::Tensor & self, int64_t dim);
TORCH_API at::Tensor _sparse_sum(const at::Tensor & self, at::IntArrayRef dim);
TORCH_API at::Tensor norm(const at::Tensor & self, const c10::optional<at::Scalar> & p, at::ScalarType dtype);
TORCH_API at::Tensor norm(const at::Tensor & self, const at::Scalar & p=2);
TORCH_API at::Tensor norm(const at::Tensor & self, const c10::optional<at::Scalar> & p, at::IntArrayRef dim, bool keepdim, at::ScalarType dtype);
TORCH_API at::Tensor norm(const at::Tensor & self, const c10::optional<at::Scalar> & p, at::IntArrayRef dim, bool keepdim=false);
TORCH_API ::std::tuple<at::Tensor,at::Tensor> frexp(const at::Tensor & self);
TORCH_API at::Tensor clone(const at::Tensor & self, c10::optional<at::MemoryFormat> memory_format=c10::nullopt);
TORCH_API const at::Tensor & resize_as_(const at::Tensor & self, const at::Tensor & the_template, c10::optional<at::MemoryFormat> memory_format=c10::nullopt);
TORCH_API at::Tensor sub(const at::Tensor & self, const at::Tensor & other, const at::Scalar & alpha=1);
TORCH_API at::Tensor & sub_(at::Tensor & self, const at::Tensor & other, const at::Scalar & alpha=1);
TORCH_API at::Tensor sub(const at::Tensor & self, const at::Scalar & other, const at::Scalar & alpha=1);
TORCH_API at::Tensor & sub_(at::Tensor & self, const at::Scalar & other, const at::Scalar & alpha=1);
TORCH_API at::Tensor heaviside(const at::Tensor & self, const at::Tensor & values);
TORCH_API at::Tensor & heaviside_(at::Tensor & self, const at::Tensor & values);
TORCH_API at::Tensor rsub(const at::Tensor & self, const at::Scalar & other, const at::Scalar & alpha=1);
TORCH_API at::Tensor _sparse_addmm(const at::Tensor & self, const at::Tensor & sparse, const at::Tensor & dense, const at::Scalar & beta=1, const at::Scalar & alpha=1);
TORCH_API at::Tensor addmm(const at::Tensor & self, const at::Tensor & mat1, const at::Tensor & mat2, const at::Scalar & beta=1, const at::Scalar & alpha=1);
TORCH_API at::Tensor & addmm_(at::Tensor & self, const at::Tensor & mat1, const at::Tensor & mat2, const at::Scalar & beta=1, const at::Scalar & alpha=1);
TORCH_API ::std::vector<at::Tensor> unbind(const at::Tensor & self, int64_t dim=0);
TORCH_API at::Tensor _to_copy(const at::Tensor & self, at::TensorOptions options={}, bool non_blocking=false, c10::optional<at::MemoryFormat> memory_format=c10::nullopt);
TORCH_API at::Tensor _to_copy(const at::Tensor & self, c10::optional<at::ScalarType> dtype, c10::optional<at::Layout> layout, c10::optional<at::Device> device, c10::optional<bool> pin_memory, bool non_blocking, c10::optional<at::MemoryFormat> memory_format);
TORCH_API ::std::tuple<at::Tensor,at::Tensor> _pack_padded_sequence(const at::Tensor & input, const at::Tensor & lengths, bool batch_first);
TORCH_API at::Tensor view(const at::Tensor & self, at::ScalarType dtype);
TORCH_API at::Tensor scatter(const at::Tensor & self, int64_t dim, const at::Tensor & index, const at::Tensor & src);
TORCH_API at::Tensor & scatter_(at::Tensor & self, int64_t dim, const at::Tensor & index, const at::Tensor & src);
TORCH_API at::Tensor scatter(const at::Tensor & self, int64_t dim, const at::Tensor & index, const at::Scalar & value);
TORCH_API at::Tensor & scatter_(at::Tensor & self, int64_t dim, const at::Tensor & index, const at::Scalar & value);
TORCH_API at::Tensor scatter(const at::Tensor & self, int64_t dim, const at::Tensor & index, const at::Tensor & src, c10::string_view reduce);
TORCH_API at::Tensor & scatter_(at::Tensor & self, int64_t dim, const at::Tensor & index, const at::Tensor & src, c10::string_view reduce);
TORCH_API at::Tensor scatter(const at::Tensor & self, int64_t dim, const at::Tensor & index, const at::Scalar & value, c10::string_view reduce);
TORCH_API at::Tensor & scatter_(at::Tensor & self, int64_t dim, const at::Tensor & index, const at::Scalar & value, c10::string_view reduce);
TORCH_API at::Tensor scatter_add(const at::Tensor & self, int64_t dim, const at::Tensor & index, const at::Tensor & src);
TORCH_API at::Tensor & scatter_add_(at::Tensor & self, int64_t dim, const at::Tensor & index, const at::Tensor & src);
TORCH_API at::Tensor eq(const at::Tensor & self, const at::Scalar & other);
TORCH_API at::Tensor & eq_(at::Tensor & self, const at::Scalar & other);
TORCH_API at::Tensor eq(const at::Tensor & self, const at::Tensor & other);
TORCH_API at::Tensor & eq_(at::Tensor & self, const at::Tensor & other);
TORCH_API at::Tensor bitwise_and(const at::Tensor & self, const at::Tensor & other);
TORCH_API at::Tensor & bitwise_and_(at::Tensor & self, const at::Tensor & other);
TORCH_API at::Tensor bitwise_and(const at::Tensor & self, const at::Scalar & other);
TORCH_API at::Tensor & bitwise_and_out(at::Tensor & out, const at::Tensor & self, const at::Scalar & other);
TORCH_API at::Tensor & bitwise_and_outf(const at::Tensor & self, const at::Scalar & other, at::Tensor & out);
TORCH_API at::Tensor bitwise_or(const at::Tensor & self, const at::Tensor & other);
TORCH_API at::Tensor & bitwise_or_(at::Tensor & self, const at::Tensor & other);
TORCH_API at::Tensor & bitwise_or_out(at::Tensor & out, const at::Tensor & self, const at::Scalar & other);
TORCH_API at::Tensor & bitwise_or_outf(const at::Tensor & self, const at::Scalar & other, at::Tensor & out);
TORCH_API at::Tensor bitwise_xor(const at::Tensor & self, const at::Tensor & other);
TORCH_API at::Tensor & bitwise_xor_(at::Tensor & self, const at::Tensor & other);
TORCH_API at::Tensor & bitwise_xor_out(at::Tensor & out, const at::Tensor & self, const at::Scalar & other);
TORCH_API at::Tensor & bitwise_xor_outf(const at::Tensor & self, const at::Scalar & other, at::Tensor & out);
TORCH_API at::Tensor bitwise_left_shift(const at::Tensor & self, const at::Tensor & other);
TORCH_API at::Tensor & bitwise_left_shift_(at::Tensor & self, const at::Tensor & other);
TORCH_API at::Tensor bitwise_right_shift(const at::Tensor & self, const at::Tensor & other);
TORCH_API at::Tensor & bitwise_right_shift_(at::Tensor & self, const at::Tensor & other);
TORCH_API at::Tensor tril(const at::Tensor & self, int64_t diagonal=0);
TORCH_API at::Tensor triu(const at::Tensor & self, int64_t diagonal=0);
TORCH_API at::Tensor digamma(const at::Tensor & self);
TORCH_API at::Tensor & digamma_(at::Tensor & self);
TORCH_API at::Tensor diag(const at::Tensor & self, int64_t diagonal=0);
TORCH_API at::Tensor ne(const at::Tensor & self, const at::Scalar & other);
TORCH_API at::Tensor & ne_(at::Tensor & self, const at::Scalar & other);
TORCH_API at::Tensor ne(const at::Tensor & self, const at::Tensor & other);
TORCH_API at::Tensor & ne_(at::Tensor & self, const at::Tensor & other);
TORCH_API at::Tensor ge(const at::Tensor & self, const at::Scalar & other);
TORCH_API at::Tensor & ge_(at::Tensor & self, const at::Scalar & other);
TORCH_API at::Tensor ge(const at::Tensor & self, const at::Tensor & other);
TORCH_API at::Tensor & ge_(at::Tensor & self, const at::Tensor & other);
TORCH_API at::Tensor le(const at::Tensor & self, const at::Scalar & other);
TORCH_API at::Tensor & le_(at::Tensor & self, const at::Scalar & other);
TORCH_API at::Tensor le(const at::Tensor & self, const at::Tensor & other);
TORCH_API at::Tensor & le_(at::Tensor & self, const at::Tensor & other);
TORCH_API at::Tensor gt(const at::Tensor & self, const at::Scalar & other);
TORCH_API at::Tensor & gt_(at::Tensor & self, const at::Scalar & other);
TORCH_API at::Tensor gt(const at::Tensor & self, const at::Tensor & other);
TORCH_API at::Tensor & gt_(at::Tensor & self, const at::Tensor & other);
TORCH_API at::Tensor lt(const at::Tensor & self, const at::Scalar & other);
TORCH_API at::Tensor & lt_(at::Tensor & self, const at::Scalar & other);
TORCH_API at::Tensor lt(const at::Tensor & self, const at::Tensor & other);
TORCH_API at::Tensor & lt_(at::Tensor & self, const at::Tensor & other);
TORCH_API at::Tensor gather(const at::Tensor & self, int64_t dim, const at::Tensor & index, bool sparse_grad=false);
TORCH_API at::Tensor addcmul(const at::Tensor & self, const at::Tensor & tensor1, const at::Tensor & tensor2, const at::Scalar & value=1);
TORCH_API at::Tensor & addcmul_(at::Tensor & self, const at::Tensor & tensor1, const at::Tensor & tensor2, const at::Scalar & value=1);
TORCH_API at::Tensor addcdiv(const at::Tensor & self, const at::Tensor & tensor1, const at::Tensor & tensor2, const at::Scalar & value=1);
TORCH_API at::Tensor & addcdiv_(at::Tensor & self, const at::Tensor & tensor1, const at::Tensor & tensor2, const at::Scalar & value=1);
TORCH_API ::std::tuple<at::Tensor,at::Tensor> symeig(const at::Tensor & self, bool eigenvectors=false, bool upper=true);
TORCH_API ::std::tuple<at::Tensor &,at::Tensor &> symeig_out(at::Tensor & e, at::Tensor & V, const at::Tensor & self, bool eigenvectors=false, bool upper=true);
TORCH_API ::std::tuple<at::Tensor &,at::Tensor &> symeig_outf(const at::Tensor & self, bool eigenvectors, bool upper, at::Tensor & e, at::Tensor & V);
TORCH_API ::std::tuple<at::Tensor,at::Tensor> eig(const at::Tensor & self, bool eigenvectors=false);
TORCH_API ::std::tuple<at::Tensor &,at::Tensor &> eig_out(at::Tensor & e, at::Tensor & v, const at::Tensor & self, bool eigenvectors=false);
TORCH_API ::std::tuple<at::Tensor &,at::Tensor &> eig_outf(const at::Tensor & self, bool eigenvectors, at::Tensor & e, at::Tensor & v);
TORCH_API at::Tensor cholesky_solve(const at::Tensor & self, const at::Tensor & input2, bool upper=false);
TORCH_API at::Tensor & cholesky_solve_out(at::Tensor & out, const at::Tensor & self, const at::Tensor & input2, bool upper=false);
TORCH_API at::Tensor & cholesky_solve_outf(const at::Tensor & self, const at::Tensor & input2, bool upper, at::Tensor & out);
TORCH_API ::std::tuple<at::Tensor,at::Tensor> solve(const at::Tensor & self, const at::Tensor & A);
TORCH_API ::std::tuple<at::Tensor &,at::Tensor &> solve_out(at::Tensor & solution, at::Tensor & lu, const at::Tensor & self, const at::Tensor & A);
TORCH_API ::std::tuple<at::Tensor &,at::Tensor &> solve_outf(const at::Tensor & self, const at::Tensor & A, at::Tensor & solution, at::Tensor & lu);
TORCH_API at::Tensor lgamma(const at::Tensor & self);
TORCH_API at::Tensor & lgamma_(at::Tensor & self);
TORCH_API at::Tensor polygamma(int64_t n, const at::Tensor & self);
TORCH_API at::Tensor & polygamma_(at::Tensor & self, int64_t n);
TORCH_API at::Tensor erfinv(const at::Tensor & self);
TORCH_API at::Tensor & erfinv_(at::Tensor & self);
TORCH_API at::Tensor i0(const at::Tensor & self);
TORCH_API at::Tensor & i0_(at::Tensor & self);
TORCH_API at::Tensor sign(const at::Tensor & self);
TORCH_API at::Tensor & sign_(at::Tensor & self);
TORCH_API at::Tensor signbit(const at::Tensor & self);
TORCH_API at::Tensor dist(const at::Tensor & self, const at::Tensor & other, const at::Scalar & p=2);
TORCH_API at::Tensor atan2(const at::Tensor & self, const at::Tensor & other);
TORCH_API at::Tensor & atan2_(at::Tensor & self, const at::Tensor & other);
TORCH_API at::Tensor fmod(const at::Tensor & self, const at::Scalar & other);
TORCH_API at::Tensor & fmod_out(at::Tensor & out, const at::Tensor & self, const at::Scalar & other);
TORCH_API at::Tensor & fmod_outf(const at::Tensor & self, const at::Scalar & other, at::Tensor & out);
TORCH_API at::Tensor & fmod_(at::Tensor & self, const at::Scalar & other);
TORCH_API at::Tensor fmod(const at::Tensor & self, const at::Tensor & other);
TORCH_API at::Tensor & fmod_(at::Tensor & self, const at::Tensor & other);
TORCH_API at::Tensor hypot(const at::Tensor & self, const at::Tensor & other);
TORCH_API at::Tensor & hypot_(at::Tensor & self, const at::Tensor & other);
TORCH_API at::Tensor igamma(const at::Tensor & self, const at::Tensor & other);
TORCH_API at::Tensor & igamma_(at::Tensor & self, const at::Tensor & other);
TORCH_API at::Tensor igammac(const at::Tensor & self, const at::Tensor & other);
TORCH_API at::Tensor & igammac_(at::Tensor & self, const at::Tensor & other);
TORCH_API at::Tensor nextafter(const at::Tensor & self, const at::Tensor & other);
TORCH_API at::Tensor & nextafter_(at::Tensor & self, const at::Tensor & other);
TORCH_API at::Tensor remainder(const at::Tensor & self, const at::Scalar & other);
TORCH_API at::Tensor & remainder_out(at::Tensor & out, const at::Tensor & self, const at::Scalar & other);
TORCH_API at::Tensor & remainder_outf(const at::Tensor & self, const at::Scalar & other, at::Tensor & out);
TORCH_API at::Tensor & remainder_(at::Tensor & self, const at::Scalar & other);
TORCH_API at::Tensor remainder(const at::Tensor & self, const at::Tensor & other);
TORCH_API at::Tensor & remainder_(at::Tensor & self, const at::Tensor & other);
TORCH_API at::Tensor fmin(const at::Tensor & self, const at::Tensor & other);
TORCH_API at::Tensor fmax(const at::Tensor & self, const at::Tensor & other);
TORCH_API at::Tensor maximum(const at::Tensor & self, const at::Tensor & other);
TORCH_API at::Tensor minimum(const at::Tensor & self, const at::Tensor & other);
TORCH_API ::std::tuple<at::Tensor,at::Tensor> topk(const at::Tensor & self, int64_t k, int64_t dim=-1, bool largest=true, bool sorted=true);
TORCH_API at::Tensor all(const at::Tensor & self);
TORCH_API at::Tensor any(const at::Tensor & self);
TORCH_API at::Tensor renorm(const at::Tensor & self, const at::Scalar & p, int64_t dim, const at::Scalar & maxnorm);
TORCH_API at::Tensor & renorm_(at::Tensor & self, const at::Scalar & p, int64_t dim, const at::Scalar & maxnorm);
TORCH_API at::Tensor pow(const at::Tensor & self, const at::Tensor & exponent);
TORCH_API at::Tensor & pow_(at::Tensor & self, const at::Tensor & exponent);
TORCH_API at::Tensor pow(const at::Scalar & self, const at::Tensor & exponent);
TORCH_API at::Tensor pow(const at::Tensor & self, const at::Scalar & exponent);
TORCH_API at::Tensor & pow_(at::Tensor & self, const at::Scalar & exponent);
TORCH_API at::Tensor alias(const at::Tensor & self);
TORCH_API at::Tensor _convert_indices_from_coo_to_csr(const at::Tensor & self, int64_t size, bool out_int32=false);
TORCH_API at::Tensor l1_loss(const at::Tensor & self, const at::Tensor & target, int64_t reduction=at::Reduction::Mean);
TORCH_API at::Tensor & l1_loss_out(at::Tensor & out, const at::Tensor & self, const at::Tensor & target, int64_t reduction=at::Reduction::Mean);
TORCH_API at::Tensor & l1_loss_outf(const at::Tensor & self, const at::Tensor & target, int64_t reduction, at::Tensor & out);
TORCH_API at::Tensor l1_loss_backward(const at::Tensor & grad_output, const at::Tensor & self, const at::Tensor & target, int64_t reduction);
TORCH_API ::std::tuple<at::Tensor,at::Tensor> nll_loss_forward(const at::Tensor & self, const at::Tensor & target, const c10::optional<at::Tensor> & weight, int64_t reduction, int64_t ignore_index);
TORCH_API at::Tensor nll_loss_backward(const at::Tensor & grad_output, const at::Tensor & self, const at::Tensor & target, const c10::optional<at::Tensor> & weight, int64_t reduction, int64_t ignore_index, const at::Tensor & total_weight);
TORCH_API at::Tensor smooth_l1_loss_backward(const at::Tensor & grad_output, const at::Tensor & self, const at::Tensor & target, int64_t reduction, double beta);
TORCH_API at::Tensor huber_loss_backward(const at::Tensor & grad_output, const at::Tensor & self, const at::Tensor & target, int64_t reduction, double delta);
TORCH_API at::Tensor soft_margin_loss(const at::Tensor & self, const at::Tensor & target, int64_t reduction=at::Reduction::Mean);
TORCH_API at::Tensor & soft_margin_loss_out(at::Tensor & out, const at::Tensor & self, const at::Tensor & target, int64_t reduction=at::Reduction::Mean);
TORCH_API at::Tensor & soft_margin_loss_outf(const at::Tensor & self, const at::Tensor & target, int64_t reduction, at::Tensor & out);
TORCH_API at::Tensor soft_margin_loss_backward(const at::Tensor & grad_output, const at::Tensor & self, const at::Tensor & target, int64_t reduction);
TORCH_API at::Tensor & soft_margin_loss_backward_out(at::Tensor & grad_input, const at::Tensor & grad_output, const at::Tensor & self, const at::Tensor & target, int64_t reduction);
TORCH_API at::Tensor & soft_margin_loss_backward_outf(const at::Tensor & grad_output, const at::Tensor & self, const at::Tensor & target, int64_t reduction, at::Tensor & grad_input);
TORCH_API at::Tensor elu(const at::Tensor & self, const at::Scalar & alpha=1, const at::Scalar & scale=1, const at::Scalar & input_scale=1);
TORCH_API at::Tensor & elu_(at::Tensor & self, const at::Scalar & alpha=1, const at::Scalar & scale=1, const at::Scalar & input_scale=1);
TORCH_API at::Tensor elu_backward(const at::Tensor & grad_output, const at::Scalar & alpha, const at::Scalar & scale, const at::Scalar & input_scale, bool is_result, const at::Tensor & self_or_result);
TORCH_API at::Tensor glu(const at::Tensor & self, int64_t dim=-1);
TORCH_API at::Tensor hardsigmoid(const at::Tensor & self);
TORCH_API at::Tensor & hardsigmoid_(at::Tensor & self);
TORCH_API at::Tensor hardsigmoid_backward(const at::Tensor & grad_output, const at::Tensor & self);
TORCH_API at::Tensor leaky_relu(const at::Tensor & self, const at::Scalar & negative_slope=0.01);
TORCH_API at::Tensor & leaky_relu_(at::Tensor & self, const at::Scalar & negative_slope=0.01);
TORCH_API at::Tensor leaky_relu_backward(const at::Tensor & grad_output, const at::Tensor & self, const at::Scalar & negative_slope, bool self_is_result);
TORCH_API at::Tensor rrelu_with_noise_backward(const at::Tensor & grad_output, const at::Tensor & self, const at::Tensor & noise, const at::Scalar & lower, const at::Scalar & upper, bool training, bool self_is_result);
TORCH_API at::Tensor softplus(const at::Tensor & self, const at::Scalar & beta=1, const at::Scalar & threshold=20);
TORCH_API at::Tensor softplus_backward(const at::Tensor & grad_output, const at::Tensor & self, const at::Scalar & beta, const at::Scalar & threshold, const at::Tensor & output);
TORCH_API at::Tensor softshrink(const at::Tensor & self, const at::Scalar & lambd=0.5);
TORCH_API at::Tensor softshrink_backward(const at::Tensor & grad_output, const at::Tensor & self, const at::Scalar & lambd);
TORCH_API ::std::tuple<at::Tensor,at::Tensor> adaptive_max_pool2d(const at::Tensor & self, at::IntArrayRef output_size);
TORCH_API at::Tensor adaptive_max_pool2d_backward(const at::Tensor & grad_output, const at::Tensor & self, const at::Tensor & indices);
TORCH_API ::std::tuple<at::Tensor,at::Tensor> adaptive_max_pool3d(const at::Tensor & self, at::IntArrayRef output_size);
TORCH_API at::Tensor adaptive_max_pool3d_backward(const at::Tensor & grad_output, const at::Tensor & self, const at::Tensor & indices);
TORCH_API at::Tensor avg_pool2d(const at::Tensor & self, at::IntArrayRef kernel_size, at::IntArrayRef stride={}, at::IntArrayRef padding=0, bool ceil_mode=false, bool count_include_pad=true, c10::optional<int64_t> divisor_override=c10::nullopt);
TORCH_API at::Tensor avg_pool2d_backward(const at::Tensor & grad_output, const at::Tensor & self, at::IntArrayRef kernel_size, at::IntArrayRef stride, at::IntArrayRef padding, bool ceil_mode, bool count_include_pad, c10::optional<int64_t> divisor_override);
TORCH_API at::Tensor avg_pool3d(const at::Tensor & self, at::IntArrayRef kernel_size, at::IntArrayRef stride={}, at::IntArrayRef padding=0, bool ceil_mode=false, bool count_include_pad=true, c10::optional<int64_t> divisor_override=c10::nullopt);
TORCH_API at::Tensor avg_pool3d_backward(const at::Tensor & grad_output, const at::Tensor & self, at::IntArrayRef kernel_size, at::IntArrayRef stride, at::IntArrayRef padding, bool ceil_mode, bool count_include_pad, c10::optional<int64_t> divisor_override);
TORCH_API ::std::tuple<at::Tensor,at::Tensor> fractional_max_pool2d(const at::Tensor & self, at::IntArrayRef kernel_size, at::IntArrayRef output_size, const at::Tensor & random_samples);
TORCH_API ::std::tuple<at::Tensor,at::Tensor> max_pool2d_with_indices(const at::Tensor & self, at::IntArrayRef kernel_size, at::IntArrayRef stride={}, at::IntArrayRef padding=0, at::IntArrayRef dilation=1, bool ceil_mode=false);
TORCH_API at::Tensor max_pool2d_with_indices_backward(const at::Tensor & grad_output, const at::Tensor & self, at::IntArrayRef kernel_size, at::IntArrayRef stride, at::IntArrayRef padding, at::IntArrayRef dilation, bool ceil_mode, const at::Tensor & indices);
TORCH_API at::Tensor reflection_pad1d(const at::Tensor & self, at::IntArrayRef padding);
TORCH_API at::Tensor reflection_pad1d_backward(const at::Tensor & grad_output, const at::Tensor & self, at::IntArrayRef padding);
TORCH_API at::Tensor reflection_pad3d(const at::Tensor & self, at::IntArrayRef padding);
TORCH_API at::Tensor reflection_pad3d_backward(const at::Tensor & grad_output, const at::Tensor & self, at::IntArrayRef padding);
TORCH_API at::Tensor replication_pad1d(const at::Tensor & self, at::IntArrayRef padding);
TORCH_API at::Tensor replication_pad1d_backward(const at::Tensor & grad_output, const at::Tensor & self, at::IntArrayRef padding);
TORCH_API at::Tensor replication_pad2d(const at::Tensor & self, at::IntArrayRef padding);
TORCH_API at::Tensor replication_pad3d(const at::Tensor & self, at::IntArrayRef padding);
TORCH_API at::Tensor upsample_linear1d(const at::Tensor & input, c10::optional<at::IntArrayRef> output_size, bool align_corners, c10::optional<at::ArrayRef<double>> scale_factors);
TORCH_API at::Tensor upsample_linear1d_backward(const at::Tensor & grad_output, c10::optional<at::IntArrayRef> output_size, at::IntArrayRef input_size, bool align_corners, c10::optional<at::ArrayRef<double>> scale_factors);
TORCH_API at::Tensor upsample_bilinear2d(const at::Tensor & input, c10::optional<at::IntArrayRef> output_size, bool align_corners, c10::optional<at::ArrayRef<double>> scale_factors);
TORCH_API at::Tensor upsample_bilinear2d_backward(const at::Tensor & grad_output, c10::optional<at::IntArrayRef> output_size, at::IntArrayRef input_size, bool align_corners, c10::optional<at::ArrayRef<double>> scale_factors);
TORCH_API at::Tensor upsample_trilinear3d(const at::Tensor & input, c10::optional<at::IntArrayRef> output_size, bool align_corners, c10::optional<at::ArrayRef<double>> scale_factors);
TORCH_API at::Tensor upsample_trilinear3d_backward(const at::Tensor & grad_output, c10::optional<at::IntArrayRef> output_size, at::IntArrayRef input_size, bool align_corners, c10::optional<at::ArrayRef<double>> scale_factors);
TORCH_API at::Tensor upsample_bicubic2d(const at::Tensor & input, c10::optional<at::IntArrayRef> output_size, bool align_corners, c10::optional<at::ArrayRef<double>> scale_factors);
TORCH_API at::Tensor upsample_bicubic2d_backward(const at::Tensor & grad_output, c10::optional<at::IntArrayRef> output_size, at::IntArrayRef input_size, bool align_corners, c10::optional<at::ArrayRef<double>> scale_factors);
TORCH_API at::Tensor upsample_nearest1d(const at::Tensor & input, c10::optional<at::IntArrayRef> output_size, c10::optional<at::ArrayRef<double>> scale_factors);
TORCH_API at::Tensor upsample_nearest1d_backward(const at::Tensor & grad_output, c10::optional<at::IntArrayRef> output_size, at::IntArrayRef input_size, c10::optional<at::ArrayRef<double>> scale_factors);
TORCH_API at::Tensor upsample_nearest2d(const at::Tensor & input, c10::optional<at::IntArrayRef> output_size, c10::optional<at::ArrayRef<double>> scale_factors);
TORCH_API at::Tensor upsample_nearest2d_backward(const at::Tensor & grad_output, c10::optional<at::IntArrayRef> output_size, at::IntArrayRef input_size, c10::optional<at::ArrayRef<double>> scale_factors);
TORCH_API at::Tensor upsample_linear1d(const at::Tensor & self, at::IntArrayRef output_size, bool align_corners, c10::optional<double> scales=c10::nullopt);
TORCH_API at::Tensor upsample_linear1d_backward(const at::Tensor & grad_output, at::IntArrayRef output_size, at::IntArrayRef input_size, bool align_corners, c10::optional<double> scales=c10::nullopt);
TORCH_API at::Tensor upsample_bilinear2d(const at::Tensor & self, at::IntArrayRef output_size, bool align_corners, c10::optional<double> scales_h=c10::nullopt, c10::optional<double> scales_w=c10::nullopt);
TORCH_API at::Tensor upsample_bilinear2d_backward(const at::Tensor & grad_output, at::IntArrayRef output_size, at::IntArrayRef input_size, bool align_corners, c10::optional<double> scales_h=c10::nullopt, c10::optional<double> scales_w=c10::nullopt);
TORCH_API at::Tensor upsample_bicubic2d(const at::Tensor & self, at::IntArrayRef output_size, bool align_corners, c10::optional<double> scales_h=c10::nullopt, c10::optional<double> scales_w=c10::nullopt);
TORCH_API at::Tensor upsample_bicubic2d_backward(const at::Tensor & grad_output, at::IntArrayRef output_size, at::IntArrayRef input_size, bool align_corners, c10::optional<double> scales_h=c10::nullopt, c10::optional<double> scales_w=c10::nullopt);
TORCH_API at::Tensor upsample_trilinear3d(const at::Tensor & self, at::IntArrayRef output_size, bool align_corners, c10::optional<double> scales_d=c10::nullopt, c10::optional<double> scales_h=c10::nullopt, c10::optional<double> scales_w=c10::nullopt);
TORCH_API at::Tensor upsample_trilinear3d_backward(const at::Tensor & grad_output, at::IntArrayRef output_size, at::IntArrayRef input_size, bool align_corners, c10::optional<double> scales_d=c10::nullopt, c10::optional<double> scales_h=c10::nullopt, c10::optional<double> scales_w=c10::nullopt);
TORCH_API at::Tensor upsample_nearest1d(const at::Tensor & self, at::IntArrayRef output_size, c10::optional<double> scales=c10::nullopt);
TORCH_API at::Tensor upsample_nearest1d_backward(const at::Tensor & grad_output, at::IntArrayRef output_size, at::IntArrayRef input_size, c10::optional<double> scales=c10::nullopt);
TORCH_API at::Tensor upsample_nearest2d(const at::Tensor & self, at::IntArrayRef output_size, c10::optional<double> scales_h=c10::nullopt, c10::optional<double> scales_w=c10::nullopt);
TORCH_API at::Tensor upsample_nearest2d_backward(const at::Tensor & grad_output, at::IntArrayRef output_size, at::IntArrayRef input_size, c10::optional<double> scales_h=c10::nullopt, c10::optional<double> scales_w=c10::nullopt);
TORCH_API at::Tensor upsample_nearest3d(const at::Tensor & self, at::IntArrayRef output_size, c10::optional<double> scales_d=c10::nullopt, c10::optional<double> scales_h=c10::nullopt, c10::optional<double> scales_w=c10::nullopt);
TORCH_API at::Tensor upsample_nearest3d_backward(const at::Tensor & grad_output, at::IntArrayRef output_size, at::IntArrayRef input_size, c10::optional<double> scales_d=c10::nullopt, c10::optional<double> scales_h=c10::nullopt, c10::optional<double> scales_w=c10::nullopt);
TORCH_API at::Tensor sigmoid_backward(const at::Tensor & grad_output, const at::Tensor & output);
TORCH_API at::Tensor logit_backward(const at::Tensor & grad_output, const at::Tensor & self, c10::optional<double> eps=c10::nullopt);
TORCH_API at::Tensor tanh_backward(const at::Tensor & grad_output, const at::Tensor & output);
TORCH_API at::Tensor slow_conv_transpose2d(const at::Tensor & self, const at::Tensor & weight, at::IntArrayRef kernel_size, const c10::optional<at::Tensor> & bias={}, at::IntArrayRef stride=1, at::IntArrayRef padding=0, at::IntArrayRef output_padding=0, at::IntArrayRef dilation=1);
TORCH_API at::Tensor isposinf(const at::Tensor & self);
TORCH_API at::Tensor isneginf(const at::Tensor & self);
TORCH_API at::Tensor special_entr(const at::Tensor & self);
TORCH_API at::Tensor special_ndtri(const at::Tensor & self);
TORCH_API at::Tensor special_erfcx(const at::Tensor & self);
TORCH_API at::Tensor special_xlog1py(const at::Tensor & self, const at::Tensor & other);
TORCH_API at::Tensor special_xlog1py(const at::Scalar & self, const at::Tensor & other);
TORCH_API at::Tensor & special_xlog1py_out(at::Tensor & out, const at::Scalar & self, const at::Tensor & other);
TORCH_API at::Tensor & special_xlog1py_outf(const at::Scalar & self, const at::Tensor & other, at::Tensor & out);
TORCH_API at::Tensor special_xlog1py(const at::Tensor & self, const at::Scalar & other);
TORCH_API at::Tensor & special_xlog1py_out(at::Tensor & out, const at::Tensor & self, const at::Scalar & other);
TORCH_API at::Tensor & special_xlog1py_outf(const at::Tensor & self, const at::Scalar & other, at::Tensor & out);
TORCH_API at::Tensor special_zeta(const at::Tensor & self, const at::Tensor & other);
TORCH_API at::Tensor special_zeta(const at::Scalar & self, const at::Tensor & other);
TORCH_API at::Tensor & special_zeta_out(at::Tensor & out, const at::Scalar & self, const at::Tensor & other);
TORCH_API at::Tensor & special_zeta_outf(const at::Scalar & self, const at::Tensor & other, at::Tensor & out);
TORCH_API at::Tensor special_zeta(const at::Tensor & self, const at::Scalar & other);
TORCH_API at::Tensor & special_zeta_out(at::Tensor & out, const at::Tensor & self, const at::Scalar & other);
TORCH_API at::Tensor & special_zeta_outf(const at::Tensor & self, const at::Scalar & other, at::Tensor & out);
TORCH_API at::Tensor special_i0e(const at::Tensor & self);
TORCH_API at::Tensor special_i1(const at::Tensor & self);
TORCH_API at::Tensor special_i1e(const at::Tensor & self);
TORCH_API ::std::tuple<at::Tensor,at::Tensor,at::Tensor,at::Tensor> linalg_lstsq(const at::Tensor & self, const at::Tensor & b, c10::optional<double> rcond=c10::nullopt, c10::optional<c10::string_view> driver=c10::nullopt);
TORCH_API ::std::tuple<at::Tensor,at::Tensor> linalg_inv_ex(const at::Tensor & self, bool check_errors=false);
TORCH_API ::std::tuple<at::Tensor &,at::Tensor &> linalg_inv_ex_out(at::Tensor & inverse, at::Tensor & info, const at::Tensor & self, bool check_errors=false);
TORCH_API ::std::tuple<at::Tensor &,at::Tensor &> linalg_inv_ex_outf(const at::Tensor & self, bool check_errors, at::Tensor & inverse, at::Tensor & info);
TORCH_API ::std::tuple<at::Tensor,at::Tensor> linalg_qr(const at::Tensor & self, c10::string_view mode="reduced");
TORCH_API ::std::tuple<at::Tensor &,at::Tensor &> linalg_qr_out(at::Tensor & Q, at::Tensor & R, const at::Tensor & self, c10::string_view mode="reduced");
TORCH_API ::std::tuple<at::Tensor &,at::Tensor &> linalg_qr_outf(const at::Tensor & self, c10::string_view mode, at::Tensor & Q, at::Tensor & R);

} // namespace compositeexplicitautograd
} // namespace at
